// app.js
const PerformanceUtils = require('./utils/performance');
const ErrorHandler = require('./utils/error-handler');

App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    openid: null,
    systemInfo: null,
    performanceUtils: PerformanceUtils,
    errorHandler: ErrorHandler
  },

  onLaunch() {
    console.log('心安AI小程序启动');

    // 初始化性能监控
    PerformanceUtils.monitor.start('app_launch');

    // 初始化错误处理
    ErrorHandler.setConfig({
      enableConsoleLog: true,
      enableRemoteLog: true,
      enableUserFeedback: true
    });

    ErrorHandler.info('小程序启动', { version: '1.0.0' });

    // 初始化云开发
    this.initCloud();

    // 获取系统信息
    this.getSystemInfo();

    // 检查登录状态
    this.checkLoginStatus();

    // 检查更新
    this.checkForUpdate();

    // 预加载关键数据
    this.preloadCriticalData();

    PerformanceUtils.monitor.end('app_launch');
  },

  onShow() {
    console.log('小程序显示');
    ErrorHandler.debug('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
    ErrorHandler.debug('小程序隐藏');

    // 清理内存
    PerformanceUtils.memory.cleanup();
  },

  onError(msg) {
    console.error('小程序错误:', msg);
    ErrorHandler.handleError(msg, { source: 'app.onError' });
  },

  // 初始化云开发
  initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }

    try {
      wx.cloud.init({
        env: 'xinan-ai-prod', // 请替换为你的实际云开发环境ID
        traceUser: true
      });

      console.log('云开发初始化成功');
    } catch (error) {
      console.error('云开发初始化失败:', error);
      wx.showToast({
        title: '云开发环境未配置',
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      },
      fail: (err) => {
        console.error('获取系统信息失败:', err);
      }
    });
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const openid = wx.getStorageSync('openid');
    
    if (userInfo && openid) {
      this.globalData.userInfo = userInfo;
      this.globalData.openid = openid;
      this.globalData.isLoggedIn = true;
      console.log('用户已登录:', userInfo);
    } else {
      console.log('用户未登录');
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate);
        ErrorHandler.info('检查更新', { hasUpdate: res.hasUpdate });
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
        ErrorHandler.error('新版本下载失败');
      });
    }
  },

  // 预加载关键数据
  async preloadCriticalData() {
    try {
      ErrorHandler.debug('开始预加载关键数据');

      // 预加载用户统计数据
      if (this.globalData.isLoggedIn) {
        PerformanceUtils.dataPreloader.preload('userStats', async () => {
          const res = await wx.cloud.callFunction({
            name: 'user',
            data: { action: 'getStats' }
          });
          return res.result.data;
        });
      }

      // 预加载图片资源
      const criticalImages = [
        '/images/icons/add.png',
        '/images/icons/check-filled.png',
        '/images/icons/check-empty.png',
        '/images/icons/microphone.png'
      ];

      PerformanceUtils.imageOptimization.preloadImages(criticalImages);

      ErrorHandler.debug('关键数据预加载完成');
    } catch (error) {
      ErrorHandler.error('预加载关键数据失败', error);
    }
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      PerformanceUtils.monitor.start('user_login');

      wx.login({
        success: (res) => {
          if (res.code) {
            // 使用请求队列管理登录请求
            PerformanceUtils.requestQueue.add(async () => {
              return wx.cloud.callFunction({
                name: 'auth',
                data: {
                  action: 'login',
                  code: res.code
                }
              });
            }, 10) // 高优先级
            .then((cloudRes) => {
              if (cloudRes.result.code === 0) {
                const { openid, userInfo } = cloudRes.result.data;

                // 保存用户信息
                this.globalData.openid = openid;
                this.globalData.userInfo = userInfo;
                this.globalData.isLoggedIn = true;

                // 本地存储
                wx.setStorageSync('openid', openid);
                wx.setStorageSync('userInfo', userInfo);

                console.log('登录成功:', userInfo);
                ErrorHandler.info('用户登录成功', { openid });

                PerformanceUtils.monitor.end('user_login');
                resolve(cloudRes.result.data);
              } else {
                const error = ErrorHandler.createError(
                  ErrorHandler.ErrorTypes.API_ERROR,
                  cloudRes.result.message,
                  { code: cloudRes.result.code }
                );
                ErrorHandler.handleError(error);
                reject(error);
              }
            })
            .catch((err) => {
              const error = ErrorHandler.createError(
                ErrorHandler.ErrorTypes.NETWORK_ERROR,
                '登录请求失败',
                err
              );
              ErrorHandler.handleError(error);
              reject(error);
            });
          } else {
            const error = ErrorHandler.createError(
              ErrorHandler.ErrorTypes.SYSTEM_ERROR,
              '获取登录凭证失败',
              { errMsg: res.errMsg }
            );
            ErrorHandler.handleError(error);
            reject(error);
          }
        },
        fail: (err) => {
          const error = ErrorHandler.createError(
            ErrorHandler.ErrorTypes.SYSTEM_ERROR,
            'wx.login失败',
            err
          );
          ErrorHandler.handleError(error);
          reject(error);
        }
      });
    });
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          const userInfo = res.userInfo;
          
          // 更新用户信息到云端
          wx.cloud.callFunction({
            name: 'auth',
            data: {
              action: 'updateProfile',
              userInfo: userInfo
            },
            success: (cloudRes) => {
              if (cloudRes.result.code === 0) {
                // 更新本地用户信息
                this.globalData.userInfo = {
                  ...this.globalData.userInfo,
                  ...userInfo
                };
                
                wx.setStorageSync('userInfo', this.globalData.userInfo);
                console.log('用户信息更新成功');
                resolve(this.globalData.userInfo);
              } else {
                reject(new Error(cloudRes.result.message));
              }
            },
            fail: reject
          });
        },
        fail: reject
      });
    });
  },

  // 退出登录
  logout() {
    this.globalData.userInfo = null;
    this.globalData.isLoggedIn = false;
    this.globalData.openid = null;
    
    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('openid');
    wx.removeStorageSync('anxiety_draft');
    
    console.log('用户已退出登录');
  },

  // 显示错误提示
  showError(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: duration
    });
  },

  // 显示成功提示
  showSuccess(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: duration
    });
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading();
  },

  // 格式化日期
  formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');
    const second = String(d.getSeconds()).padStart(2, '0');
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second);
  },

  // 防抖函数（使用性能优化版本）
  debounce(func, wait, immediate = false) {
    return PerformanceUtils.debounce(func, wait, immediate);
  },

  // 节流函数（使用性能优化版本）
  throttle(func, limit) {
    return PerformanceUtils.throttle(func, limit);
  },

  // 网络请求重试
  async retryRequest(requestFn, maxRetries = 3) {
    return PerformanceUtils.network.retryRequest(requestFn, maxRetries);
  },

  // 检查网络状态
  async checkNetworkStatus() {
    return PerformanceUtils.network.checkNetworkStatus();
  },

  // 缓存数据
  setCache(key, value, ttl) {
    return PerformanceUtils.cache.set(key, value, ttl);
  },

  // 获取缓存
  getCache(key) {
    return PerformanceUtils.cache.get(key);
  },

  // 性能监控
  startMonitor(name) {
    return PerformanceUtils.monitor.start(name);
  },

  endMonitor(name) {
    return PerformanceUtils.monitor.end(name);
  },

  // 错误处理
  handleError(error, context) {
    return ErrorHandler.handleError(error, context);
  },

  // 记录日志
  log(level, message, data) {
    return ErrorHandler.log(level, message, data);
  }
});

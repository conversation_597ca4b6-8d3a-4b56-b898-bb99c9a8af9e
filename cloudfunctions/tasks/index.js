// 任务管理云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { action, ...data } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    switch (action) {
      case 'getList':
        return await getTaskList(data, wxContext);
      case 'getDetail':
        return await getTaskDetail(data, wxContext);
      case 'create':
        return await createTask(data, wxContext);
      case 'createFromAnalysis':
        return await createTasksFromAnalysis(data, wxContext);
      case 'update':
        return await updateTask(data, wxContext);
      case 'updateStatus':
        return await updateTaskStatus(data, wxContext);
      case 'delete':
        return await deleteTask(data, wxContext);
      case 'getStats':
        return await getTaskStats(data, wxContext);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

// 获取任务列表
async function getTaskList(data, wxContext) {
  const { OPENID } = wxContext;
  const { 
    status = 'all', 
    category = 'all',
    priority = 'all',
    page = 1, 
    limit = 20,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = data;
  
  try {
    const tasksCollection = db.collection('tasks');
    let query = tasksCollection.where({
      userId: OPENID
    });
    
    // 状态筛选
    if (status !== 'all') {
      query = query.where({
        status: status
      });
    }
    
    // 分类筛选
    if (category !== 'all') {
      query = query.where({
        category: category
      });
    }
    
    // 优先级筛选
    if (priority !== 'all') {
      query = query.where({
        priority: priority
      });
    }
    
    // 排序
    const sortDirection = sortOrder === 'desc' ? 'desc' : 'asc';
    query = query.orderBy(sortBy, sortDirection);
    
    // 分页
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    
    const result = await query.get();
    
    // 获取总数
    const countResult = await tasksCollection.where({
      userId: OPENID,
      ...(status !== 'all' && { status }),
      ...(category !== 'all' && { category }),
      ...(priority !== 'all' && { priority })
    }).count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        tasks: result.data,
        pagination: {
          page: page,
          limit: limit,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / limit)
        }
      }
    };
    
  } catch (error) {
    console.error('获取任务列表失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取任务详情
async function getTaskDetail(data, wxContext) {
  const { OPENID } = wxContext;
  const { taskId } = data;
  
  try {
    const tasksCollection = db.collection('tasks');
    const taskResult = await tasksCollection.doc(taskId).get();
    
    if (!taskResult.data || taskResult.data.userId !== OPENID) {
      return { code: 1003, message: '任务不存在或无权限访问' };
    }
    
    return {
      code: 0,
      message: '获取成功',
      data: taskResult.data
    };
    
  } catch (error) {
    console.error('获取任务详情失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 创建任务
async function createTask(data, wxContext) {
  const { OPENID } = wxContext;
  const {
    title,
    description = '',
    priority = 'medium',
    category = '其他',
    estimatedTime = 30,
    dueDate,
    tags = []
  } = data;
  
  // 验证必填字段
  if (!title || title.trim().length === 0) {
    return { code: 1001, message: '任务标题不能为空' };
  }
  
  try {
    const tasksCollection = db.collection('tasks');
    
    const task = {
      userId: OPENID,
      title: title.trim(),
      description: description.trim(),
      priority: priority,
      category: category,
      estimatedTime: estimatedTime,
      dueDate: dueDate ? new Date(dueDate) : null,
      status: 'pending',
      progress: 0,
      tags: tags,
      reminder: {
        enabled: false,
        time: null,
        sent: false
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await tasksCollection.add({
      data: task
    });
    
    // 更新用户统计
    await updateUserTaskStats(OPENID, 'create');
    
    return {
      code: 0,
      message: '创建成功',
      data: {
        taskId: result._id,
        task: task
      }
    };
    
  } catch (error) {
    console.error('创建任务失败:', error);
    return { code: 3001, message: '创建失败' };
  }
}

// 从分析结果创建任务
async function createTasksFromAnalysis(data, wxContext) {
  const { OPENID } = wxContext;
  const { recordId, tasks } = data;
  
  if (!tasks || tasks.length === 0) {
    return { code: 1001, message: '没有要创建的任务' };
  }
  
  try {
    const tasksCollection = db.collection('tasks');
    const createdTasks = [];
    
    for (const taskData of tasks) {
      const task = {
        userId: OPENID,
        anxietyRecordId: recordId,
        title: taskData.title,
        description: taskData.description,
        priority: taskData.priority || 'medium',
        category: '焦虑管理',
        estimatedTime: taskData.estimatedTime || 30,
        dueDate: taskData.suggestedTime ? new Date(taskData.suggestedTime) : null,
        status: 'pending',
        progress: 0,
        tags: ['AI生成'],
        reminder: {
          enabled: true,
          time: taskData.suggestedTime ? new Date(taskData.suggestedTime) : null,
          sent: false
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await tasksCollection.add({
        data: task
      });
      
      createdTasks.push({
        ...task,
        _id: result._id
      });
    }
    
    // 更新用户统计
    await updateUserTaskStats(OPENID, 'create', createdTasks.length);
    
    return {
      code: 0,
      message: '任务创建成功',
      data: {
        count: createdTasks.length,
        tasks: createdTasks
      }
    };
    
  } catch (error) {
    console.error('从分析结果创建任务失败:', error);
    return { code: 3001, message: '创建失败' };
  }
}

// 更新任务
async function updateTask(data, wxContext) {
  const { OPENID } = wxContext;
  const { taskId, ...updateData } = data;
  
  try {
    const tasksCollection = db.collection('tasks');
    
    // 验证任务所有权
    const taskResult = await tasksCollection.doc(taskId).get();
    if (!taskResult.data || taskResult.data.userId !== OPENID) {
      return { code: 1003, message: '任务不存在或无权限访问' };
    }
    
    // 准备更新数据
    const updateFields = {
      updatedAt: new Date()
    };
    
    // 只更新允许的字段
    const allowedFields = ['title', 'description', 'priority', 'category', 'estimatedTime', 'dueDate', 'tags'];
    allowedFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (field === 'dueDate' && updateData[field]) {
          updateFields[field] = new Date(updateData[field]);
        } else {
          updateFields[field] = updateData[field];
        }
      }
    });
    
    await tasksCollection.doc(taskId).update({
      data: updateFields
    });
    
    // 记录任务日志
    await logTaskAction(taskId, OPENID, 'update', updateFields);
    
    return {
      code: 0,
      message: '更新成功',
      data: updateFields
    };
    
  } catch (error) {
    console.error('更新任务失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}

// 更新任务状态
async function updateTaskStatus(data, wxContext) {
  const { OPENID } = wxContext;
  const { taskId, status, note = '', actualTime } = data;
  
  try {
    const tasksCollection = db.collection('tasks');
    
    // 验证任务所有权
    const taskResult = await tasksCollection.doc(taskId).get();
    if (!taskResult.data || taskResult.data.userId !== OPENID) {
      return { code: 1003, message: '任务不存在或无权限访问' };
    }
    
    const oldStatus = taskResult.data.status;
    const updateData = {
      status: status,
      updatedAt: new Date()
    };
    
    // 如果是完成任务
    if (status === 'completed') {
      updateData.completion = {
        completedAt: new Date(),
        actualTime: actualTime || taskResult.data.estimatedTime,
        note: note
      };
      updateData.progress = 100;
      
      // 更新用户统计
      if (oldStatus !== 'completed') {
        await updateUserTaskStats(OPENID, 'complete');
      }
    } else if (oldStatus === 'completed' && status !== 'completed') {
      // 如果从完成状态改为其他状态，减少完成数量
      await updateUserTaskStats(OPENID, 'uncomplete');
    }
    
    await tasksCollection.doc(taskId).update({
      data: updateData
    });
    
    // 记录任务日志
    await logTaskAction(taskId, OPENID, 'status_change', {
      oldStatus: oldStatus,
      newStatus: status,
      note: note
    });
    
    return {
      code: 0,
      message: '状态更新成功',
      data: updateData
    };
    
  } catch (error) {
    console.error('更新任务状态失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}

// 删除任务
async function deleteTask(data, wxContext) {
  const { OPENID } = wxContext;
  const { taskId } = data;
  
  try {
    const tasksCollection = db.collection('tasks');
    
    // 验证任务所有权
    const taskResult = await tasksCollection.doc(taskId).get();
    if (!taskResult.data || taskResult.data.userId !== OPENID) {
      return { code: 1003, message: '任务不存在或无权限访问' };
    }
    
    await tasksCollection.doc(taskId).remove();
    
    // 记录任务日志
    await logTaskAction(taskId, OPENID, 'delete', {
      deletedTask: taskResult.data
    });
    
    return {
      code: 0,
      message: '删除成功'
    };
    
  } catch (error) {
    console.error('删除任务失败:', error);
    return { code: 3001, message: '删除失败' };
  }
}

// 获取任务统计
async function getTaskStats(data, wxContext) {
  const { OPENID } = wxContext;
  
  try {
    const tasksCollection = db.collection('tasks');
    
    // 获取各种状态的任务数量
    const [totalResult, pendingResult, completedResult, todayResult] = await Promise.all([
      tasksCollection.where({ userId: OPENID }).count(),
      tasksCollection.where({ userId: OPENID, status: 'pending' }).count(),
      tasksCollection.where({ userId: OPENID, status: 'completed' }).count(),
      tasksCollection.where({
        userId: OPENID,
        createdAt: _.gte(new Date(new Date().toDateString()))
      }).count()
    ]);
    
    // 获取今日完成的任务
    const todayCompletedResult = await tasksCollection.where({
      userId: OPENID,
      status: 'completed',
      'completion.completedAt': _.gte(new Date(new Date().toDateString()))
    }).count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        total: totalResult.total,
        pending: pendingResult.total,
        completed: completedResult.total,
        todayCreated: todayResult.total,
        todayCompleted: todayCompletedResult.total
      }
    };
    
  } catch (error) {
    console.error('获取任务统计失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 更新用户任务统计
async function updateUserTaskStats(userId, action, count = 1) {
  try {
    const usersCollection = db.collection('users');
    const updateData = {
      updatedAt: new Date()
    };
    
    if (action === 'create') {
      // 创建任务时不需要更新统计
    } else if (action === 'complete') {
      updateData['statistics.totalTasksCompleted'] = _.inc(count);
    } else if (action === 'uncomplete') {
      updateData['statistics.totalTasksCompleted'] = _.inc(-count);
    }
    
    await usersCollection.where({
      openid: userId
    }).update({
      data: updateData
    });
    
  } catch (error) {
    console.error('更新用户任务统计失败:', error);
  }
}

// 记录任务操作日志
async function logTaskAction(taskId, userId, action, details) {
  try {
    const logsCollection = db.collection('taskLogs');
    
    await logsCollection.add({
      data: {
        taskId: taskId,
        userId: userId,
        action: action,
        details: details,
        metadata: {
          source: 'user_action',
          timestamp: new Date()
        },
        createdAt: new Date()
      }
    });
    
  } catch (error) {
    console.error('记录任务日志失败:', error);
  }
}

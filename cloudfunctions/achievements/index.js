// 成就系统云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { action, ...data } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    switch (action) {
      case 'checkAchievements':
        return await checkUserAchievements(data, wxContext);
      case 'getAchievements':
        return await getUserAchievements(data, wxContext);
      case 'generateCard':
        return await generateAchievementCard(data, wxContext);
      case 'shareCard':
        return await shareAchievementCard(data, wxContext);
      case 'getCardHistory':
        return await getCardHistory(data, wxContext);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

// 检查用户成就
async function checkUserAchievements(data, wxContext) {
  const { OPENID } = wxContext;
  const { triggerType, triggerData } = data;
  
  try {
    // 获取用户统计数据
    const userStats = await getUserStats(OPENID);
    
    // 获取已获得的成就
    const achievementsCollection = db.collection('userAchievements');
    const existingAchievements = await achievementsCollection.where({
      userId: OPENID
    }).get();
    
    const earnedAchievementIds = existingAchievements.data.map(a => a.achievementId);
    
    // 检查所有成就规则
    const newAchievements = [];
    const achievementRules = getAchievementRules();
    
    for (const rule of achievementRules) {
      // 跳过已获得的成就
      if (earnedAchievementIds.includes(rule.id)) {
        continue;
      }
      
      // 检查成就条件
      if (checkAchievementCondition(rule, userStats, triggerType, triggerData)) {
        // 记录新成就
        const achievement = {
          userId: OPENID,
          achievementId: rule.id,
          title: rule.title,
          description: rule.description,
          icon: rule.icon,
          category: rule.category,
          rarity: rule.rarity,
          points: rule.points,
          unlockedAt: new Date(),
          triggerType: triggerType,
          triggerData: triggerData
        };
        
        const result = await achievementsCollection.add({
          data: achievement
        });
        
        achievement._id = result._id;
        newAchievements.push(achievement);
      }
    }
    
    return {
      code: 0,
      message: '成就检查完成',
      data: {
        newAchievements: newAchievements,
        hasNewAchievements: newAchievements.length > 0
      }
    };
    
  } catch (error) {
    console.error('检查用户成就失败:', error);
    return { code: 3001, message: '检查失败' };
  }
}

// 获取用户成就
async function getUserAchievements(data, wxContext) {
  const { OPENID } = wxContext;
  const { category = 'all', page = 1, limit = 20 } = data;
  
  try {
    const achievementsCollection = db.collection('userAchievements');
    let query = achievementsCollection.where({
      userId: OPENID
    });
    
    if (category !== 'all') {
      query = query.where({
        category: category
      });
    }
    
    // 按获得时间倒序
    query = query.orderBy('unlockedAt', 'desc');
    
    // 分页
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    
    const result = await query.get();
    
    // 获取总数
    const countResult = await achievementsCollection.where({
      userId: OPENID,
      ...(category !== 'all' && { category })
    }).count();
    
    // 计算成就统计
    const stats = await getAchievementStats(OPENID);
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        achievements: result.data,
        stats: stats,
        pagination: {
          page: page,
          limit: limit,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / limit)
        }
      }
    };
    
  } catch (error) {
    console.error('获取用户成就失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 生成成就卡片
async function generateAchievementCard(data, wxContext) {
  const { OPENID } = wxContext;
  const { achievementId, template = 'default' } = data;
  
  try {
    // 获取成就信息
    const achievementsCollection = db.collection('userAchievements');
    const achievementResult = await achievementsCollection.where({
      userId: OPENID,
      achievementId: achievementId
    }).get();
    
    if (achievementResult.data.length === 0) {
      return { code: 1003, message: '成就不存在' };
    }
    
    const achievement = achievementResult.data[0];
    
    // 获取用户信息
    const userInfo = await getUserInfo(OPENID);
    
    // 生成卡片数据
    const cardData = {
      achievement: achievement,
      user: {
        nickname: userInfo.profile.nickname,
        avatarUrl: userInfo.profile.avatarUrl
      },
      template: template,
      generatedAt: new Date(),
      shareText: generateShareText(achievement)
    };
    
    // 保存卡片记录
    const cardsCollection = db.collection('achievementCards');
    const cardResult = await cardsCollection.add({
      data: {
        userId: OPENID,
        achievementId: achievementId,
        cardData: cardData,
        template: template,
        createdAt: new Date()
      }
    });
    
    return {
      code: 0,
      message: '卡片生成成功',
      data: {
        cardId: cardResult._id,
        cardData: cardData
      }
    };
    
  } catch (error) {
    console.error('生成成就卡片失败:', error);
    return { code: 3001, message: '生成失败' };
  }
}

// 分享成就卡片
async function shareAchievementCard(data, wxContext) {
  const { OPENID } = wxContext;
  const { cardId, shareType = 'friend' } = data;
  
  try {
    // 记录分享行为
    const sharesCollection = db.collection('cardShares');
    await sharesCollection.add({
      data: {
        userId: OPENID,
        cardId: cardId,
        shareType: shareType,
        sharedAt: new Date()
      }
    });
    
    // 更新卡片分享次数
    const cardsCollection = db.collection('achievementCards');
    await cardsCollection.doc(cardId).update({
      data: {
        shareCount: _.inc(1),
        lastSharedAt: new Date()
      }
    });
    
    return {
      code: 0,
      message: '分享记录成功',
      data: {
        cardId: cardId,
        shareType: shareType
      }
    };
    
  } catch (error) {
    console.error('分享成就卡片失败:', error);
    return { code: 3001, message: '分享失败' };
  }
}

// 获取卡片历史
async function getCardHistory(data, wxContext) {
  const { OPENID } = wxContext;
  const { page = 1, limit = 10 } = data;
  
  try {
    const cardsCollection = db.collection('achievementCards');
    const query = cardsCollection.where({
      userId: OPENID
    }).orderBy('createdAt', 'desc');
    
    // 分页
    const skip = (page - 1) * limit;
    const result = await query.skip(skip).limit(limit).get();
    
    // 获取总数
    const countResult = await cardsCollection.where({
      userId: OPENID
    }).count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        cards: result.data,
        pagination: {
          page: page,
          limit: limit,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / limit)
        }
      }
    };
    
  } catch (error) {
    console.error('获取卡片历史失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取用户统计数据
async function getUserStats(userId) {
  try {
    const usersCollection = db.collection('users');
    const userResult = await usersCollection.where({
      openid: userId
    }).get();
    
    if (userResult.data.length === 0) {
      return {};
    }
    
    const user = userResult.data[0];
    
    // 获取额外统计数据
    const [tasksResult, moodResult, anxietyResult] = await Promise.all([
      db.collection('tasks').where({ userId }).count(),
      db.collection('moodJournals').where({ userId }).count(),
      db.collection('anxietyRecords').where({ userId }).count()
    ]);
    
    return {
      ...user.statistics,
      totalTasks: tasksResult.total,
      totalMoodRecords: moodResult.total,
      totalAnxietyRecords: anxietyResult.total,
      joinDate: user.createdAt
    };
    
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return {};
  }
}

// 获取用户信息
async function getUserInfo(userId) {
  try {
    const usersCollection = db.collection('users');
    const userResult = await usersCollection.where({
      openid: userId
    }).get();
    
    return userResult.data[0] || {};
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {};
  }
}

// 获取成就统计
async function getAchievementStats(userId) {
  try {
    const achievementsCollection = db.collection('userAchievements');
    
    // 按分类统计
    const categoryStats = {};
    const rarityStats = {};
    let totalPoints = 0;
    
    const achievements = await achievementsCollection.where({
      userId: userId
    }).get();
    
    achievements.data.forEach(achievement => {
      // 分类统计
      categoryStats[achievement.category] = (categoryStats[achievement.category] || 0) + 1;
      
      // 稀有度统计
      rarityStats[achievement.rarity] = (rarityStats[achievement.rarity] || 0) + 1;
      
      // 总积分
      totalPoints += achievement.points || 0;
    });
    
    return {
      total: achievements.data.length,
      totalPoints: totalPoints,
      categoryStats: categoryStats,
      rarityStats: rarityStats
    };
    
  } catch (error) {
    console.error('获取成就统计失败:', error);
    return {};
  }
}

// 成就规则定义
function getAchievementRules() {
  return [
    // 任务相关成就
    {
      id: 'first_task_completed',
      title: '初次尝试',
      description: '完成第一个任务',
      icon: '🎯',
      category: 'task',
      rarity: 'common',
      points: 10,
      condition: (stats) => stats.totalTasksCompleted >= 1
    },
    {
      id: 'task_master_10',
      title: '行动达人',
      description: '累计完成10个任务',
      icon: '⭐',
      category: 'task',
      rarity: 'uncommon',
      points: 50,
      condition: (stats) => stats.totalTasksCompleted >= 10
    },
    {
      id: 'task_master_50',
      title: '执行专家',
      description: '累计完成50个任务',
      icon: '🏆',
      category: 'task',
      rarity: 'rare',
      points: 200,
      condition: (stats) => stats.totalTasksCompleted >= 50
    },
    
    // 情绪记录成就
    {
      id: 'first_mood_record',
      title: '情绪觉察',
      description: '记录第一次心情',
      icon: '💝',
      category: 'mood',
      rarity: 'common',
      points: 10,
      condition: (stats) => stats.totalMoodRecords >= 1
    },
    {
      id: 'mood_streak_7',
      title: '坚持记录',
      description: '连续7天记录心情',
      icon: '📝',
      category: 'mood',
      rarity: 'uncommon',
      points: 30,
      condition: (stats) => stats.streakDays >= 7
    },
    {
      id: 'mood_master_30',
      title: '情绪管理师',
      description: '累计记录30次心情',
      icon: '🌈',
      category: 'mood',
      rarity: 'rare',
      points: 100,
      condition: (stats) => stats.totalMoodRecords >= 30
    },
    
    // 焦虑管理成就
    {
      id: 'first_anxiety_dump',
      title: '勇敢表达',
      description: '第一次倾诉焦虑',
      icon: '🗣️',
      category: 'anxiety',
      rarity: 'common',
      points: 15,
      condition: (stats) => stats.totalAnxietyRecords >= 1
    },
    {
      id: 'anxiety_warrior',
      title: '焦虑战士',
      description: '累计倾诉10次焦虑',
      icon: '⚔️',
      category: 'anxiety',
      rarity: 'uncommon',
      points: 75,
      condition: (stats) => stats.totalAnxietyRecords >= 10
    },
    
    // 时间相关成就
    {
      id: 'early_adopter',
      title: '早期用户',
      description: '注册使用心安AI',
      icon: '🌟',
      category: 'special',
      rarity: 'common',
      points: 20,
      condition: (stats) => !!stats.joinDate
    },
    {
      id: 'loyal_user_30',
      title: '忠实用户',
      description: '使用心安AI超过30天',
      icon: '💎',
      category: 'special',
      rarity: 'rare',
      points: 150,
      condition: (stats) => {
        if (!stats.joinDate) return false;
        const daysDiff = Math.floor((new Date() - new Date(stats.joinDate)) / (1000 * 60 * 60 * 24));
        return daysDiff >= 30;
      }
    }
  ];
}

// 检查成就条件
function checkAchievementCondition(rule, userStats, triggerType, triggerData) {
  return rule.condition(userStats, triggerType, triggerData);
}

// 生成分享文案
function generateShareText(achievement) {
  const templates = [
    `我在心安AI获得了「${achievement.title}」成就！${achievement.description} 💪`,
    `解锁新成就：${achievement.title}！${achievement.description} 🎉`,
    `又一个里程碑！获得「${achievement.title}」成就 ✨`,
    `成就达成：${achievement.title}！继续加油 🚀`
  ];
  
  const randomIndex = Math.floor(Math.random() * templates.length);
  return templates[randomIndex];
}

// 情绪日记云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { action, ...data } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    switch (action) {
      case 'createJournal':
        return await createMoodJournal(data, wxContext);
      case 'updateJournal':
        return await updateMoodJournal(data, wxContext);
      case 'getJournal':
        return await getMoodJournal(data, wxContext);
      case 'getJournalList':
        return await getMoodJournalList(data, wxContext);
      case 'deleteJournal':
        return await deleteMoodJournal(data, wxContext);
      case 'quickRecord':
        return await quickMoodRecord(data, wxContext);
      case 'getTodayMood':
        return await getTodayMood(data, wxContext);
      case 'getMoodStats':
        return await getMoodStats(data, wxContext);
      case 'getMoodTrend':
        return await getMoodTrend(data, wxContext);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

// 创建情绪日记
async function createMoodJournal(data, wxContext) {
  const { OPENID } = wxContext;
  const {
    recordDate,
    mood,
    note = '',
    activities = [],
    weather = '',
    location = '',
    tags = []
  } = data;
  
  // 验证必填字段
  if (!recordDate || !mood || !mood.primary) {
    return { code: 1001, message: '缺少必要的情绪信息' };
  }
  
  try {
    const moodCollection = db.collection('moodJournals');
    
    // 检查当天是否已有记录
    const existingQuery = await moodCollection.where({
      userId: OPENID,
      recordDate: recordDate
    }).get();
    
    if (existingQuery.data.length > 0) {
      return { code: 1002, message: '今天已经记录过情绪了，可以选择更新' };
    }
    
    const journal = {
      userId: OPENID,
      recordDate: recordDate,
      mood: {
        primary: mood.primary,
        secondary: mood.secondary || [],
        intensity: mood.intensity || 5,
        triggers: mood.triggers || []
      },
      activities: activities.map(activity => ({
        name: activity.name,
        impact: activity.impact || 'neutral',
        duration: activity.duration || 0
      })),
      note: note.trim(),
      weather: weather,
      location: location,
      tags: tags,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await moodCollection.add({
      data: journal
    });
    
    // 更新用户统计
    await updateUserMoodStats(OPENID, 'create');
    
    return {
      code: 0,
      message: '情绪记录创建成功',
      data: {
        journalId: result._id,
        journal: journal
      }
    };
    
  } catch (error) {
    console.error('创建情绪日记失败:', error);
    return { code: 3001, message: '创建失败' };
  }
}

// 更新情绪日记
async function updateMoodJournal(data, wxContext) {
  const { OPENID } = wxContext;
  const { journalId, ...updateData } = data;
  
  try {
    const moodCollection = db.collection('moodJournals');
    
    // 验证日记所有权
    const journalResult = await moodCollection.doc(journalId).get();
    if (!journalResult.data || journalResult.data.userId !== OPENID) {
      return { code: 1003, message: '日记不存在或无权限访问' };
    }
    
    // 准备更新数据
    const updateFields = {
      updatedAt: new Date()
    };
    
    // 只更新允许的字段
    const allowedFields = ['mood', 'note', 'activities', 'weather', 'location', 'tags'];
    allowedFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        updateFields[field] = updateData[field];
      }
    });
    
    await moodCollection.doc(journalId).update({
      data: updateFields
    });
    
    return {
      code: 0,
      message: '情绪记录更新成功',
      data: updateFields
    };
    
  } catch (error) {
    console.error('更新情绪日记失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}

// 获取情绪日记详情
async function getMoodJournal(data, wxContext) {
  const { OPENID } = wxContext;
  const { journalId, recordDate } = data;
  
  try {
    const moodCollection = db.collection('moodJournals');
    let query;
    
    if (journalId) {
      query = moodCollection.doc(journalId);
    } else if (recordDate) {
      const result = await moodCollection.where({
        userId: OPENID,
        recordDate: recordDate
      }).get();
      
      if (result.data.length === 0) {
        return { code: 1003, message: '该日期没有情绪记录' };
      }
      
      return {
        code: 0,
        message: '获取成功',
        data: result.data[0]
      };
    } else {
      return { code: 1001, message: '缺少查询参数' };
    }
    
    const journalResult = await query.get();
    
    if (!journalResult.data || journalResult.data.userId !== OPENID) {
      return { code: 1003, message: '日记不存在或无权限访问' };
    }
    
    return {
      code: 0,
      message: '获取成功',
      data: journalResult.data
    };
    
  } catch (error) {
    console.error('获取情绪日记失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取情绪日记列表
async function getMoodJournalList(data, wxContext) {
  const { OPENID } = wxContext;
  const {
    startDate,
    endDate,
    page = 1,
    limit = 20,
    sortOrder = 'desc'
  } = data;
  
  try {
    const moodCollection = db.collection('moodJournals');
    let query = moodCollection.where({
      userId: OPENID
    });
    
    // 日期范围筛选
    if (startDate && endDate) {
      query = query.where({
        recordDate: _.gte(startDate).and(_.lte(endDate))
      });
    } else if (startDate) {
      query = query.where({
        recordDate: _.gte(startDate)
      });
    } else if (endDate) {
      query = query.where({
        recordDate: _.lte(endDate)
      });
    }
    
    // 排序
    const sortDirection = sortOrder === 'desc' ? 'desc' : 'asc';
    query = query.orderBy('recordDate', sortDirection);
    
    // 分页
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    
    const result = await query.get();
    
    // 获取总数
    const countResult = await moodCollection.where({
      userId: OPENID,
      ...(startDate && endDate && {
        recordDate: _.gte(startDate).and(_.lte(endDate))
      })
    }).count();
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        journals: result.data,
        pagination: {
          page: page,
          limit: limit,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / limit)
        }
      }
    };
    
  } catch (error) {
    console.error('获取情绪日记列表失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 删除情绪日记
async function deleteMoodJournal(data, wxContext) {
  const { OPENID } = wxContext;
  const { journalId } = data;
  
  try {
    const moodCollection = db.collection('moodJournals');
    
    // 验证日记所有权
    const journalResult = await moodCollection.doc(journalId).get();
    if (!journalResult.data || journalResult.data.userId !== OPENID) {
      return { code: 1003, message: '日记不存在或无权限访问' };
    }
    
    await moodCollection.doc(journalId).remove();
    
    // 更新用户统计
    await updateUserMoodStats(OPENID, 'delete');
    
    return {
      code: 0,
      message: '删除成功'
    };
    
  } catch (error) {
    console.error('删除情绪日记失败:', error);
    return { code: 3001, message: '删除失败' };
  }
}

// 快速情绪记录
async function quickMoodRecord(data, wxContext) {
  const { OPENID } = wxContext;
  const { mood, date } = data;
  
  try {
    const moodCollection = db.collection('moodJournals');
    const recordDate = date || new Date().toISOString().split('T')[0];
    
    // 检查当天是否已有记录
    const existingQuery = await moodCollection.where({
      userId: OPENID,
      recordDate: recordDate
    }).get();
    
    if (existingQuery.data.length > 0) {
      // 更新现有记录
      await moodCollection.doc(existingQuery.data[0]._id).update({
        data: {
          'mood.primary': mood,
          updatedAt: new Date()
        }
      });
      
      return {
        code: 0,
        message: '情绪记录更新成功',
        data: { journalId: existingQuery.data[0]._id }
      };
    } else {
      // 创建新记录
      const journal = {
        userId: OPENID,
        recordDate: recordDate,
        mood: {
          primary: mood,
          secondary: [],
          intensity: 5,
          triggers: []
        },
        activities: [],
        note: '',
        weather: '',
        location: '',
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await moodCollection.add({
        data: journal
      });
      
      // 更新用户统计
      await updateUserMoodStats(OPENID, 'create');
      
      return {
        code: 0,
        message: '情绪记录创建成功',
        data: { journalId: result._id }
      };
    }
    
  } catch (error) {
    console.error('快速情绪记录失败:', error);
    return { code: 3001, message: '记录失败' };
  }
}

// 获取今日情绪
async function getTodayMood(data, wxContext) {
  const { OPENID } = wxContext;
  const { date } = data;
  
  const recordDate = date || new Date().toISOString().split('T')[0];
  
  try {
    const moodCollection = db.collection('moodJournals');
    const result = await moodCollection.where({
      userId: OPENID,
      recordDate: recordDate
    }).get();
    
    if (result.data.length === 0) {
      return {
        code: 0,
        message: '今日还未记录情绪',
        data: null
      };
    }
    
    return {
      code: 0,
      message: '获取成功',
      data: result.data[0]
    };
    
  } catch (error) {
    console.error('获取今日情绪失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取情绪统计
async function getMoodStats(data, wxContext) {
  const { OPENID } = wxContext;
  const { days = 30 } = data;
  
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    const startDateStr = startDate.toISOString().split('T')[0];
    
    const moodCollection = db.collection('moodJournals');
    const result = await moodCollection.where({
      userId: OPENID,
      recordDate: _.gte(startDateStr)
    }).get();
    
    const journals = result.data;
    
    // 统计各种情绪的出现次数
    const moodCounts = {};
    const intensitySum = {};
    const intensityCount = {};
    
    journals.forEach(journal => {
      const primary = journal.mood.primary;
      const intensity = journal.mood.intensity || 5;
      
      moodCounts[primary] = (moodCounts[primary] || 0) + 1;
      intensitySum[primary] = (intensitySum[primary] || 0) + intensity;
      intensityCount[primary] = (intensityCount[primary] || 0) + 1;
    });
    
    // 计算平均强度
    const moodAverages = {};
    Object.keys(intensitySum).forEach(mood => {
      moodAverages[mood] = Math.round(intensitySum[mood] / intensityCount[mood] * 10) / 10;
    });
    
    // 找出最常见的情绪
    const mostCommonMood = Object.keys(moodCounts).reduce((a, b) => 
      moodCounts[a] > moodCounts[b] ? a : b, null
    );
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        totalRecords: journals.length,
        moodCounts: moodCounts,
        moodAverages: moodAverages,
        mostCommonMood: mostCommonMood,
        recordDays: days
      }
    };
    
  } catch (error) {
    console.error('获取情绪统计失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 获取情绪趋势
async function getMoodTrend(data, wxContext) {
  const { OPENID } = wxContext;
  const { days = 7 } = data;
  
  try {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days + 1);
    
    const moodCollection = db.collection('moodJournals');
    const result = await moodCollection.where({
      userId: OPENID,
      recordDate: _.gte(startDate.toISOString().split('T')[0]).and(_.lte(endDate.toISOString().split('T')[0]))
    }).orderBy('recordDate', 'asc').get();
    
    const journals = result.data;
    const trendData = [];
    
    // 生成每一天的数据
    for (let i = 0; i < days; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      const dateStr = currentDate.toISOString().split('T')[0];
      
      const dayJournal = journals.find(j => j.recordDate === dateStr);
      
      trendData.push({
        date: dateStr,
        mood: dayJournal ? dayJournal.mood.primary : null,
        intensity: dayJournal ? dayJournal.mood.intensity : null,
        hasRecord: !!dayJournal
      });
    }
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        trend: trendData,
        days: days
      }
    };
    
  } catch (error) {
    console.error('获取情绪趋势失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 更新用户情绪统计
async function updateUserMoodStats(userId, action) {
  try {
    const usersCollection = db.collection('users');
    const updateData = {
      updatedAt: new Date()
    };
    
    if (action === 'create') {
      updateData['statistics.totalMoodRecords'] = _.inc(1);
    } else if (action === 'delete') {
      updateData['statistics.totalMoodRecords'] = _.inc(-1);
    }
    
    await usersCollection.where({
      openid: userId
    }).update({
      data: updateData
    });
    
  } catch (error) {
    console.error('更新用户情绪统计失败:', error);
  }
}

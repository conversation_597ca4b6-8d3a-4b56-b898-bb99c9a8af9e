// 消息推送云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { action, ...data } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    switch (action) {
      case 'subscribe':
        return await subscribeMessage(data, wxContext);
      case 'sendTaskReminder':
        return await sendTaskReminder(data, wxContext);
      case 'sendBatchReminders':
        return await sendBatchReminders(data, wxContext);
      case 'getSubscriptionStatus':
        return await getSubscriptionStatus(data, wxContext);
      case 'updateSettings':
        return await updateNotificationSettings(data, wxContext);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

// 订阅消息
async function subscribeMessage(data, wxContext) {
  const { OPENID } = wxContext;
  const { templateId, scene = 'task_reminder' } = data;
  
  try {
    // 记录用户订阅状态
    const subscriptionsCollection = db.collection('subscriptions');
    
    const subscription = {
      userId: OPENID,
      templateId: templateId,
      scene: scene,
      status: 'active',
      subscribedAt: new Date(),
      updatedAt: new Date()
    };
    
    // 查找是否已存在订阅
    const existingQuery = await subscriptionsCollection.where({
      userId: OPENID,
      templateId: templateId,
      scene: scene
    }).get();
    
    if (existingQuery.data.length > 0) {
      // 更新现有订阅
      await subscriptionsCollection.doc(existingQuery.data[0]._id).update({
        data: {
          status: 'active',
          updatedAt: new Date()
        }
      });
    } else {
      // 创建新订阅
      await subscriptionsCollection.add({
        data: subscription
      });
    }
    
    return {
      code: 0,
      message: '订阅成功',
      data: {
        templateId: templateId,
        scene: scene
      }
    };
    
  } catch (error) {
    console.error('订阅消息失败:', error);
    return { code: 3001, message: '订阅失败' };
  }
}

// 发送任务提醒
async function sendTaskReminder(data, wxContext) {
  const { taskId, userId, templateId } = data;
  
  try {
    // 获取任务信息
    const tasksCollection = db.collection('tasks');
    const taskResult = await tasksCollection.doc(taskId).get();
    
    if (!taskResult.data) {
      return { code: 1003, message: '任务不存在' };
    }
    
    const task = taskResult.data;
    
    // 检查用户订阅状态
    const subscriptionsCollection = db.collection('subscriptions');
    const subscriptionQuery = await subscriptionsCollection.where({
      userId: userId,
      templateId: templateId,
      status: 'active'
    }).get();
    
    if (subscriptionQuery.data.length === 0) {
      return { code: 1004, message: '用户未订阅消息' };
    }
    
    // 准备消息数据
    const messageData = {
      thing1: { value: task.title.substring(0, 20) }, // 任务标题
      time2: { value: formatTime(task.dueDate) }, // 提醒时间
      thing3: { value: task.description.substring(0, 20) || '无描述' }, // 任务描述
      phrase4: { value: getPriorityText(task.priority) } // 优先级
    };
    
    // 发送订阅消息
    const sendResult = await cloud.openapi.subscribeMessage.send({
      touser: userId,
      templateId: templateId,
      page: `pages/task-detail/index?id=${taskId}`,
      data: messageData,
      miniprogramState: 'formal' // 正式版
    });
    
    if (sendResult.errCode === 0) {
      // 更新任务提醒状态
      await tasksCollection.doc(taskId).update({
        data: {
          'reminder.sent': true,
          'reminder.sentAt': new Date(),
          updatedAt: new Date()
        }
      });
      
      // 记录消息发送日志
      await logMessageSent(userId, taskId, templateId, 'success', messageData);
      
      return {
        code: 0,
        message: '提醒发送成功',
        data: {
          taskId: taskId,
          sentAt: new Date()
        }
      };
    } else {
      // 记录发送失败日志
      await logMessageSent(userId, taskId, templateId, 'failed', messageData, sendResult);
      
      return {
        code: 2001,
        message: '消息发送失败',
        data: sendResult
      };
    }
    
  } catch (error) {
    console.error('发送任务提醒失败:', error);
    return { code: 3001, message: '发送失败' };
  }
}

// 批量发送提醒
async function sendBatchReminders(data, wxContext) {
  const { templateId } = data;
  
  try {
    // 获取需要发送提醒的任务
    const now = new Date();
    const reminderTime = new Date(now.getTime() + 30 * 60 * 1000); // 30分钟后的任务
    
    const tasksCollection = db.collection('tasks');
    const tasksQuery = await tasksCollection.where({
      status: 'pending',
      'reminder.enabled': true,
      'reminder.sent': false,
      'reminder.time': db.command.lte(reminderTime)
    }).get();
    
    const tasks = tasksQuery.data;
    let successCount = 0;
    let failCount = 0;
    
    // 批量发送提醒
    for (const task of tasks) {
      try {
        const result = await sendTaskReminder({
          taskId: task._id,
          userId: task.userId,
          templateId: templateId
        }, wxContext);
        
        if (result.code === 0) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        console.error(`发送任务 ${task._id} 提醒失败:`, error);
        failCount++;
      }
    }
    
    return {
      code: 0,
      message: '批量发送完成',
      data: {
        total: tasks.length,
        success: successCount,
        failed: failCount
      }
    };
    
  } catch (error) {
    console.error('批量发送提醒失败:', error);
    return { code: 3001, message: '批量发送失败' };
  }
}

// 获取订阅状态
async function getSubscriptionStatus(data, wxContext) {
  const { OPENID } = wxContext;
  
  try {
    const subscriptionsCollection = db.collection('subscriptions');
    const subscriptionsQuery = await subscriptionsCollection.where({
      userId: OPENID,
      status: 'active'
    }).get();
    
    const subscriptions = subscriptionsQuery.data.map(sub => ({
      templateId: sub.templateId,
      scene: sub.scene,
      subscribedAt: sub.subscribedAt
    }));
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        subscriptions: subscriptions,
        hasActiveSubscriptions: subscriptions.length > 0
      }
    };
    
  } catch (error) {
    console.error('获取订阅状态失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

// 更新通知设置
async function updateNotificationSettings(data, wxContext) {
  const { OPENID } = wxContext;
  const { enabled, reminderTime, types } = data;
  
  try {
    const usersCollection = db.collection('users');
    
    const updateData = {
      'preferences.notificationEnabled': enabled,
      'preferences.reminderTime': reminderTime,
      'preferences.notificationTypes': types || ['task_reminder'],
      updatedAt: new Date()
    };
    
    await usersCollection.where({
      openid: OPENID
    }).update({
      data: updateData
    });
    
    return {
      code: 0,
      message: '设置更新成功',
      data: updateData
    };
    
  } catch (error) {
    console.error('更新通知设置失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}

// 记录消息发送日志
async function logMessageSent(userId, taskId, templateId, status, messageData, error = null) {
  try {
    const logsCollection = db.collection('messageLogs');
    
    await logsCollection.add({
      data: {
        userId: userId,
        taskId: taskId,
        templateId: templateId,
        status: status, // success/failed
        messageData: messageData,
        error: error,
        sentAt: new Date(),
        createdAt: new Date()
      }
    });
    
  } catch (logError) {
    console.error('记录消息日志失败:', logError);
  }
}

// 工具函数
function formatTime(date) {
  if (!date) return '未设置';
  
  const d = new Date(date);
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hour = String(d.getHours()).padStart(2, '0');
  const minute = String(d.getMinutes()).padStart(2, '0');
  
  return `${month}月${day}日 ${hour}:${minute}`;
}

function getPriorityText(priority) {
  const priorityMap = {
    high: '紧急',
    medium: '重要',
    low: '普通'
  };
  return priorityMap[priority] || '普通';
}

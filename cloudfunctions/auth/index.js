// 用户认证云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { action, ...data } = event;
  const wxContext = cloud.getWXContext();
  
  try {
    switch (action) {
      case 'login':
        return await login(data, wxContext);
      case 'updateProfile':
        return await updateProfile(data, wxContext);
      case 'getProfile':
        return await getProfile(wxContext);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

// 用户登录
async function login(data, wxContext) {
  const { OPENID, APPID, UNIONID } = wxContext;
  
  try {
    // 查找或创建用户
    const userCollection = db.collection('users');
    const userQuery = await userCollection.where({
      openid: OPENID
    }).get();
    
    let userInfo;
    
    if (userQuery.data.length === 0) {
      // 新用户，创建用户记录
      const newUser = {
        openid: OPENID,
        unionid: UNIONID,
        profile: {
          nickname: '新用户',
          avatarUrl: '',
          gender: 0,
          city: '',
          province: '',
          country: ''
        },
        preferences: {
          notificationEnabled: true,
          reminderTime: '09:00',
          theme: 'light',
          language: 'zh-CN'
        },
        statistics: {
          totalAnxietyRecords: 0,
          totalTasksCompleted: 0,
          totalMoodRecords: 0,
          streakDays: 0,
          lastActiveDate: new Date().toISOString().split('T')[0]
        },
        privacy: {
          dataRetentionDays: 365,
          allowAnalytics: true
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const createResult = await userCollection.add({
        data: newUser
      });
      
      userInfo = {
        ...newUser,
        _id: createResult._id
      };
      
      console.log('新用户创建成功:', OPENID);
    } else {
      // 老用户，更新最后活跃时间
      userInfo = userQuery.data[0];
      
      await userCollection.doc(userInfo._id).update({
        data: {
          'statistics.lastActiveDate': new Date().toISOString().split('T')[0],
          updatedAt: new Date()
        }
      });
      
      console.log('用户登录成功:', OPENID);
    }
    
    return {
      code: 0,
      message: '登录成功',
      data: {
        openid: OPENID,
        userInfo: userInfo.profile,
        isNewUser: userQuery.data.length === 0
      }
    };
    
  } catch (error) {
    console.error('登录失败:', error);
    return { code: 3001, message: '登录失败' };
  }
}

// 更新用户资料
async function updateProfile(data, wxContext) {
  const { OPENID } = wxContext;
  const { userInfo } = data;
  
  try {
    const userCollection = db.collection('users');
    
    await userCollection.where({
      openid: OPENID
    }).update({
      data: {
        profile: {
          nickname: userInfo.nickName || '',
          avatarUrl: userInfo.avatarUrl || '',
          gender: userInfo.gender || 0,
          city: userInfo.city || '',
          province: userInfo.province || '',
          country: userInfo.country || ''
        },
        updatedAt: new Date()
      }
    });
    
    return {
      code: 0,
      message: '更新成功',
      data: userInfo
    };
    
  } catch (error) {
    console.error('更新用户资料失败:', error);
    return { code: 3001, message: '更新失败' };
  }
}

// 获取用户资料
async function getProfile(wxContext) {
  const { OPENID } = wxContext;
  
  try {
    const userCollection = db.collection('users');
    const userQuery = await userCollection.where({
      openid: OPENID
    }).get();
    
    if (userQuery.data.length === 0) {
      return { code: 1002, message: '用户不存在' };
    }
    
    const userInfo = userQuery.data[0];
    
    return {
      code: 0,
      message: '获取成功',
      data: {
        profile: userInfo.profile,
        preferences: userInfo.preferences,
        statistics: userInfo.statistics
      }
    };
    
  } catch (error) {
    console.error('获取用户资料失败:', error);
    return { code: 3001, message: '获取失败' };
  }
}

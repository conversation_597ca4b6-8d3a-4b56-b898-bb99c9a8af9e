{"description": "心安AI - 焦虑管理小程序", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx9629059eddd9919f", "projectname": "xinan-ai", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}, {"type": "file", "value": ".giti<PERSON>re"}, {"type": "file", "value": "README.md"}, {"type": "folder", "value": "node_modules"}, {"type": "folder", "value": ".git"}], "include": []}, "editorSetting": {}, "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"name": "首页", "pathName": "pages/index/index", "query": "", "scene": null}, {"name": "焦虑倾诉", "pathName": "pages/anxiety-dump/index", "query": "", "scene": null}, {"name": "分析结果", "pathName": "pages/analysis-result/index", "query": "recordId=test123", "scene": null}, {"name": "任务列表", "pathName": "pages/task-list/index", "query": "", "scene": null}]}}}
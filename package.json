{"name": "xinan-ai", "version": "1.0.0", "description": "心安AI - 焦虑管理小程序", "main": "app.js", "scripts": {"deploy:all": "node scripts/deploy-cloud.js all", "deploy:auth": "node scripts/deploy-cloud.js single auth", "deploy:anxiety": "node scripts/deploy-cloud.js single anxiety", "deploy:tasks": "node scripts/deploy-cloud.js single tasks", "deploy:mood": "node scripts/deploy-cloud.js single mood", "deploy:user": "node scripts/deploy-cloud.js single user", "deploy:notification": "node scripts/deploy-cloud.js single notification", "deploy:achievements": "node scripts/deploy-cloud.js single achievements", "preview": "node scripts/preview.js", "upload": "node scripts/upload.js"}, "keywords": ["miniprogram", "wechat", "anxiety", "ai"], "author": "xinan-ai", "license": "MIT", "devDependencies": {"miniprogram-ci": "^1.9.17"}}
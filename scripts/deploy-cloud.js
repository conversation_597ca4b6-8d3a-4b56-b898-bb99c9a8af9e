#!/usr/bin/env node

const ci = require('miniprogram-ci');
const path = require('path');

// 配置信息
const config = {
  appid: 'wx8c631f7e9f2465e1', // 你的小程序 appid
  type: 'miniProgram',
  projectPath: path.resolve(__dirname, '../'),
  privateKeyPath: path.resolve(__dirname, '../private.key'), // 私钥文件路径
  ignores: ['node_modules/**/*'],
};

// 云函数列表
const cloudFunctions = [
  'auth',
  'anxiety', 
  'tasks',
  'mood',
  'user',
  'notification',
  'achievements'
];

async function deployCloudFunctions() {
  try {
    console.log('开始部署云函数...');

    const project = new ci.Project(config);

    // 逐个部署云函数
    for (const funcName of cloudFunctions) {
      console.log(`正在部署云函数: ${funcName}`);

      try {
        const result = await ci.uploadCloudFunction({
          project,
          name: funcName,
          path: path.resolve(__dirname, `../cloudfunctionRoot/${funcName}`),
          env: 'cloud1-0gfpogwuc45d1489', // 你的云开发环境ID
        });

        console.log(`✅ ${funcName} 部署成功`, result);
      } catch (error) {
        console.error(`❌ ${funcName} 部署失败:`, error.message);
      }
    }

    console.log('🎉 所有云函数部署完成！');

  } catch (error) {
    console.error('部署失败:', error);
    process.exit(1);
  }
}

// 单独部署某个云函数
async function deploySingleFunction(functionName) {
  try {
    console.log(`开始部署云函数: ${functionName}`);

    const project = new ci.Project(config);

    const result = await ci.uploadCloudFunction({
      project,
      name: functionName,
      path: path.resolve(__dirname, `../cloudfunctions/${functionName}`),
      env: 'cloud1-0gfpogwuc45d1489',
    });

    console.log(`✅ ${functionName} 部署成功`, result);

  } catch (error) {
    console.error(`❌ ${functionName} 部署失败:`, error);
    process.exit(1);
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];
const functionName = args[1];

switch (command) {
  case 'all':
    deployCloudFunctions();
    break;
  case 'single':
    if (!functionName) {
      console.error('请指定要部署的云函数名称');
      console.log('用法: node deploy-cloud.js single <function-name>');
      process.exit(1);
    }
    deploySingleFunction(functionName);
    break;
  default:
    console.log('用法:');
    console.log('  部署所有云函数: node deploy-cloud.js all');
    console.log('  部署单个云函数: node deploy-cloud.js single <function-name>');
    console.log('');
    console.log('可用的云函数:', cloudFunctions.join(', '));
}

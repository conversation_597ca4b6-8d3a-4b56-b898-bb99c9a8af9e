<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabBar 图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .icon-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .icon-preview {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 15px;
        }
        .icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .icon-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        svg {
            width: 40px;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
        }
        .download-btn {
            background: #4A90E2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .download-btn:hover {
            background: #357abd;
        }
        .instructions {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #333;
        }
        .instructions ol {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>心安AI - TabBar 图标生成器</h1>
        
        <div class="icon-grid">
            <!-- 首页图标 -->
            <div class="icon-item">
                <h3>首页 (Home)</h3>
                <div class="icon-preview">
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#999999">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#4A90E2">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('home')">下载图标</button>
            </div>

            <!-- 任务图标 -->
            <div class="icon-item">
                <h3>任务 (Task)</h3>
                <div class="icon-preview">
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#999999">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#4A90E2">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('task')">下载图标</button>
            </div>

            <!-- 心情图标 -->
            <div class="icon-item">
                <h3>心情 (Mood)</h3>
                <div class="icon-preview">
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#999999">
                            <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
                        </svg>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#4A90E2">
                            <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
                        </svg>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('mood')">下载图标</button>
            </div>

            <!-- 个人中心图标 -->
            <div class="icon-item">
                <h3>个人中心 (Profile)</h3>
                <div class="icon-preview">
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#999999">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        <div class="icon-label">未选中</div>
                    </div>
                    <div class="icon-container">
                        <svg viewBox="0 0 24 24" fill="#4A90E2">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        <div class="icon-label">选中</div>
                    </div>
                </div>
                <button class="download-btn" onclick="downloadIcon('profile')">下载图标</button>
            </div>
        </div>

        <div class="instructions">
            <h3>使用说明</h3>
            <ol>
                <li>点击"下载图标"按钮，会自动下载对应的PNG图标文件</li>
                <li>每个图标包含两个版本：普通状态和选中状态</li>
                <li>将下载的图标文件放置到小程序项目的 <code>images/tabbar/</code> 目录下</li>
                <li>确保文件名与 app.json 中配置的路径一致</li>
                <li>图标尺寸为 81px × 81px，适配高分辨率屏幕</li>
            </ol>
            <p><strong>注意：</strong>如果自动下载不工作，可以右键点击图标选择"另存为"手动保存。</p>
        </div>
    </div>

    <script>
        function downloadIcon(iconName) {
            const icons = {
                home: {
                    normal: 'M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z',
                    active: 'M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'
                },
                task: {
                    normal: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z',
                    active: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'
                },
                mood: {
                    normal: 'M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z',
                    active: 'M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z'
                },
                profile: {
                    normal: 'M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z',
                    active: 'M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'
                }
            };

            // 生成并下载普通状态图标
            generateAndDownload(iconName, icons[iconName].normal, '#999999', false);
            
            // 生成并下载选中状态图标
            setTimeout(() => {
                generateAndDownload(iconName, icons[iconName].active, '#4A90E2', true);
            }, 500);
        }

        function generateAndDownload(iconName, pathData, color, isActive) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const size = 81; // 高分辨率尺寸
            
            canvas.width = size;
            canvas.height = size;
            
            // 创建SVG
            const svg = `
                <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="${pathData}" fill="${color}"/>
                </svg>
            `;
            
            const img = new Image();
            const svgBlob = new Blob([svg], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                // 转换为PNG并下载
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = `${iconName}${isActive ? '-active' : ''}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
            };
            
            img.src = url;
        }
    </script>
</body>
</html>

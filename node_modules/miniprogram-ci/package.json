{"name": "miniprogram-ci", "version": "1.9.17", "description": "pre compilation module about the miniProgram / miniGame project extracted from WeChatDevtools.", "main": "dist/index.js", "types": "dist/@types/index.d.ts", "scripts": {"init": "git submodule init && git submodule update", "submodule": "git submodule init && git submodule update", "dev": "npm run init && rm -rf dist && gulp copy && tsc -w", "beta": "npm run init && rm -rf dist && tsc && gulp copy && node ./task/afterbuild.js && npm run test", "build": "npm run bb && npm run report", "bb": "npm run init &&  rm -rf dist && gulp inc-version && tsc && gulp build && node ./task/afterbuild.js ", "bbwin": "gulp inc-version && tsc && gulp build && node ./task/afterbuild.js ", "testlandun": "echo 'haha'", "test": "nyc --reporter=text npm run mocha && npm run jest", "jest": "jest", "report": "nyc --reporter=html npm run mocha && npm run jest", "mocha": "mocha --reporter mochawesome --exit --timeout 5000 || true", "build-json-schema": "node ./schema/task/build.js && gulp copy-schema", "eslint": "eslint --ext .ts,.tsx,.js,.jsx ./src", "mochawin": "mocha --reporter mochawesome --reporter-options reportFilename=result_win --exit || exit /b 0", "mochamac": "mocha --reporter mochawesome --reporter-options reportFilename=result_mac --exit || true", "mochalinux": "mocha --reporter mochawesome --reporter-options reportFilename=result_linux --exit || true"}, "repository": {"type": "git", "url": ""}, "bin": {"miniprogram-ci": "./bin/miniprogram-ci.js"}, "files": ["dist/**/*", "bin/**/*", "CHANGELOG.md", "README.md", "package.json"], "author": "canhuang", "license": "MIT", "devDependencies": {"@tencent/eslint-config-tencent": "^1.0.4", "@types/babel__core": "7.1.17", "@types/babel-code-frame": "^6.20.2", "@types/babel-core": "6.25.6", "@types/cssnano": "^4.0.1", "@types/estree": "0.0.47", "@types/fs-extra": "^8.0.0", "@types/glob": "^7.2.0", "@types/html-minifier": "^3.5.3", "@types/jest": "^26.0.20", "@types/less": "^3.0.3", "@types/lodash": "^4.14.178", "@types/node": "^13.13.52", "@types/read-package-tree": "^5.2.0", "@types/request": "^2.48.3", "@types/rimraf": "^3.0.0", "@types/sass": "^1.43.1", "@types/uglify-js": "^3.0.4", "@types/yargs": "^13.0.3", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^7.20.0", "gulp": "^4.0.2", "gulp-terser": "^1.2.0", "jest": "^26.6.3", "mocha": "^6.2.2", "mochawesome": "^4.1.0", "nyc": "^14.1.1", "ts-jest": "^26.5.2", "typescript": "^4.5.3", "typescript-json-schema": "^0.41.0"}, "dependencies": {"@babel/core": "7.21.4", "@babel/code-frame": "7.22.10", "@babel/compat-data": "7.22.9", "@babel/eslint-parser": "7.22.10", "@babel/generator": "7.21.4", "@babel/helper-annotate-as-pure": "7.22.5", "@babel/helper-builder-binary-assignment-operator-visitor": "7.22.10", "@babel/helper-compilation-targets": "7.22.10", "@babel/helper-create-class-features-plugin": "7.22.10", "@babel/helper-create-regexp-features-plugin": "7.22.9", "@babel/helper-define-polyfill-provider": "0.3.3", "@babel/helper-environment-visitor": "7.22.5", "@babel/helper-function-name": "7.22.5", "@babel/helper-hoist-variables": "7.22.5", "@babel/helper-member-expression-to-functions": "7.22.5", "@babel/helper-module-imports": "7.21.4", "@babel/helper-module-transforms": "7.22.9", "@babel/helper-optimise-call-expression": "7.22.5", "@babel/helper-plugin-utils": "7.24.6", "@babel/helper-remap-async-to-generator": "7.22.9", "@babel/helper-replace-supers": "7.22.9", "@babel/helper-simple-access": "7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "7.24.6", "@babel/helper-split-export-declaration": "7.22.6", "@babel/helper-string-parser": "7.24.6", "@babel/helper-validator-identifier": "7.24.6", "@babel/helper-validator-option": "7.22.5", "@babel/helper-wrap-function": "7.22.10", "@babel/helpers": "7.21.0", "@babel/highlight": "7.22.10", "@babel/parser": "7.21.4", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "7.24.6", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "7.24.6", "@babel/plugin-proposal-async-generator-functions": "7.20.7", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-class-static-block": "7.21.0", "@babel/plugin-proposal-decorators": "7.21.0", "@babel/plugin-proposal-do-expressions": "7.18.6", "@babel/plugin-proposal-dynamic-import": "7.18.6", "@babel/plugin-proposal-export-default-from": "7.18.10", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-function-bind": "7.18.9", "@babel/plugin-proposal-function-sent": "7.18.6", "@babel/plugin-proposal-json-strings": "7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-numeric-separator": "7.18.6", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-proposal-optional-catch-binding": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-pipeline-operator": "7.18.9", "@babel/plugin-proposal-private-methods": "7.18.6", "@babel/plugin-proposal-private-property-in-object": "7.21.0", "@babel/plugin-proposal-throw-expressions": "7.18.6", "@babel/plugin-proposal-unicode-property-regex": "7.18.6", "@babel/plugin-syntax-async-generators": "7.8.4", "@babel/plugin-syntax-bigint": "7.8.3", "@babel/plugin-syntax-class-properties": "7.12.13", "@babel/plugin-syntax-class-static-block": "7.14.5", "@babel/plugin-syntax-decorators": "7.22.10", "@babel/plugin-syntax-do-expressions": "7.22.5", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-export-default-from": "7.22.5", "@babel/plugin-syntax-export-namespace-from": "7.8.3", "@babel/plugin-syntax-function-bind": "7.22.5", "@babel/plugin-syntax-function-sent": "7.22.5", "@babel/plugin-syntax-import-assertions": "7.24.6", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/plugin-syntax-json-strings": "7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "7.8.3", "@babel/plugin-syntax-numeric-separator": "7.10.4", "@babel/plugin-syntax-object-rest-spread": "7.8.3", "@babel/plugin-syntax-optional-catch-binding": "7.8.3", "@babel/plugin-syntax-optional-chaining": "7.8.3", "@babel/plugin-syntax-pipeline-operator": "7.22.5", "@babel/plugin-syntax-private-property-in-object": "7.14.5", "@babel/plugin-syntax-throw-expressions": "7.22.5", "@babel/plugin-syntax-top-level-await": "7.14.5", "@babel/plugin-syntax-typescript": "7.22.5", "@babel/plugin-transform-arrow-functions": "7.22.5", "@babel/plugin-transform-async-to-generator": "7.22.5", "@babel/plugin-transform-block-scoped-functions": "7.22.5", "@babel/plugin-transform-block-scoping": "7.22.10", "@babel/plugin-transform-classes": "7.22.6", "@babel/plugin-transform-computed-properties": "7.22.5", "@babel/plugin-transform-destructuring": "7.22.10", "@babel/plugin-transform-dotall-regex": "7.22.5", "@babel/plugin-transform-duplicate-keys": "7.22.5", "@babel/plugin-transform-exponentiation-operator": "7.22.5", "@babel/plugin-transform-for-of": "7.22.5", "@babel/plugin-transform-function-name": "7.22.5", "@babel/plugin-transform-literals": "7.22.5", "@babel/plugin-transform-member-expression-literals": "7.22.5", "@babel/plugin-transform-modules-amd": "7.22.5", "@babel/plugin-transform-modules-commonjs": "7.21.2", "@babel/plugin-transform-modules-systemjs": "7.22.5", "@babel/plugin-transform-modules-umd": "7.22.5", "@babel/plugin-transform-named-capturing-groups-regex": "7.22.5", "@babel/plugin-transform-new-target": "7.22.5", "@babel/plugin-transform-object-super": "7.22.5", "@babel/plugin-transform-optional-chaining": "7.24.6", "@babel/plugin-transform-parameters": "7.22.5", "@babel/plugin-transform-property-literals": "7.22.5", "@babel/plugin-transform-regenerator": "7.22.10", "@babel/plugin-transform-reserved-words": "7.22.5", "@babel/plugin-transform-runtime": "7.21.4", "@babel/plugin-transform-shorthand-properties": "7.22.5", "@babel/plugin-transform-spread": "7.22.5", "@babel/plugin-transform-sticky-regex": "7.22.5", "@babel/plugin-transform-template-literals": "7.22.5", "@babel/plugin-transform-typeof-symbol": "7.22.5", "@babel/plugin-transform-typescript": "7.21.3", "@babel/plugin-transform-unicode-escapes": "7.22.10", "@babel/plugin-transform-unicode-regex": "7.22.5", "@babel/preset-env": "7.21.4", "@babel/preset-modules": "0.1.6", "@babel/regjsgen": "0.8.0", "@babel/runtime": "7.21.0", "@babel/template": "7.20.7", "@babel/traverse": "7.21.4", "@babel/types": "7.24.6", "@vue/reactivity": "3.0.5", "acorn": "^6.1.1", "autoprefixer": "^10.4.0", "babel-code-frame": "6.26.0", "babel-core": "6.26.0", "babel-preset-es2015": "6.24.1", "babel-preset-stage-0": "6.24.1", "chokidar": "^3.5.1", "cos-nodejs-sdk-v5": "^2.11.4", "cssnano": "^5.0.12", "eventemitter3": "^4.0.5", "fs-extra": "8.1.0", "get-proxy": "^2.1.0", "glob": "7.1.2", "html-minifier": "4.0.0", "jimp": "^0.9.3", "jsonschema": "^1.2.5", "jszip": "^3.4.0", "less": "^4.1.2", "licia": "^1.31.1", "lodash": "^4.17.15", "memory-fs": "^0.5.0", "minimatch": "3.0.4", "moment-timezone": "^0.5.34", "postcss": "^8.4.4", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "read-package-tree": "5.2.1", "request": "^2.81.2", "rimraf": "^3.0.2", "sass": "^1.44.0", "source-map": "0.6.1", "string-hash-64": "1.0.3", "terser": "4.8.0", "tslib": "1.10.0", "uglify-js": "3.0.27", "wxml-minifier": "0.0.1", "yargs": "^15.0.2"}}
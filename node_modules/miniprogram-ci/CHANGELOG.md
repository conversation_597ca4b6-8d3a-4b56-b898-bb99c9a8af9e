#### 1.9.17
 - `fix` 修复 babel 导致的 path.requeueComputedKeyAndDecorators 问题

#### 1.9.15
 - `fix` 修复 app.json 的 app-bar 配置未生效的 bug。

#### 1.9.14
 - `new` 更新  `es6` 或者 `es7` 编译时，忽略编译的文件大小阈值由 500kb 改为 2MB，即大小超过 2MB 的 js 文件不会被编译。

#### 1.9.11
 - `new` 新增  `checkCodeQuality` 代码质量检查能力。

#### 1.9.10
  - `new` 新增 支持 compileWorklet 编译选项。
#### 1.9.9
  - `new` 更新 worklet 函数的编译逻辑。

## 1.9.8
  - `fix` 修复 TypeError: _regeneratorRuntime3 is not a function 报错问题
## 1.9.7
  - `new` 更新 babel 版本到 7.21.4
## 1.9.5
  - `new` 新增 getDevSourceMap 接口获取 sourcemap 文件支持流式传输
## 1.9.1
  - `new` 更新 支持预览和上传时，过滤无依赖文件
## 1.8.60 
  - `new` 更新 支持预览时主包、分包体积上限调整为 4M
  - `new` 更新 支持 worker 代码打包到小程序分包
  - `fix` 修复 代码保护兼容 resolveAlias
  - `fix` 修复 使用编译插件时，app.json 中包含小程序插件页面会报错的 bug
## 1.8.35
  - `new` 新增 支持拉取第三方代开发的“授权小程序”的 sourcemap
## 1.8.25
  - `new` 更新 增强编译支持编译 worklet 函数
  - `fix` 修复 使用编译插件时，也支持 wxml 压缩功能
## 1.8.18
  - `new` 更新 `analyseCode` 代码依赖分析能力。
## 1.8.12
  - `new` app.json 配置支持 halfPage
  - `new` 更新 上传时忽略 project.private.config.json 文件
  - `new` 更新 代码包超过 5MB 后，默认通过异步方式上传代码，详情可查看 useCOS 参数
  - `fix` 修复 ts 项目支持 babelSetting ignore 配置
  - `fix` 修复 windows 编译 less 或 sass 文件提示路径出错的 bug [相关反馈](https://developers.weixin.qq.com/community/develop/doc/000ecc927587e0aca69df553851400)
## 1.6.1
  - `new` 新增 支持编译工具上对应的 Typescript 模板项目。
  - `fix` 更新 wxml压缩逻辑，可通过 minifyWXML 参数开启 wxml 压缩。
## 1.5.12
  - `new` 新增 `analyseCode` 分析代码间的依赖关系。
## 1.5.1
  - `fix` 修复 js 压缩中文字符串转义导致体积变大的问题，[相关反馈](https://developers.weixin.qq.com/community/develop/doc/00066831aa8b203b8f8cba59251400)
  - `new` 新增 `getCompiledResult` 方法，输出本地编译后的代码包内容。
## 1.4.12
  - `new` 更新 增强编译所依赖的 `babel` 版本
  - `new` 新增 云开发云存储上传文件
  - `new` 新增 云开发静态托管上传文件
  - `new` 新增 云开发云托管新建版本
## 1.3.5
  - `new` 新增 `uploadJsServer` 上传小游戏 jsserver 代码。
## 1.2.3
  - `new` 新增 创建 Project 支持传入 privateKey 来替代 privateKeyPath，[相关反馈](https://developers.weixin.qq.com/community/develop/doc/000686e21d04a05616db1c25c5b800)
  - `new` 新增 预览和上传时，支持使用异步方式上传代码包内容，可通过 useCOS 参数来控制。
## 1.1.6
  - `fix` 修复 第三方平台开发 directcommit 为 true 时提示文案不正确的问题
## 1.1.5 
  - `fix` 修复 代码保护功能处理引用 npm 包路径的问题
  - `new` 新增 `upload` 和 `preview` 支持 threads 参数控制编译线程数
  - `fix` 优化 命令行调用出错时，程序退出码为1
## 1.0.98 
  - `fix` 修复第三方package引用miniprogram-ci时，babel preset找不到的bug
## 1.0.96 
  - `fix` 修复因CRLF换行符导致游戏插件signature.json校验失败的bug
## 1.0.94 
  - `fix` 优化getDevSourceMap的网络传输耗时

## 1.0.93 
  - `new` 增强编译支持对`functional-pages`目录独立分包编译
  - `fix` 更新依赖`request`的版本

## 1.0.85 
  - `fix` app.json中分包配置支持 useExtendedLib

## 1.0.71 
  - `fix` 修复代码保护在插件开发模式中的问题

## 1.0.60
  - `new` 新增 `ci.preview` 场景值参数的支持

## 1.0.58
  - `fix` update terser from 3.17.0 to 4.8.0

## 1.0.57
  - `fix` 调整代码保护的问题
  - `fix` wxss calc()压缩问题
  - `fix` 一些其他bugfix

## 1.0.31
- `new` 新增 `upload` 和 `preview` 返回上传后包体信息和插件信息
- `new` 新增 获取最近上传版本 sourceMap 的能力
- `fix` 修复 useExtendedLib 模式下 usingComponent 绝对路径的问题


## 1.0.29
- `new` 支持自定义 node_modules 位置的构建 npm

## 1.0.28
- `new` 支持第三方平台开发模式预览和上传
- `new` 构建npm支持分别构建到分包、独立分包、插件目录

## 1.0.27
- `fix` 修复wxml压缩错误

## 1.0.25
- `new` 废弃`navigateToMiniProgramAppList`字段
- `new` 新增插件开发模式`ci.upload`会返回`devPluginId`字段 
- `new` 支持`kbone`和`weui`的`appJSON["useExtendedLib"]`的声明方式 [详情](https://developers.weixin.qq.com/miniprogram/dev/reference/configuration/app.html#%E9%85%8D%E7%BD%AE%E7%A4%BA%E4%BE%8B)"useExtendedLib"章节，[相关反馈](https://developers.weixin.qq.com/community/develop/doc/000cc6936a8170cc241a8333c56c00?jumpto=reply&parent_commentid=0008c49401878841cb2a4b2f1514&commentid=000a0272df446834693aaf377568)
- `new` 支持 darkmode
- `fix` 修复了`@`开头的`miniprogram_npm`包组件引用
- `fix` windows 更新 schema 路径错误问题
- `fix` 自定义tabbar路径查找问题 [相关反馈](https://developers.weixin.qq.com/community/minihome/doc/0008ca0f5e85b803c33aa6f2d5b400)

## 1.0.24

- `new` 增加25个机器人
- `new` 插件版本号支持semver规则
- `fix` usingComponents无法找到组件缺省index路径的问题 [相关反馈](https://developers.weixin.qq.com/community/develop/doc/000c824a6241d813d72a8de8051800)


## 1.0.18

- `fix` windows环境路径查找问题和换行符问题

## 1.0.16

- `fix` mac & linux usingComponents的路径查找和开发者工具保持一致

## 1.0.15

- `fix` minifyWXML导致的自闭合标签错误 [相关反馈](https://developers.weixin.qq.com/community/develop/doc/000c2810738ed01a9d1abc3f056800)

## 1.0.14

- `fix` 自定义tabbar图标路径校验问题

## 1.0.13

- `fix` 构建npm顺序问题 [相关反馈](https://developers.weixin.qq.com/community/develop/doc/0006c43dbd029017041a05b5653400)

## 1.0.11

- `new` 增加构建npm能力
- `fix` 修复多线程环境变量问题

## 1.0.9

- `new` 增加预览指定pagePath和query的能力
- `fix` 修复进程不结束的问题
- `fix` 修复postcss报错信息不明确的问题

## 1.0.7

- `up` 升级postcss至`7.0.27`
- `fix` 代码保护问题

## 1.0.5

- `new` 支持获取环境代理和npm代理配置
- `fix` 修复代码调用模式下proxy不生效的问题

## 1.0.3

- `new` 支持预览二维码
- `new` 支持proxy

## 1.0.1

首发版本

- `new` 支持命令行调用
- `new` 支持node模块调用
- `new` 支持上传

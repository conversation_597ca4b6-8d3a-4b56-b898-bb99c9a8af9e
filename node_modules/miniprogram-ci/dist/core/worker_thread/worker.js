var _a;process.env&&process.env.isDevtools&&require("../../utils/hackrequire/index"),Object.defineProperty(exports,"__esModule",{value:!0});const tslib_1=require("tslib");(null===(_a=process.env)||void 0===_a?void 0:_a.isDevtools)&&require("../../utils/hackrequire/index");const worker_threads_1=require("worker_threads"),index_1=tslib_1.__importDefault(require("./task/index")),config_1=require("./config"),logger_1=require("../utils/logger");worker_threads_1.isMainThread?module.exports=index_1.default:worker_threads_1.parentPort.on("message",e=>{const{command:r,data:o}=e;r===config_1.COMMAND.RUN_TASK&&(logger_1.logger.info("worker process task start",r,o.data.filePath),(0,index_1.default)(o.taskName,o.data).then(e=>{logger_1.logger.info("worker process task end",r,o.data.filePath),worker_threads_1.parentPort.postMessage({command:config_1.COMMAND.TASK_DONE,data:e})}).catch(e=>{worker_threads_1.parentPort.postMessage({command:config_1.COMMAND.TASK_DONE,data:{error:e.toString()}})}))});
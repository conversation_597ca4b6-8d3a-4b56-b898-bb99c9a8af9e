"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getWorkerManager=exports.AbortEvent=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),os_1=tslib_1.__importDefault(require("os")),events_1=tslib_1.__importDefault(require("events")),log_1=tslib_1.__importDefault(require("../../utils/log")),config_1=require("./config"),call_func_1=require("./task/call_func"),child_process_1=require("child_process"),logger_1=require("../utils/logger"),env_1=require("../utils/env"),cpus=os_1.default.cpus().length,WORKER_PATH=path_1.default.posix.join(__dirname,"./worker.js"),FORK_PATH=path_1.default.posix.join(__dirname,"./fork.js"),MAX_TASK_TRY_TIME=2;let envIsDevtools=!1;function getCurrentScriptPath(){const e=path_1.default.posix.relative(process.cwd(),__filename);return process.cwd()===__dirname?`.${path_1.default.posix.resolve}${e}`:e}let Worker,workerManagerInstance,supportWorkerThread=!1;try{Worker=require("worker_threads").Worker,supportWorkerThread=!0}catch(e){supportWorkerThread=!1}exports.AbortEvent="WorkerTaskAborted";class WorkerInstance extends events_1.default{constructor(){super(),this.status=config_1.EWorkerStatus.free,this.threadId=-1,this._suicideTime=config_1.SUICIDE_TIME["miniprogram-ci"],this._postMessage=()=>{},envIsDevtools&&(this._suicideTime=config_1.SUICIDE_TIME.devtools);const e={NODE_PATH:path_1.default.posix.join(getCurrentScriptPath(),"../../../node_modules"),isDevtools:String(envIsDevtools)};supportWorkerThread?(logger_1.logger.info("create worker start"),this._instance=new Worker(WORKER_PATH,{env:Object.assign(Object.assign({},e),{cpprocessEnv:"workerthread"}),stdout:!0}),this.threadId=this._instance.threadId,this._postMessage=this._instance.postMessage.bind(this._instance)):(this._instance=(0,child_process_1.fork)(FORK_PATH,[""],{env:Object.assign(Object.assign({},e),{cpprocessEnv:"workerprocess"})}),this.threadId=this._instance.pid,this._postMessage=this._instance.send.bind(this._instance));this._instance.on("message",e=>{const{command:t,data:s}=e;t===config_1.COMMAND.TASK_DONE&&this.onTaskDone(s),t===config_1.COMMAND.SEND_LOG&&logger_1.logger.send(s.level,s.args),t===config_1.COMMAND.CALL_FUNC&&call_func_1.call.apply(null,[s.funcName,...s.args]).then(e=>{this._postMessage({command:config_1.COMMAND.CALL_FUNC_RESULT,data:{id:s.id,result:e}})}).catch(e=>{this._postMessage({command:config_1.COMMAND.CALL_FUNC_RESULT,data:{id:s.id,error:e.toString()}})})}),this._instance.on("exit",e=>{0!==e&&1!==e&&log_1.default.error(`worker thread: ${this.threadId}, status: ${this.status}, exit with code: ${e}`),this.emit("exit",{code:e,status:this.status,task:this._task,threadId:this.threadId})})}on(e,t){return super.on(e,t)}onTaskDone(e){var t;this.status=config_1.EWorkerStatus.free,(null===(t=this._task)||void 0===t?void 0:t.resolve)&&(this._task.onStatusUpdate(config_1.ETaskStatus.done),this._task.resolve(e)),this._task=void 0,this.emit("taskDone",this.threadId),env_1.summerProcess||this.setUpSuicideTimer()}setUpSuicideTimer(){this._suicideTimer=setTimeout(()=>{clearTimeout(this._suicideTimer),this.status===config_1.EWorkerStatus.free?(this.status=config_1.EWorkerStatus.dying,supportWorkerThread?this._instance.terminate():this._instance.kill("SIGTERM")):this._suicideTimer||this.setUpSuicideTimer()},this._suicideTime)}runTask(e){clearTimeout(this._suicideTimer),this.status=config_1.EWorkerStatus.busy,this._task=e,this._task.onStatusUpdate(config_1.ETaskStatus.progress),this._postMessage({command:config_1.COMMAND.RUN_TASK,data:{taskName:e.name,data:e.data}})}}class WorkerManager{constructor(e,t=!1){this._taskQueue=[],this._workerPool={},this._max_pool_size=4,this.onWorkerExit=e=>{const{code:t,status:s,task:r,threadId:i}=e;delete this._workerPool[i],s===config_1.EWorkerStatus.busy&&(r.retryTimes+=1,r.retryTimes<=2?(log_1.default.error(`worker thread: ${i} exit with code: ${t} when it is busy, retry ${r.retryTimes} times`),this._taskQueue.push(r)):r.reject(`worker thread exit with code: ${t} when it is busy`)),this._run()},this._actualWorkerPoolSize=1,this._doneThreadSet=new Set,this.onTaskDone=e=>{this._doneThreadSet.add(e),this._actualWorkerPoolSize=Math.min(howManyWorker(this._doneThreadSet.size),this._max_pool_size),this._run()},this._max_pool_size=e>8?8:e,envIsDevtools=t}runTask(e,t,s=(()=>{})){return new Promise((r,i)=>{const o={name:e,data:t,resolve:r,reject:i,retryTimes:0,onStatusUpdate:s};s(config_1.ETaskStatus.waiting),this._taskQueue.push(o),this._run()})}abort(e){const t=[],s=[];this._taskQueue.forEach(r=>{r.name!==e?s.push(r):t.push(r)}),t.forEach(e=>{e.reject(exports.AbortEvent)}),this._taskQueue=s}_run(){let e=this._taskQueue[0];for(;e;){const t=this.allocWorker();if(!t)return;this._taskQueue.shift(),t.runTask(e),e=this._taskQueue[0]}}allocWorker(){for(const e in this._workerPool){const t=this._workerPool[e];if(t.status===config_1.EWorkerStatus.free)return t}if(this.workerCount()<this._max_pool_size){const e=new WorkerInstance;return e.on("taskDone",this.onTaskDone),e.on("exit",this.onWorkerExit),this._workerPool[e.threadId]=e,e}return null}workerCount(){return Object.keys(this._workerPool).length}}function howManyWorker(e){switch(e){case 1:return 4;case 2:return 6;default:return 8}}const getWorkerManager=function(e=!1){if(!workerManagerInstance){let t=parseInt(process.env.COMPILE_THREADS||"0",10);(t<=0||t>cpus)&&(t=cpus),workerManagerInstance=new WorkerManager(t,e)}return workerManagerInstance};exports.getWorkerManager=getWorkerManager;
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.logger=void 0;const worker_thread_1=require("../worker_thread"),env_1=require("./env");function pad(e,t=2){return e>10**t?String(e):`${new Array(t).join("0")}${e}`.slice(-t)}const LevelMap={trace:1,info:2,log:3,warn:4,error:5};class Logger{constructor(e){this.output=e,this.enableLevel=LevelMap.log,this.ready=!1,this.logs=[]}setOutput(e){this.output=e}setEnableLevel(e){this.enableLevel=e}setReady(){if(this.ready=!0,this.logs.length>0)for(const e of this.logs)this.output(e.level,e.args)}send(e,t){this.ready?this.output(e,t):this.logs.push({level:e,args:t})}getPrintTime(){const e=new Date;return`${String(e.getFullYear())}-${pad(e.getMonth()+1)}-${pad(e.getDate())} ${pad(e.getHours())}:${pad(e.getMinutes())}:${pad(e.getSeconds())}.${pad(e.getMilliseconds(),3)}`}_receive(e,t,r){const s=LevelMap[e];this.enableLevel>s||(r=[t=`[compiler][${env_1.processEnv}][${this.getPrintTime()}] ${t}`,...r],this.send(e,r))}info(e,...t){this._receive("info",e,t)}log(e,...t){this._receive("log",e,t)}error(e,...t){this._receive("error",e,t)}}if(exports.logger=new Logger((e,t)=>{console[e](...t)}),exports.logger.setReady(),"ci"===env_1.hostEnv&&("compiler"===process.env.debug?exports.logger.setEnableLevel(LevelMap.info):exports.logger.setEnableLevel(LevelMap.warn)),"childprocess"===env_1.processEnv&&exports.logger.setOutput((e,t)=>{process.send({command:worker_thread_1.COMMAND.SEND_LOG,data:{level:e,args:t}})}),"workerthread"===env_1.processEnv){const e=require("worker_threads").parentPort;e&&exports.logger.setOutput((t,r)=>{e.postMessage({command:worker_thread_1.COMMAND.SEND_LOG,data:{level:t,args:r}})})}
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.packNpmManually=exports.packNpm=void 0;const tslib_1=require("tslib"),config_1=require("../../config"),lodash_1=tslib_1.__importDefault(require("lodash")),rimraf_1=tslib_1.__importDefault(require("rimraf")),path_1=tslib_1.__importDefault(require("path")),fs_1=tslib_1.__importDefault(require("fs")),glob_1=tslib_1.__importDefault(require("glob")),source_map_1=tslib_1.__importDefault(require("source-map")),filterdeps_1=tslib_1.__importDefault(require("./filterdeps")),locales_1=tslib_1.__importDefault(require("../../utils/locales/locales")),error_1=require("../../utils/error"),log_1=tslib_1.__importDefault(require("../../utils/log")),projectconfig_1=require("../json/projectconfig"),acorn=require("acorn"),NPM_RECORD={start_time:Date.now(),pack_time:0,miniprogram_pack_num:0,other_pack_num:0,warn_not_found_num:0,warn_require_var_num:0,warn_require_rename_num:0,extra1:"",extra2:"",extra3:""},REPORT_LIST=["pack_time","miniprogram_pack_num","other_pack_num","warn_not_found_num","warn_require_var_num","warn_require_rename_num","extra1","extra2","extra3"];function wrap(e,t){return function(...a){if(a.length){const e=a.pop();"function"!=typeof e&&a.push(e)}return new Promise((r,n)=>{a.push((e,t)=>{e?n(e):r(t)}),e.apply(t||null,a)})}}const statSync=wrap(fs_1.default.stat),mkdirSync=wrap(fs_1.default.mkdir),readFileSync=wrap(fs_1.default.readFile),writeFileSync=wrap(fs_1.default.writeFile),accessSync=wrap(fs_1.default.access),renameSync=wrap(fs_1.default.rename),globSync=wrap(glob_1.default);let seed=+new Date;function getId(){return++seed}function startRecord(){NPM_RECORD.start_time=Date.now(),REPORT_LIST.forEach(e=>NPM_RECORD[e]=0===e.indexOf("extra")?"":0)}function endRecord(e){NPM_RECORD.pack_time=Date.now()-NPM_RECORD.start_time;const t={};REPORT_LIST.forEach(e=>t[e]=NPM_RECORD[e]);try{null==e||e(t)}catch(e){}}async function recursiveMkdir(e){const t=path_1.default.posix.dirname(e);try{await accessSync(t)}catch(e){await recursiveMkdir(t)}try{await accessSync(e);const t=await statSync(e);t&&!t.isDirectory()&&(await renameSync(e,e+".bak"),log_1.default.warn(e+" already exists but is not a directory, so it will be rename to a file with the suffix ending in '.bak'"),await mkdirSync(e))}catch(t){await mkdirSync(e)}}async function copyFile(e,t){const a=await readFileSync(e);await writeFile(a,t)}async function writeFile(e,t){await recursiveMkdir(path_1.default.posix.dirname(t)),await writeFileSync(t,e)}function walkNode(e,t){t(e),Object.keys(e).forEach(a=>{const r=e[a];Array.isArray(r)&&r.forEach(e=>{(null==e?void 0:e.type)&&walkNode(e,t)}),(null==r?void 0:r.type)&&walkNode(r,t)})}function parseDeps(e,t,a){const r=[];let n,o=[];try{n=acorn.parse(e,{sourceType:"module",locations:!0,allowHashBang:!0,onComment(t,a,r,n){t||"#"!==e[r]||o.push({start:r,end:n,adjustContent:""})}})}catch(e){const a=`parse js file (${t}) failed: `+e.message;throw console.error(a),e.message=a,e}return walkNode(n,n=>{const i=n.callee,s=n.arguments;if("CallExpression"===n.type&&i&&"Identifier"===i.type&&"require"===i.name&&s&&1===s.length&&("Literal"===s[0].type?r.push(s[0].value):(a.push({jsPath:t,code:e.substring(n.start,n.end),startLine:n.loc.start.line,endLine:n.loc.end.line,tips:"require variable is not allowed",msg:locales_1.default.config.NOT_ALLOWED_REQUIRE_VAR.format()}),NPM_RECORD.warn_require_var_num++)),"ExpressionStatement"===n.type&&n.expression&&"use strict"===n.expression.value&&o.push({start:n.start,end:n.end,adjustContent:""}),"ImportDeclaration"===n.type){const e=n.source,t=n.specifiers,a={start:n.start,end:n.end,adjustContent:""},i=[];e&&"Literal"===e.type&&(r.push(e.value),i.push(`var __TEMP__ = require('${e.value}');`)),t&&Array.isArray(t)&&t.forEach(e=>{if("ImportSpecifier"===e.type){const t=e.local,a=e.imported;"Identifier"===t.type&&"Identifier"===a.type&&i.push(`var ${t.name} = __TEMP__['${a.name}'];`)}else if("ImportDefaultSpecifier"===e.type){const t=e.local;"Identifier"===t.type&&i.push(`var ${t.name} = __REQUIRE_DEFAULT__(__TEMP__);`)}else if("ImportNamespaceSpecifier"===e.type){const t=e.local;"Identifier"===t.type&&i.push(`var ${t.name} = __REQUIRE_WILDCARD__(__TEMP__);`)}}),a.adjustContent=i.join(""),o.push(a)}if("ExportNamedDeclaration"===n.type){const t=n.source,a=n.specifiers,i=n.declaration;let s=!1;const p={start:n.start,end:n.end,adjustContent:""},c=['if (!exports.__esModule) Object.defineProperty(exports, "__esModule", { value: true });'];if(t&&"Literal"===t.type&&(r.push(t.value),c.push(`var __TEMP__ = require('${t.value}');`),s=!0),i){if("VariableDeclaration"===i.type){const t=i.declarations;t&&Array.isArray(t)&&t.forEach(t=>{if("VariableDeclarator"===t.type){const a=t.id,r=t.init;a&&"Identifier"===a.type&&(p.notAddLines=!0,c.push(`var ${a.name} = exports.${a.name} = ${r?e.substring(r.start,r.end):"undefined"};`))}})}else if("FunctionDeclaration"===i.type){const t=i.id;t&&"Identifier"===t.type&&(p.notAddLines=!0,c.push(`${e.substring(i.start,i.end)};exports.${t.name} = ${t.name}`))}else if("ClassDeclaration"===i.type){const t=i.id;t&&"Identifier"===t.type&&(p.notAddLines=!0,c.push(`${e.substring(i.start,i.end)};exports.${t.name} = ${t.name}`))}}else;a&&Array.isArray(a)&&a.forEach(e=>{if("ExportSpecifier"===e.type){const t=e.local,a=e.exported;"Identifier"===t.type&&"Identifier"===a.type&&c.push(`Object.defineProperty(exports, '${a.name}', { enumerable: true, configurable: true, get: function() { return ${s?"__TEMP__.":""}${t.name}; } });`)}}),p.adjustContent=c.join(""),o.push(p)}else if("ExportAllDeclaration"===n.type){const e=n.source,t={start:n.start,end:n.end,adjustContent:""},a=['if (!exports.__esModule) Object.defineProperty(exports, "__esModule", { value: true });'];e&&"Literal"===e.type&&(r.push(e.value),a.push(`var __TEMP__ = require('${e.value}');`)),a.push('Object.keys(__TEMP__).forEach(function(k) { if (k === "default" || k === "__esModule") return; Object.defineProperty(exports, k, { enumerable: true, configurable: true, get: function() { return __TEMP__[k]; } }); });'),t.adjustContent=a.join(""),o.push(t)}else if("ExportDefaultDeclaration"===n.type){const t=n.declaration,a={start:n.start,end:n.end,adjustContent:""},r=['if (!exports.__esModule) Object.defineProperty(exports, "__esModule", { value: true });'];if(t)if(a.notAddLines=!0,t.id){const a=t.id;r.push(`${e.substring(t.start,t.end)};exports.default = ${a.name}`)}else r.push(`exports.default = ${e.substring(t.start,t.end)};`);a.adjustContent=r.join(""),o.push(a)}const p=n.expression;"ExpressionStatement"===n.type&&p&&"AssignmentExpression"===p.type&&"Identifier"===p.right.type&&"require"===p.right.name&&(a.push({jsPath:t,code:e.substring(n.start,n.end),startLine:n.loc.start.line,endLine:n.loc.end.line,tips:"assign require function to a variable is not allowed",msg:locales_1.default.config.NOT_ALLOWED_REQUIRE_ASSIGN.format()}),NPM_RECORD.warn_require_rename_num++);const c=n.declarations;"VariableDeclaration"===n.type&&c.length>0&&c.forEach(r=>{const o=r.init;"VariableDeclarator"===r.type&&o&&"Identifier"===o.type&&"require"===o.name&&(a.push({jsPath:t,code:e.substring(n.start,n.end),startLine:n.loc.start.line,endLine:n.loc.end.line,tips:"assign require function to a variable is not allowed",msg:locales_1.default.config.NOT_ALLOWED_REQUIRE_ASSIGN.format()}),NPM_RECORD.warn_require_rename_num++)})}),o=o.sort((e,t)=>t.start-e.start),o.forEach(t=>{const a=e.substring(t.start,t.end),r=t.notAddLines?0:a.split("\n").length;e=e.substring(0,t.start)+t.adjustContent+new Array(r).join("\n")+e.substring(t.end)}),{deps:r,parsedContent:e}}async function parseJs(e,t,a,r,n,o){if(n[t=path_1.default.posix.normalize(t)])return n[t];const i=await readFileSync(t,"utf8"),s=getId(),p=path_1.default.posix.relative(e,t);if(/\.json$/.test(t)){const e={id:s,name:p,content:"module.exports = "+i,deps:[],depsMap:{}};return n[t]=s,r.push(e),s}const{deps:c,parsedContent:_}=parseDeps(i,t,o),u={id:s,name:p,content:_,deps:c,depsMap:{}};n[t]=s,r.push(u);for(const i of c){let s,p=path_1.default.posix.join(path_1.default.posix.dirname(t),i);if(!/\.js$/.test(p)&&!/\.json$/.test(p)){const e=p+".js";try{await accessSync(e),p=e}catch(e){}}try{const e=await statSync(p);(null==e?void 0:e.isDirectory())&&(p=path_1.default.posix.join(p,"index.js"))}catch(e){}/\.js$/.test(p)||/\.json$/.test(p)||(p+=".js");try{await accessSync(p),s=await parseJs(e,p,a,r,n,o)}catch(e){}s&&(u.depsMap[i]=s)}return s}function addJsToMap(e,t,a,r){const n=t.split("\n").length;for(let o=1;o<=n;o++)e.addMapping({generated:{line:r+o,column:0},original:{line:o,column:0},source:a}),e.setSourceContent(a,t)}function findOutsideDeps(e){const t=new Set;return e.forEach(e=>{e.deps.forEach(a=>{Object.keys(e.depsMap).includes(a)||t.add(a)})}),Array.from(t)}async function packJs(e,t,a){try{const t=await statSync(e);(null==t?void 0:t.isDirectory())&&(e=path_1.default.posix.join(e,"index.js"))}catch(e){}/\.js$/.test(e)||/\.json$/.test(e)||(e+=".js");try{await accessSync(e)}catch(t){return a.push({jsPath:e,code:"",tips:"entry file is not found",msg:locales_1.default.config.NOT_FOUND_NPM_ENTRY.format()}),void NPM_RECORD.warn_not_found_num++}const r=new source_map_1.default.SourceMapGenerator({file:"index.js"}),n=[];await parseJs(path_1.default.posix.dirname(e),e,t,n,{},a);const o=findOutsideDeps(n),i=["module.exports = (function() {","var __MODS__ = {};","var __DEFINE__ = function(modId, func, req) { var m = { exports: {}, _tempexports: {} }; __MODS__[modId] = { status: 0, func: func, req: req, m: m }; };",'var __REQUIRE__ = function(modId, source) { if(!__MODS__[modId]) return require(source); if(!__MODS__[modId].status) { var m = __MODS__[modId].m; m._exports = m._tempexports; var desp = Object.getOwnPropertyDescriptor(m, "exports"); if (desp && desp.configurable) Object.defineProperty(m, "exports", { set: function (val) { if(typeof val === "object" && val !== m._exports) { m._exports.__proto__ = val.__proto__; Object.keys(val).forEach(function (k) { m._exports[k] = val[k]; }); } m._tempexports = val }, get: function () { return m._tempexports; } }); __MODS__[modId].status = 1; __MODS__[modId].func(__MODS__[modId].req, m, m.exports); } return __MODS__[modId].m.exports; };',"var __REQUIRE_WILDCARD__ = function(obj) { if(obj && obj.__esModule) { return obj; } else { var newObj = {}; if(obj != null) { for(var k in obj) { if (Object.prototype.hasOwnProperty.call(obj, k)) newObj[k] = obj[k]; } } newObj.default = obj; return newObj; } };","var __REQUIRE_DEFAULT__ = function(obj) { return obj && obj.__esModule ? obj.default : obj; };"];if(n.length){const e=n.shift();i.push(`__DEFINE__(${e.id}, function(require, module, exports) {`),addJsToMap(r,e.content,e.name,i.length),i.push(e.content),i.push(`}, function(modId) {var map = ${JSON.stringify(e.depsMap)}; return __REQUIRE__(map[modId], modId); })`);for(const e of n)i.push(`__DEFINE__(${e.id}, function(require, module, exports) {`),addJsToMap(r,e.content,e.name,i.length),i.push(e.content),i.push(`}, function(modId) { var map = ${JSON.stringify(e.depsMap)}; return __REQUIRE__(map[modId], modId); })`);i.push(`return __REQUIRE__(${e.id});`)}return i.push("})()"),i.push("//miniprogram-npm-outsideDeps="+JSON.stringify(o)),i.push("//# sourceMappingURL=index.js.map"),{js:i.join("\n"),map:r.toString()}}async function checkIsMiniprogramPack(e,t){let a="miniprogram_dist";t.miniprogram&&"string"==typeof t.miniprogram&&(a=t.miniprogram);try{const t=path_1.default.posix.join(e,a);await accessSync(t);const r=await statSync(t);if(null==r?void 0:r.isDirectory())return t}catch(e){}return""}async function packNpm(e,t={}){const a=e.projectPath,r=e.type,n=await(0,projectconfig_1.getProjectConfigJSON)(e),o=n.pluginRoot||"",i=n.miniprogramRoot||"",s="miniProgramPlugin"===r||"miniGamePlugin"===r,{ignores:p,reporter:c}=t;if(!a)throw new error_1.CodeError(locales_1.default.config.SHOULD_NOT_BE_EMPTY.format("projectPath"),config_1.PARAM_ERROR);if("miniProgramPlugin"===r&&!o)throw new error_1.CodeError(locales_1.default.config.SHOULD_NOT_BE_EMPTY.format('project.config.json "pluginRoot"'),config_1.PARAM_ERROR);startRecord();const _=path_1.default.isAbsolute(a)?a:path_1.default.posix.join(process.cwd(),a),u=[{searchRoot:path_1.default.posix.join(_,i),paths:await globSync("**/package.json",{cwd:path_1.default.posix.join(_,i),nodir:!0,dot:!0,ignore:(p||[]).concat("**/node_modules/**")})}];s&&u.push({searchRoot:path_1.default.posix.join(_,o),paths:await globSync("**/package.json",{cwd:path_1.default.posix.join(_,o),nodir:!0,dot:!0,ignore:(p||[]).concat("**/node_modules/**")})});lodash_1.default.flattenDeep(u.map(e=>e.paths.map(t=>path_1.default.posix.join(e.searchRoot,path_1.default.dirname(t),"miniprogram_npm")))).forEach(e=>rimraf_1.default.sync(e));const l=[];for(const e of u)for(const t of e.paths){const a=path_1.default.dirname(path_1.default.posix.join(e.searchRoot,t)),r=lodash_1.default.xorWith(e.paths,[t]).map(e=>path_1.default.posix.join(path_1.default.posix.dirname(e),"/**")).filter(e=>"**"!==e);let n=await globSync("**/package.json",{cwd:a.replace(/\\/g,"/"),nodir:!0,dot:!0,ignore:(p||[]).concat(r).concat(["node_modules/@types/**"])});if(n=await(0,filterdeps_1.default)(a,n),n&&n.length)for(const e of n){const t=path_1.default.posix.join(a,e);let r=await readFileSync(t,"utf8");const n=path_1.default.dirname(t);let o=n.replace(/([\b/\\])node_modules([\b/\\])/g,(e,t,a)=>`${t}miniprogram_npm${a}`),i=path_1.default.basename(o);const s=i.split("@");s.length&&(i=s.pop()||""),o=o.replace(path_1.default.basename(o),i),r=JSON.parse(r);const p=path_1.default.posix.join(n,r.main||"index.js"),c=await checkIsMiniprogramPack(n,r);if(c){const e=await globSync("**/*",{cwd:c.replace(/\\/g,"/"),nodir:!0,dot:!0,ignore:"**/node_modules/**"});for(const t of e)await copyFile(path_1.default.posix.join(c,t),path_1.default.posix.join(o,t));NPM_RECORD.miniprogram_pack_num++}else{const e=await packJs(p,o,l);if(!e)continue;await writeFile(e.js,path_1.default.posix.join(o,"./index.js")),await writeFile(e.map,path_1.default.posix.join(o,"./index.js.map")),NPM_RECORD.other_pack_num++}}}if(endRecord(c),NPM_RECORD.miniprogram_pack_num+NPM_RECORD.other_pack_num<=0)throw new Error("__NO_NODE_MODULES__ "+locales_1.default.config.NOT_FOUND_NODE_MODULES.format());return"function"==typeof e.updateFiles&&e.updateFiles(),l}async function packNpmManually(e){let{packageJsonPath:t,miniprogramNpmDistDir:a}=e;const r=e.ignores||[],n={pack_time:0,miniprogram_pack_num:0,other_pack_num:0,warn_not_found_num:0,warn_require_var_num:0,warn_require_rename_num:0},o=[];if(!a)throw new Error("param miniprogramNpmDistDir is required");if(!t)throw new Error("param packageJsonPath is required");if(path_1.default.isAbsolute(a)||(a=path_1.default.join(process.cwd(),a)),path_1.default.isAbsolute(t)||(t=path_1.default.join(process.cwd(),t)),!fs_1.default.existsSync(t))throw new Error(`param packageJsonPath: ${t} file is not exited`);const i=path_1.default.dirname(t);let s=await globSync("**/package.json",{cwd:i.replace(/\\/g,"/"),nodir:!0,dot:!0,ignore:(r||[]).concat(["node_modules/@types/**"])});if(s=await(0,filterdeps_1.default)(i,s),!s||!s.length)return log_1.default.warn("No miniprogram_npm package was built."),{miniProgramPackNum:0,otherNpmPackNum:0,warnList:[]};s=s.filter(e=>e.startsWith("node_modules"));for(const e of s){const t=path_1.default.posix.join(i,e);let r=await readFileSync(t,"utf8");const s=path_1.default.dirname(t);let p=s.replace(/([\b/\\])node_modules([\b/\\])/g,(e,t,a)=>`${t}miniprogram_npm${a}`);p=path_1.default.posix.normalize(path_1.default.posix.join(a,"miniprogram_npm",p.split(/[/\\]miniprogram_npm[/\\]/)[1]));let c=path_1.default.basename(p);const _=c.split("@");_.length&&(c=_.pop()||""),p=p.replace(path_1.default.basename(p),c),r=JSON.parse(r);const u=path_1.default.posix.join(s,r.main||"index.js"),l=await checkIsMiniprogramPack(s,r);if(l){const e=await globSync("**/*",{cwd:l.replace(/\\/g,"/"),nodir:!0,dot:!0,ignore:"**/node_modules/**"});for(const t of e)await copyFile(path_1.default.posix.join(l,t),path_1.default.posix.join(p,t));n.miniprogram_pack_num++}else{const e=await packJs(u,p,o);if(!e)continue;await writeFile(e.js,path_1.default.posix.join(p,"./index.js")),await writeFile(e.map,path_1.default.posix.join(p,"./index.js.map")),n.other_pack_num++}}return{miniProgramPackNum:n.miniprogram_pack_num,otherNpmPackNum:n.other_pack_num,warnList:o}}exports.packNpm=packNpm,exports.packNpmManually=packNpmManually;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";function findAllDescendant(e,t){let n;n=Array.isArray(e)?e:[e];const s=n.slice(0),o=[],a=[];for(;s.length>0;){const e=s.pop();o.push(e),e.childModules.forEach(e=>{s.indexOf(e)<0&&o.indexOf(e)<0&&(s.push(e),e.type===t&&a.push(e))})}return a}function findModsByType(e,t){var n;return(null===(n=null==e?void 0:e.graph)||void 0===n?void 0:n.modules.filter(e=>e.type===t))||[]}function findModByTypeAndPath(e,t,n){var s;return null===(s=e.graph)||void 0===s?void 0:s.modules.find(e=>e.type===t&&e.path===n)}function findPageMods(e,t){return(findModsByType(e,"Page")||[]).filter(e=>t.includes(e.path.replace(/\.json$/,"")))}function findGlobalCompMods(e){const t=findModByTypeAndPath(e,"MainPackage","app.json");return(null==t?void 0:t.childModules.filter(e=>"Component"===e.type))||[]}function getAllSubpackagePath(e){return findModsByType(e,"SubPackage").map(e=>e.path)}function innerGetSortedJSFiles(e,t){const n=new Set,s=new Set,o=new Set,a=[];for(const t of e){const e=t.findChild("Js");e&&(t.config?o.add(e.path):s.add(e.path),n.add(e.path),a.push(e))}const i=findAllDescendant(e.concat(t),"Component");for(const e of i.concat(t)){const t=e.findChild("Js");t&&(o.add(t.path),n.add(t.path),a.push(t))}const l=findAllDescendant(a,"Js");for(const e of l){const t=e.path;n.add(t)}return{allFiles:Array.from(n),pageFiles:Array.from(s),componentFiles:Array.from(o)}}function groupBySubpackage(e,t){const n=[],s={};for(const o of e){const e=t.find(e=>o.startsWith(e));e?(s[e]||(s[e]=[]),s[e].push(o)):n.push(o)}return{main:n,subs:s}}function partialGetSubPkgSortedJSFiles(e,t,n){const s=findPageMods(e,t);let{allFiles:o,pageFiles:a,componentFiles:i}=innerGetSortedJSFiles(s,[]);const l=getAllSubpackagePath(e);return{allFiles:groupBySubpackage(o,l).subs[n]||[],pageFiles:groupBySubpackage(a,l).subs[n]||[],componentFiles:groupBySubpackage(i,l).subs[n]||[]}}function partialGetMainPkgSortedJSFiles(e,t){const n=findPageMods(e,t),s=findGlobalCompMods(e);let{allFiles:o,pageFiles:a,componentFiles:i}=innerGetSortedJSFiles(n,s),l=!1;const p=findModByTypeAndPath(e,"Js","app.js");if(p){l=!0,o.push(p.path);const e=findAllDescendant([p],"Js");for(const t of e){const e=t.path;o.indexOf(e)<0&&o.push(e)}}const r=getAllSubpackagePath(e);return{hasAppJS:l,allFiles:groupBySubpackage(o,r).main,pageFiles:groupBySubpackage(a,r).main,componentFiles:groupBySubpackage(i,r).main}}function partialWholePkgGetSortedJSFiles(e,t){const n=findPageMods(e,t),s=findGlobalCompMods(e);let{allFiles:o,pageFiles:a,componentFiles:i}=innerGetSortedJSFiles(n,s),l=!1;const p=findModByTypeAndPath(e,"Js","app.js");if(p){l=!0,o.push(p.path);const e=findAllDescendant([p],"Js");for(const t of e){const e=t.path;o.indexOf(e)<0&&o.push(e)}}return{hasAppJS:l,allFiles:o,pageFiles:a,componentFiles:i}}function innerGetResourceFiles(e,t){let n=[];for(const s of e){const e=findAllDescendant(s,t);n=n.concat(e.map(e=>e.path))}return n}function partialGetWxmlAndWxsFiles(e,t,n){const s=findPageMods(e,t),o=findGlobalCompMods(e),a=innerGetResourceFiles(s.concat(o),"Wxml"),i=innerGetResourceFiles(s.concat(o),"Wxs"),l=getAllSubpackagePath(e),p=groupBySubpackage(a,l),r=groupBySubpackage(i,l);return{wxmlFiles:n?p.main.concat(p.subs[n]||[]):p.main,wxsFiles:n?r.main.concat(r.subs[n]||[]):r.main}}function partialGetWxssFiles(e,t,n){const s=findPageMods(e,t),o=findGlobalCompMods(e),a=innerGetResourceFiles(s.concat(o),"Wxss"),i=findModByTypeAndPath(e,"Wxss","app.wxss");if(i&&!a.includes(i.path)){a.push(i.path);const e=findAllDescendant(i,"Wxss");for(const t of e)a.includes(t.path)||a.push(t.path)}const l=groupBySubpackage(a,getAllSubpackagePath(e));return n?l.main.concat(l.subs[n]||[]):l.main}function partialGetCodeFiles(e,t){const n=new Set,s=findModByTypeAndPath(e,"MainPackage","app.json");n.add("app.json");const o=new Set;function a(e){switch(o.add(e),e.type){case"Page":case"Component":case"Wxml":case"Wxss":case"Wxs":case"Js":case"Config":n.add(e.path)}for(const t of e.childModules)o.has(t)||a(t)}for(const e of null==s?void 0:s.childModules)if("Page"===e.type)t.includes(e.path.replace(/\.json$/,""))&&a(e);else if("SubPackage"===e.type)for(const n of e.childModules)t.includes(n.path.replace(/\.json$/,""))&&a(n);else a(e);return console.log("[partial-compile] compile",n),Array.from(n)}Object.defineProperty(exports,"__esModule",{value:!0}),exports.partialGetCodeFiles=exports.partialGetWxssFiles=exports.partialGetWxmlAndWxsFiles=exports.partialWholePkgGetSortedJSFiles=exports.partialGetMainPkgSortedJSFiles=exports.partialGetSubPkgSortedJSFiles=exports.findAllDescendant=void 0,exports.findAllDescendant=findAllDescendant,exports.partialGetSubPkgSortedJSFiles=partialGetSubPkgSortedJSFiles,exports.partialGetMainPkgSortedJSFiles=partialGetMainPkgSortedJSFiles,exports.partialWholePkgGetSortedJSFiles=partialWholePkgGetSortedJSFiles,exports.partialGetWxmlAndWxsFiles=partialGetWxmlAndWxsFiles,exports.partialGetWxssFiles=partialGetWxssFiles,exports.partialGetCodeFiles=partialGetCodeFiles;
}(require("licia/lazyImport")(require), require)
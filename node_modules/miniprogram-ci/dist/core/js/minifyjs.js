"use strict";const config_1=require("../../config"),_Terser=()=>require("terser"),_UglifyJS=()=>require("uglify-js"),_babelCodeFrame=()=>require("babel-code-frame"),minify=e=>{const{code:r,inputSourceMap:o,useTerser:i,filePath:n}=e;let c;const s=o?{includeSources:!0,content:o,filename:n}:{includeSources:!0,content:void 0,filename:n};if(c=i?require("terser").minify(r,{output:{comments:!1},toplevel:!0,compress:{drop_console:!1,drop_debugger:!1},sourceMap:s}):require("uglify-js").minify(r,{toplevel:!0,sourceMap:s}),c.error){const e=c.error;return{error:{message:`file: ${n}\n ${c.error.message}\n ${require("babel-code-frame")(r,e.line,e.col>0?e.col:1)}`,code:config_1.UGLIFY_JS_ERR}}}return c};module.exports=minify;
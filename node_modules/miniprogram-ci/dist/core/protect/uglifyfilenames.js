!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.uglifyFileNames=exports.getGameNameMapping=exports.getNameMapping=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),config_1=require("../../config"),error_1=require("../../utils/error"),tools_1=require("../../utils/tools"),game_1=tslib_1.__importDefault(require("../json/game")),app_1=require("../json/app"),file_flatter_1=require("./file_flatter"),url_config_1=require("../../utils/url_config"),request_1=require("../../utils/request"),sign_1=require("../../utils/sign"),jsonParse_1=require("../../utils/jsonParse"),locales_1=tslib_1.__importDefault(require("../../utils/locales/locales")),config=tslib_1.__importStar(require("../../config")),common_1=require("../../utils/common");function checkPrefix(e,t){for(const a of t)if(0===e.indexOf(a))return a;return""}const getNameMapping=async(e,t)=>{if(process.env.isDevtools)return e.nameMappingFromDevtools||{};{const a=e.getFileList(t,".js").map(e=>path_1.default.posix.relative(t,e));try{let r={};if("miniProgram"===e.type){const o=await(0,app_1.getAppJSON)(e);r=await getMiniProgramNameMapping(e,t,o,a)}if("miniGame"===e.type){const t=await(0,game_1.default)(e);r=await(0,exports.getGameNameMapping)(e,t,a)}return r}catch(e){throw new error_1.CodeError(e.toString(),config_1.CODE_PROTECT_TRANSLATE_FILENAME)}}};exports.getNameMapping=getNameMapping;const getMiniProgramNameMapping=async(e,t,a,r)=>{const o=[{type:"file",value:"app.js"},{type:"regex",value:/\/miniprogram_npm\/|^miniprogram_npm\//},{type:"folder",value:"functional-pages/"}],i=e.getFileList(t,".wxml").map(e=>path_1.default.posix.relative(t,e));for(const e of i)o.push({type:"file",value:""+e.replace(/\.wxml$/,".js")});let s=[];return a.subPackages&&(s=a.subPackages.map(e=>e.root)),a.widgets&&a.widgets.length>0&&a.widgets.forEach(e=>{o.push({type:"folder",value:/\/$/.test(e.path)?e.path:e.path+"/"})}),a.workers&&o.push({type:"folder",value:(0,tools_1.getWorkersPath)(a.workers)}),a.openDataContext&&(o.push({type:"file",value:path_1.default.posix.join(a.openDataContext,"index.js")}),s.push(a.openDataContext)),await _getNameMapping(e,r,o,s)},getGameNameMapping=async(e,t,a)=>{const r=[{type:"file",value:"game.js"},{type:"regex",value:/\/miniprogram_npm\/|^miniprogram_npm\//}],o=[];if(t.subPackages&&t.subPackages.forEach(e=>{const t=e.root.replace(/^\//,"");/\.js$/.test(t)?(r.push({type:"file",value:t}),o.push(path_1.default.posix.dirname(t))):(r.push({type:"file",value:path_1.default.posix.join(t,"./game.js")}),o.push(t))}),t.openDataContext&&(r.push({type:"file",value:path_1.default.posix.join(t.openDataContext,"index.js")}),o.push(t.openDataContext)),t.workers&&r.push({type:"folder",value:(0,tools_1.getWorkersPath)(t.workers)}),t.plugins)for(const e in t.plugins){const a=t.plugins[e];if(a.path){const e=a.path.replace(/^\//,"");r.push({type:"folder",value:e})}}return await _getNameMapping(e,a,r,o)};async function _getNameMapping(e,t,a,r=[]){const o={},i=[];for(const e of t){let t=!1;for(const r of a)if("file"===r.type&&r.value===e||"folder"===r.type&&0===e.indexOf(r.value)||"regex"===r.type&&r.value.test(e)){t=!0;break}t||i.push(e)}const s=await(0,sign_1.getSignature)(e.privateKey,e.appid),{body:n}=await(0,request_1.request)({url:url_config_1.TRANSLATE_FILENAME,method:"post",body:JSON.stringify({appid:e.appid,signature:s,arrPaths:i}),headers:{"content-type":"application/json"}}),p=(0,jsonParse_1.jsonRespParse)(n,url_config_1.TRANSLATE_FILENAME);if(0===p.errCode)return p.body.pairs.forEach(e=>{const t=checkPrefix(e.origin,r);o[e.origin]=(0,tools_1.normalizePath)(path_1.default.posix.join(t,e.translated+".js"))}),o;throw new Error(`errCode: ${p.errCode} errMsg: ${p.errMsg}`)}function genResolveAlias(e){if(e){const t=[];return Object.keys(e).forEach(a=>{let r=a;a.endsWith("*")&&(r=r.slice(0,-1));let o=e[a];e[a].endsWith("*")&&(o=o.slice(0,-1)),t.push({key:r,value:o})}),e=>{let a={key:"",value:""},r=!1;if(t.forEach(t=>{e.startsWith(t.key)&&a.key.length<t.key.length&&(a=t,r=!0)}),!r)return;let o=e.replace(a.key,a.value);return"/"===o[0]&&(o=o.slice(1)),o}}return e=>{}}async function uglifyFileNames(e,t,a){let r={miniprogramRoot:""};try{r=JSON.parse(t["project.config.json"].toString())}catch(e){}let o=(0,tools_1.normalizePath)(r.miniprogramRoot);"."===o&&(o=""),a=a||await(0,exports.getNameMapping)(e,e.miniprogramRoot);const i=Object.keys(t).filter(e=>e.endsWith(".js")),s={};let n,p=t[path_1.default.posix.join(o,"app.json")];p?(Buffer.isBuffer(p)&&(p=p.toString("utf-8")),p=JSON.parse(p),n=genResolveAlias(p.resolveAlias)):n=genResolveAlias(void 0);for(const e of i){if(/\/miniprogram_npm\/|^miniprogram_npm\//.test(e))continue;const r=e,p=t[e].toString(),l=t[e+".map"]||"";s[r]=(0,file_flatter_1.tryTranslateSingleFile)({rootPath:o,code:p,nameMapping:a,check:!0,sourceFileName:e,sourceMap:l,filePath:e,miniProgramJSFiles:i,resolveAlias:n})}const l=Object.keys(t),u={};for(const e of l){u[(0,tools_1.normalizePath)(e)]=t[e]}for(const e in s){const t=(0,tools_1.normalizePath)(e);let r=t;const i=s[e];if(i.errMsg){if(!process.env.isDevtools)throw new Error(`\n${locales_1.default.config.COULD_NOT_USE_CODE_PROTECT}\n${i.errMsg}`);(0,common_1.throwError)({code:config.FILE_FLAT_ERR,msg:`${locales_1.default.config.COULD_NOT_USE_CODE_PROTECT}\n${i.errMsg}`,filePath:t})}const n=path_1.default.posix.relative(o,e);a[n]&&(r=(0,tools_1.normalizePath)(path_1.default.posix.join(o,a[n])),delete u[t],delete u[t+".map"]),u[r]=i.translatedContent,i.translatedSourceMap&&(u[r+".map"]=i.translatedSourceMap)}return u}exports.getGameNameMapping=getGameNameMapping,exports.uglifyFileNames=uglifyFileNames;
}(require("licia/lazyImport")(require), require)
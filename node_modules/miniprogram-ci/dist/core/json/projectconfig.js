!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getProjectConfigJSON=void 0;const tslib_1=require("tslib"),lodash_1=require("lodash"),common_1=require("../../utils/common"),common_2=require("./common"),schemaValidate_1=require("../validate/schemaValidate"),cache_1=require("../../utils/cache"),tools_1=require("../../utils/tools"),config_1=require("../../config"),locales_1=tslib_1.__importDefault(require("../../utils/locales/locales")),reactiveCache_1=require("./reactiveCache"),PROJECT_CONFIG_JSON="project.config.json",rootConfig=["svr","client","qcloudRoot","miniprogramRoot","pluginRoot","cloudfunctionRoot","jsserverRoot","testRoot"];function formatPath(o=""){return o&&"/"!==o?(o=(0,tools_1.normalizePath)(o+"/")).replace(/^(\/)*/,"").replace(/\.\.\//g,"").replace(/^\.\//,""):""}const _getProjectConfigJSON=(0,reactiveCache_1.wrapCompileJSONFunc)(cache_1.CACHE_KEY.PROJECT_CONFIG,o=>{if(!o.stat("",PROJECT_CONFIG_JSON))return o.type!==config_1.COMPILE_TYPE.miniGamePlugin&&o.type!==config_1.COMPILE_TYPE.miniProgramPlugin||(0,common_1.throwError)({msg:locales_1.default.config.NOT_FOUND.format("project.config.json"),code:config_1.FILE_NOT_FOUND,filePath:PROJECT_CONFIG_JSON}),{};const e=o.getFile("",PROJECT_CONFIG_JSON),r=(0,common_1.checkUTF8)(e,PROJECT_CONFIG_JSON),t=(0,common_2.checkJSONFormat)(r,PROJECT_CONFIG_JSON),i=(0,schemaValidate_1.schemaValidate)("projectconfig",t);if(i.error.length){const o=i.error.map(o=>"type"===o.errorType||"enum"===o.errorType||"anyOf"===o.errorType?locales_1.default.config.JSON_CONTENT_SHOULD_BE.format([o.errorProperty,o.correctType]):locales_1.default.config.SHOULD_NOT_BE_EMPTY.format([o.requireProperty])).join("\n");(0,common_1.throwError)({msg:o,filePath:PROJECT_CONFIG_JSON})}return o.miniprogramRoot=t.miniprogramRoot||"",o.pluginRoot=t.pluginRoot||"",rootConfig.forEach(o=>{"string"==typeof t[o]?t[o]=formatPath(t[o]):t[o]=""}),o.type!==config_1.COMPILE_TYPE.miniGamePlugin&&o.type!==config_1.COMPILE_TYPE.miniProgramPlugin||t.pluginRoot||(0,common_1.throwError)({msg:locales_1.default.config.SHOULD_NOT_BE_EMPTY.format('["pluginRoot"]'),filePath:PROJECT_CONFIG_JSON}),o.type!==config_1.COMPILE_TYPE.miniGamePlugin&&o.type!==config_1.COMPILE_TYPE.miniProgramPlugin||t.pluginRoot!==t.miniprogramRoot||(0,common_1.throwError)({msg:locales_1.default.config.PLUGIN_PATH_SAME_WITH_MINIPROGRAM.format([t.pluginRoot,t.miniprogramRoot]),filePath:PROJECT_CONFIG_JSON}),t}),getProjectConfigJSON=function(o){return(0,lodash_1.cloneDeep)(_getProjectConfigJSON(o))};exports.getProjectConfigJSON=getProjectConfigJSON;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.ProjectWithMockBuffer=void 0;const tslib_1=require("tslib"),project_1=require("../../ci/project"),tools_1=require("../../utils/tools"),path_1=tslib_1.__importDefault(require("path")),fs_1=tslib_1.__importDefault(require("fs"));class ProjectWithMockBuffer extends project_1.Project{constructor(){super(...arguments),this.mockBuffer={},this.__dirSet=new Set,this.__fileSet=new Set}setMockFileCache(t){Object.entries(t).forEach(([t,e])=>{this.mockBuffer[t]=Buffer.from(e)})}_getTargetPath(t,e){return(0,tools_1.normalizePath)(path_1.default.posix.join(t,e)).replace(/\/$/,"").replace(/^\//,"")}getFile(t,e){const r=this._getTargetPath(t,e),i=(0,tools_1.normalizePath)(path_1.default.posix.join(this.projectPath,r));return this.mockBuffer[r]?this.mockBuffer[r]:fs_1.default.readFileSync(i,null)}stat(t,e){const r=this._getTargetPath(t,e);if(this.mockBuffer[r])return{isFile:!0,isDirectory:!1};if(this.__fileSet.has(r)){return{isFile:!0,isDirectory:!1,size:fs_1.default.statSync(path_1.default.posix.join(this.projectPath,r)).size}}return this.__dirSet.has(r)?{isFile:!1,isDirectory:!0}:void 0}}exports.ProjectWithMockBuffer=ProjectWithMockBuffer;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getGameLocalPluginJSON=exports.getDevPluginJSON=exports.checkWorkers=exports.checkComponentPath=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),projectconfig_1=require("../projectconfig"),locales_1=tslib_1.__importDefault(require("../../../utils/locales/locales")),common_1=require("../common"),config_1=require("../../../config"),schemaValidate_1=require("../../validate/schemaValidate"),tools_1=require("../../../utils/tools"),cache_1=require("../../../utils/cache"),common_2=require("../../../utils/common"),reactiveCache_1=require("../reactiveCache");function checkComponentPath(o,e){const{project:t,root:c,filePath:i}=o;void 0!==e.usingComponents&&(0,common_1.checkComponentPath)({project:t,root:c,relativePath:path_1.default.posix.relative(c,i),inputJSON:e})}function checkPublicComponentsAndPages(o,e){const{project:t,root:c,filePath:i}=o,{publicComponents:n,pages:r}=e,a={};if(n){const o=[];for(const r in n){const n=e.publicComponents[r];(0,common_2.checkPath)({value:n,tips:`["publicComponents"]["${r}"]`,filePath:i,code:config_1.PLUGIN_JSON_CONTENT_ERR});let l=t.stat(c,n+".wxml");l&&!l.isDirectory||o.push(locales_1.default.config.CORRESPONDING_FILE_NOT_FOUND.format(`["publicComponents"]["${n}"]`,n+".wxml")),l=t.stat(c,n+".js"),l&&!l.isDirectory||o.push(locales_1.default.config.CORRESPONDING_FILE_NOT_FOUND.format(`["publicComponents"]["${n}"]`,n+".js")),l=t.stat(c,n+".json"),l&&!l.isDirectory||o.push(locales_1.default.config.CORRESPONDING_FILE_NOT_FOUND.format(`["publicComponents"]["${n}"]`,n+".json")),a[r]=!0}o.length>0&&(0,common_2.throwError)({msg:o.join("\n"),code:config_1.PLUGIN_JSON_CONTENT_ERR,filePath:i})}if(r){const o=[];for(const e in r){const n=r[e];(0,common_2.checkPath)({value:n,tips:`["pages"]["${e}"]`,filePath:i,code:config_1.PLUGIN_JSON_CONTENT_ERR});let l=t.stat(c,n+".wxml");l&&!l.isDirectory||o.push(locales_1.default.config.CORRESPONDING_FILE_NOT_FOUND.format(`["pages"]["${n}"]`,n+".wxml")),l=t.stat(c,n+".js"),l&&!l.isDirectory||o.push(locales_1.default.config.CORRESPONDING_FILE_NOT_FOUND.format(`["pages"]["${n}"]`,n+".js")),l=t.stat(c,n+".json"),l&&!l.isDirectory||o.push(locales_1.default.config.CORRESPONDING_FILE_NOT_FOUND.format(`["pages"]["${n}"]`,n+".json")),a[e]&&o.push(locales_1.default.config.SAME_KEY_PAGE_PUBLICCOMPONENTS.format(`"${e}"`)),a[e]=!0}o.length>0&&(0,common_2.throwError)({msg:o.join("\n"),code:config_1.PLUGIN_JSON_CONTENT_ERR,filePath:i})}}function checkWorkers(o,e){const{project:t,root:c,filePath:i}=o,{workers:n}=e;if(void 0===n)return;const r='["workers"]';""===n&&(0,common_2.throwError)({msg:locales_1.default.config.JSON_CONTENT_SHOULD_BE.format(r,locales_1.default.config.DIRECTORY),filePath:i}),(0,common_2.checkPath)({value:n,tips:r,filePath:i});const a=t.stat(c,n);a&&a.isDirectory||(0,common_2.throwError)({msg:locales_1.default.config.JSON_CONTENT_SHOULD_BE.format([r,locales_1.default.config.DIRECTORY]),filePath:i}),e.workers=(0,tools_1.normalizePath)(n+"/")}function checkMain(o,e){const{project:t,root:c,filePath:i}=o,{main:n}=e;if(void 0===n)return;""===n&&(0,common_2.throwError)({msg:locales_1.default.config.SHOULD_NOT_BE_EMPTY.format('["main"]'),code:config_1.PLUGIN_JSON_CONTENT_ERR,filePath:i}),(0,common_2.checkPath)({value:n,tips:'["main"]',filePath:i,code:config_1.PLUGIN_JSON_CONTENT_ERR});const r=t.stat(c,n);r&&!r.isDirectory||(0,common_2.throwError)({msg:locales_1.default.config.CORRESPONDING_FILE_NOT_FOUND.format('["main"]',n),code:config_1.PLUGIN_JSON_CONTENT_ERR,filePath:i})}function isKindOfGamePlugin(o){return o.type===config_1.COMPILE_TYPE.miniGamePlugin}function checkPluginJSON(o){const{root:e="",project:t}=o,c=path_1.default.posix.join(e,"plugin.json");t.stat(e,"plugin.json")||(0,common_2.throwError)({msg:locales_1.default.config.PLUGIN_JSON_NOT_FOUND.format(e,"plugin.json"),code:config_1.PLUGIN_JSON_FILE_NOT_FOUND,filePath:c});const i=t.getFile(e,"plugin.json"),n=(0,common_1.checkJSONFormat)((0,common_2.checkUTF8)(i,c),c),r=(0,schemaValidate_1.schemaValidate)("plugin",n);if(r.error.length){const o=r.error.map(o=>"type"===o.errorType||"enum"===o.errorType||"anyOf"===o.errorType?locales_1.default.config.JSON_CONTENT_SHOULD_BE.format([o.errorProperty,o.correctType]):locales_1.default.config.SHOULD_NOT_BE_EMPTY.format([o.requireProperty])).join("\n");(0,common_2.throwError)({msg:"pluginJSON$"+o,code:config_1.PLUGIN_JSON_CONTENT_ERR,filePath:c})}return void 0!==n.themeLocation&&(0,common_2.checkPath)({value:n.themeLocation,tips:'["themeLocation"]',filePath:c}),isKindOfGamePlugin(t)||(checkPublicComponentsAndPages(o,n),checkComponentPath(o,n)),checkMain(o,n),checkWorkers(o,n),n}exports.checkComponentPath=checkComponentPath,exports.checkWorkers=checkWorkers,exports.getDevPluginJSON=(0,reactiveCache_1.wrapCompileJSONFunc)(cache_1.CACHE_KEY.PLUGIN_JSON,(o,e="")=>{const t=(0,projectconfig_1.getProjectConfigJSON)(o),{pluginRoot:c=""}=t;let i=c;o.type===config_1.COMPILE_TYPE.miniGame&&e&&(i=e),e||c||(0,common_2.throwError)({msg:locales_1.default.config.NOT_FOUND.format('["pluginRoot"]'),filePath:"project.config.json",code:config_1.JSON_CONTENT_ERR});return checkPluginJSON({project:o,filePath:path_1.default.posix.join(i,"plugin.json"),root:i})});const getGameLocalPluginJSON=async(o,e)=>{const t=await(0,projectconfig_1.getProjectConfigJSON)(o),{miniprogramRoot:c}=t;if(!e)return{};const i=(0,tools_1.normalizePath)(path_1.default.posix.join(c||"",e));return checkPluginJSON({project:(0,reactiveCache_1.tryToGetReactiveProject)(o),filePath:path_1.default.posix.join(i,"plugin.json"),root:i})};exports.getGameLocalPluginJSON=getGameLocalPluginJSON;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getPluginPageJSON=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),common_1=require("../common"),schemaValidate_1=require("../../validate/schemaValidate"),locales_1=tslib_1.__importDefault(require("../../../utils/locales/locales")),common_2=require("../../../utils/common"),plugin_1=require("./plugin"),theme_1=require("../theme"),spreadUsingComponent=async(e,t)=>{const{project:a,root:o,filePath:n}=t,i=path_1.default.posix.relative(o,n);if(n.includes("miniprogram_npm/"))return;const r=await(0,plugin_1.getDevPluginJSON)(a,!0),s=Object.assign({},r.usingComponents);if(0!==Object.keys(s).length){e.usingComponents||(e.usingComponents={});for(const t in s){if(e.usingComponents[t])continue;const a=s[t]||"";if(a.startsWith("/")||a.startsWith("plugin://")){e.usingComponents[t]=a;continue}const o=path_1.default.posix.normalize(path_1.default.posix.relative(path_1.default.posix.dirname(i),a));e.usingComponents[t]=o.startsWith(".")?o:"./"+o}}};async function checkComponentPath(e,t){const{project:a,root:o,filePath:n}=t;await(0,common_1.checkComponentPath)({project:a,root:o,relativePath:path_1.default.posix.relative(o,n),inputJSON:e})}async function getPluginPageJSON(e){const{project:t}=e;let a=e.filePath;if(!t.stat("",a))return{};const o=await t.getFile("",a),n=(0,common_1.checkJSONFormat)((0,common_2.checkUTF8)(o,a),a),i=await(0,theme_1.getPluginThemeLocation)(t);if(!i){const e=(0,common_1.getPageJSONVariableDecalearProperty)(n);e.length&&(0,common_2.throwError)({msg:'pluginJSON["themeLocation"] is required because:\n'+e.map(e=>`"${e.value}" as variable was declared at ${a}:${e.property}`).join("\n"),filePath:a})}let r={light:{},dark:{}};i&&(r=await(0,theme_1.checkThemeJSON)(t,{themeLocation:i,isPlugin:!0}));const s=(0,theme_1.mergeThemeJSONToPageJSON)(r,n,a),l=(0,schemaValidate_1.schemaValidate)("page",s.pageJSONLight),c=(0,schemaValidate_1.schemaValidate)("page",s.pageJSONDark);if(l.warning&&(n.__warning__=locales_1.default.config.INVALID.format(l.warning)),i&&c.warning&&(n.__warning__=locales_1.default.config.INVALID.format(c.warning)),l.error.length){const e=(0,common_1.transValidateResult)(l);i&&(a+=` or ${i}["light"]`),(0,common_2.throwError)({msg:e,filePath:a})}if(i&&c.error.length){const e=(0,common_1.transValidateResult)(c);i&&(a+=` or ${i}["dark"]`),(0,common_2.throwError)({msg:e,filePath:a})}return await checkComponentPath(n,e),await spreadUsingComponent(n,e),n}exports.getPluginPageJSON=getPluginPageJSON;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.cleanReactiveCache=exports.wrapCompileJSONFunc=exports.tryToGetReactiveJSONCompiler=exports.ReactiveJSONCompiler=exports.tryToGetReactiveProject=exports.ReactiveProject=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),tools_1=require("../../utils/tools"),reactivity_1=require("@vue/reactivity"),lodash_1=require("lodash"),process_1=require("process"),config_1=require("../../config"),isDevtools=process.__nwjs&&"wechatwebdevtools"===nw.App.manifest.appname;function info(...e){}function log(...e){isDevtools&&console.log.apply(console,e)}function error(...e){isDevtools&&console.error.apply(console,e)}function isAttrEqual(e,t){return"object"==typeof e&&"object"==typeof t&&(!(!e||!t)&&(e.platform===t.platform&&e.appType===t.appType&&e.gameApp===t.gameApp&&e.isSandbox===t.isSandbox&&e.released===t.released&&(e.setting.MaxCodeSize===t.setting.MaxCodeSize&&e.setting.MaxSubpackageSubCodeSize===t.setting.MaxSubpackageSubCodeSize&&e.setting.MaxSubpackageFullCodeSize===t.setting.MaxSubpackageFullCodeSize&&e.setting.NavigateMiniprogramLimit===t.setting.NavigateMiniprogramLimit&&e.setting.MaxSubPackageLimit===t.setting.MaxSubPackageLimit&&e.setting.MinTabbarCount===t.setting.MinTabbarCount&&e.setting.MaxTabbarCount===t.setting.MaxTabbarCount&&e.setting.MaxCustomTabbarCount===t.setting.MaxCustomTabbarCount&&e.setting.MaxTabbarIconSize===t.setting.MaxTabbarIconSize)))}function makeReadonly(e){return e&&"object"==typeof e?(0,reactivity_1.readonly)(e):e}class ReactiveProject{constructor(e){if(this.fileBoxs=new Map,this.statBoxs=new Map,this.resetFileChangeListener=()=>{},this.project=e,e.onFileChange){const t=e.onFileChange;e.onFileChange=(i,o)=>{t.call(e,i,o),this.onFileChange(i,o)},this.resetFileChangeListener=()=>{e.onFileChange=t}}this.miniprogramRootBox=(0,reactivity_1.ref)(e.miniprogramRoot),this.pluginRootBox=(0,reactivity_1.ref)(e.pluginRoot),this.appidBox=(0,reactivity_1.ref)(e.appid),this.typeBox=(0,reactivity_1.ref)(e.type),this.attrBox=(0,reactivity_1.ref)(config_1.DefaultProjectAttr)}release(){this.resetFileChangeListener(),log("[reactiveCache] reactiveProject release")}async attr(){return this.attrSync()}getFileList(e,t){return this.project.getFileList(e,t)}getFilesAndDirs(){return this.project.getFilesAndDirs()}getExtAppid(){return this.project.getExtAppid()}updateFiles(){throw new Error("Method updateFiles not implemented.")}async updateProject(){this.appidBox.value!==this.project.appid&&(this.appidBox.value=this.project.appid),this.typeBox.value!==this.project.type&&(this.typeBox.value=this.project.type),this.miniprogramRootBox.value!==this.project.miniprogramRoot&&(this.miniprogramRootBox.value=this.project.miniprogramRoot),this.pluginRootBox.value!==this.project.pluginRoot&&(this.pluginRootBox.value=this.project.pluginRoot);const e=await this.project.attr();return isAttrEqual(e,this.attrBox.value)||(this.attrBox.value=e),new Promise(e=>{setTimeout(e,0)})}onFileChange(e,t){if("change"===e){const e=this.fileBoxs.get(t);if(e){const i=this.project.getFile("",t);e.value&&0===i.compare(e.value)||(e.value=i)}}else if("unlink"===e){const e=this.fileBoxs.get(t);e&&(this.fileBoxs.delete(t),e.value=void 0);const i=this.statBoxs.get(t);if(i){const e=this.project.stat("",t);(0,lodash_1.isEqual)(e,i.value)||(i.value=e)}}else if("unlinkDir"===e){const e=t+"/";let i=Array.from(this.fileBoxs.keys());for(const t of i)if(0===t.indexOf(e)){const e=this.fileBoxs.get(t);this.fileBoxs.delete(t),e.value=void 0}i=Array.from(this.statBoxs.keys());for(const t of i)if(0===t.indexOf(e)){const e=this.statBoxs.get(t);void 0!==e.value&&(this.statBoxs.delete(t),e.value=void 0)}}else if("add"===e||"addDir"===e){const e=this.statBoxs.get(t);if(e){const i=this.project.stat("",t);(0,lodash_1.isEqual)(i,e.value)||(e.value=i)}}}getFile(e,t){const i=this.getTargetPath(e,t),o=this.fileBoxs.get(i);if(o)return o.value;{const o=(0,reactivity_1.ref)(this.project.getFile(e,t));return this.fileBoxs.set(i,o),o.value}}stat(e,t){const i=this.getTargetPath(e,t),o=this.statBoxs.get(i);if(o)return o.value;{const o=(0,reactivity_1.ref)(this.project.stat(e,t));return this.statBoxs.set(i,o),o.value}}attrSync(){return this.attrBox.value}get appid(){return this.appidBox.value}get type(){return this.typeBox.value}get nameMappingFromDevtools(){return this.project.nameMappingFromDevtools}get projectPath(){return this.project.projectPath}get privateKey(){return this.project.privateKey}get miniprogramRoot(){return this.miniprogramRootBox.value}set miniprogramRoot(e){this.miniprogramRootBox.value=e,this.project.miniprogramRoot=e}get pluginRoot(){return this.pluginRootBox.value}set pluginRoot(e){this.pluginRootBox.value=e,this.project.pluginRoot=e}getTargetPath(e,t){return(0,tools_1.normalizePath)(path_1.default.posix.join(e,t)).replace(/\/$/,"").replace(/^\//,"")}}exports.ReactiveProject=ReactiveProject;const reactiveProjectMap=new Map;function tryToGetReactiveProject(e){let t=reactiveProjectMap.get(e.projectPath);return t||(t=new ReactiveProject(e),reactiveProjectMap.set(e.projectPath,t),t)}exports.tryToGetReactiveProject=tryToGetReactiveProject;let isPending=!1;const pendingRunner=new Set,resolvedPromise=Promise.resolve();function runInNextTick(e){pendingRunner.add(e),isPending||(isPending=!0,resolvedPromise.then(()=>{const e=Date.now();try{const t=Array.from(pendingRunner);pendingRunner.clear(),isPending=!1,t.forEach(e=>{e()})}finally{info(`[reactiveCache] nextTick update cost ${Date.now()-e} ms`)}}))}class ReactiveJSONCompiler{constructor(e){this.pageComputeds=new Map,this.jsonComputeds=new Map,this.project=e}release(){log("[reactiveCache] reactiveJSONCompiler release")}registerOrGet(e,t,...i){let o=this.jsonComputeds.get(e);if(!o){o=(0,reactivity_1.ref)(void 0),this.jsonComputeds.set(e,o);let a=void 0;a=(0,reactivity_1.effect)(()=>{try{info(`[reactiveCache] ${e} start to update`);const a=t.call(null,this.project,...i);(0,lodash_1.isEqual)(a,o.value)?info(`[reactiveCache] ${e} update finish, value no change`):(o.value=makeReadonly(a),info(`[reactiveCache] ${e} update finish, new value: `,o.value))}catch(t){o.value=t instanceof Error?t:new Error(t.toString()),log(`[reactiveCache] update ${e} failed: `,t)}},{scheduler(){a&&a.active&&runInNextTick(a)}})}const{value:a}=o;if(a instanceof Error)throw a;return a}static setOriginGetPageJSON(e){this.originGetPageJSON=e}static setOriginCheckPageJSON(e){this.originCheckPageJSON=e}getPageJSON(e,t){let i=this.pageComputeds.get(t.pagePath);i||(i={checked:(0,reactivity_1.ref)(void 0),compiled:(0,reactivity_1.ref)(void 0)},this.pageComputeds.set(t.pagePath,i));const o=i[e];if(void 0===o.value){let i=void 0;i=(0,reactivity_1.effect)(()=>{try{info(`[reactiveCache] start to update ${e} ${t.pagePath}`);const i="compiled"===e?ReactiveJSONCompiler.originGetPageJSON(this.project,t):ReactiveJSONCompiler.originCheckPageJSON(this.project,t);o.value=makeReadonly(i),info(`[reactiveCache] update finish ${e} ${t.pagePath}`)}catch(i){o.value=i instanceof Error?i:new Error(i.toString()),log(`[reactiveCache] update ${e} ${t.pagePath} failed: `,i)}},{scheduler(){i&&i.active&&(0,process_1.nextTick)(i)}})}const a=o.value;if(a instanceof Error)throw a;return a}}exports.ReactiveJSONCompiler=ReactiveJSONCompiler;const reactiveJSONCompilerMap=new Map;function tryToGetReactiveJSONCompiler(e){let t=reactiveJSONCompilerMap.get(e.projectPath);return t||(t=new ReactiveJSONCompiler(e),reactiveJSONCompilerMap.set(e.projectPath,t),t)}function wrapCompileJSONFunc(e,t){return function(i,...o){i instanceof ReactiveProject||(i=tryToGetReactiveProject(i));return tryToGetReactiveJSONCompiler(i).registerOrGet(e,t,...o)}}function cleanReactiveCache(){reactiveProjectMap.forEach(e=>{e.release()}),reactiveProjectMap.clear(),reactiveJSONCompilerMap.forEach(e=>{e.release()}),reactiveJSONCompilerMap.clear()}exports.tryToGetReactiveJSONCompiler=tryToGetReactiveJSONCompiler,exports.wrapCompileJSONFunc=wrapCompileJSONFunc,exports.cleanReactiveCache=cleanReactiveCache;
}(require("licia/lazyImport")(require), require)
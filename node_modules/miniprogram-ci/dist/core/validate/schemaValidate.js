"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.NEW_CHECK_JSON_WAY=exports.schemaValidate=exports.config=void 0;const jsonschema_1=require("jsonschema"),validator=new jsonschema_1.Validator,app=require("../../vendor/schema/dist/app.js"),ext=require("../../vendor/schema/dist/ext.js"),game=require("../../vendor/schema/dist/game.js"),page=require("../../vendor/schema/dist/page.js"),plugin=require("../../vendor/schema/dist/plugin.js"),pluginpage=require("../../vendor/schema/dist/pluginpage.js"),projectconfig=require("../../vendor/schema/dist/projectconfig.js"),sitemap=require("../../vendor/schema/dist/sitemap.js"),theme=require("../../vendor/schema/dist/theme.js");exports.config={app:app,ext:ext,game:game,page:page,plugin:plugin,pluginpage:pluginpage,projectconfig:projectconfig,sitemap:sitemap,theme:theme};const SchemaMap=Object.assign({},exports.config);function schemaValidate(e,r){const a=validator.validate(r,SchemaMap[e]).errors,t=a.filter(e=>"additionalProperties"===e.name),n=a.filter(e=>"additionalProperties"!==e.name).map(e=>{if("type"===e.name||"enum"===e.name||"anyOf"===e.name){let r=e.argument;if("string"==typeof r&&(r=[r]),"anyOf"===e.name){r=[(e.schema.anyOf||[]).map(e=>e.type).join(", ")]}const a=e.property.replace(/^instance\.?/,"");return{errorType:e.name,errorProperty:a,correctType:r}}return{errorType:e.name,errorProperty:e.property.replace(/^instance\.?/,""),requireProperty:e.argument}});return{warning:t.map(r=>{const a=r.property.replace(/^instance\.?/,"");return`${e}.json ${a.length?a:""}["${r.argument}"]`}).join("、")||"",error:n}}exports.schemaValidate=schemaValidate,exports.NEW_CHECK_JSON_WAY=!0;
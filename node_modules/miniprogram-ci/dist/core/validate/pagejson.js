"use strict";const T=require("./validate");module.exports=new T("object",!1,{navigationBarBackgroundColor:new T("string",!1),navigationBarTextStyle:new T("string",!1,["black","white"]),navigationBarTitleText:new T("string",!1),navigationStyle:new T("string",!1,["default","custom"]),backgroundColor:new T("string",!1),backgroundTextStyle:new T("string",!1,["dark","light"]),enablePullDownRefresh:new T("boolean",!1),onReachBottomDistance:new T("number",!1),disableScroll:new T("boolean",!1),disableSwipeBack:new T("boolean",!1),backgroundColorTop:new T("string",!1),backgroundColorBottom:new T("string",!1),usingComponents:new T("object",!1,new T("string",!0)),pageOrientation:new T("string",!1,["auto","portrait","landscape"]),component:new T("boolean",!1),restartStrategy:new T("string",!1,["homePage","homePageAndLatestPage"])});
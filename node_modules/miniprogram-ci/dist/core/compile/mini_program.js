!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.compile=void 0;const tslib_1=require("tslib"),common_1=require("./common"),mpjson_1=require("./handler/mpjson"),white_ext_list_1=require("../../utils/white_ext_list"),config_1=require("../../config"),path_1=tslib_1.__importDefault(require("path")),projectconfig_1=require("../json/projectconfig"),uglifyfilenames_1=require("../protect/uglifyfilenames"),partial_1=require("../analyse/partial"),lodash_1=tslib_1.__importDefault(require("lodash")),summer=tslib_1.__importStar(require("../../summer/ci"));async function compile(e,i){var t,o,a,l;const s=await(0,projectconfig_1.getProjectConfigJSON)(e);if(null===(t=s.setting)||void 0===t?void 0:t.useCompilerPlugins)return summer.compile(e,s,i,s.setting.useCompilerPlugins);const r=s.miniprogramRoot||"",{MiniProgramWhiteList:p}=await(0,white_ext_list_1.getWhiteExtList)();let n=e.getFileList(r,"").filter(common_1.isNotIgnoredByProjectConfig.bind(null,s,r)).filter(e=>p.has(path_1.default.posix.extname(e)));if((null===(o=i.compilePages)||void 0===o?void 0:o.length)&&i.analyzer){const e=(0,partial_1.partialGetCodeFiles)(i.analyzer,i.compilePages).map(e=>path_1.default.posix.join(r,e)),t=n.filter(e=>{const i=path_1.default.posix.extname(e);return".js"!==i&&".json"!==i&&".wxss"!==i&&".wxml"!==i});n=t.concat(e)}let c=!1,m=await(0,mpjson_1.compileJSON)(e,i);if((null===(a=i.compilePages)||void 0===a?void 0:a.length)&&i.analyzer){const e=JSON.parse(m[path_1.default.posix.join(r,"app.json")]);e.pages=e.pages.filter(e=>{var t;return null===(t=i.compilePages)||void 0===t?void 0:t.includes(e)}),0===e.pages.length&&(e.pages=["partialcompileplaceholder"],c=!0),e.subPackages&&(e.subPackages.forEach(e=>{e.pages=e.pages.filter(t=>{var o;return null===(o=i.compilePages)||void 0===o?void 0:o.includes(e.root+t)})}),e.subPackages=e.subPackages.filter(e=>e.pages.length>0)),m[path_1.default.posix.join(r,"app.json")]=JSON.stringify(e),m=lodash_1.default.pick(m,Object.keys(m).filter(e=>m[e]instanceof Buffer||n.includes(e)))}const _=n.filter(e=>".js"===path_1.default.posix.extname(e)).map(e=>path_1.default.posix.relative(r,e)),u=await(0,common_1.compileJSFiles)(e,_,r,i),f=n.filter(e=>".wxss"===path_1.default.posix.extname(e)).map(e=>path_1.default.posix.relative(r,e)),g=await(0,common_1.compileWXSSFiles)(e,f,r,i),d=n.filter(e=>".wxml"===path_1.default.posix.extname(e)).map(e=>path_1.default.posix.relative(r,e)),h=await(0,common_1.compileWXMLFiles)(e,d,r,i),j=n.filter(e=>{const i=path_1.default.posix.extname(e);return".js"!==i&&".json"!==i&&".wxss"!==i&&".wxml"!==i}),x=await(0,common_1.compileOther)(e,j,i);let v=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},m),u),x),g),h);if(c&&(v[path_1.default.posix.join(r,"partialcompileplaceholder.js")]||(v[path_1.default.posix.join(r,"partialcompileplaceholder.js")]=""),v[path_1.default.posix.join(r,"partialcompileplaceholder.wxml")]||(v[path_1.default.posix.join(r,"partialcompileplaceholder.wxml")]="")),e.type===config_1.COMPILE_TYPE.miniProgram){if(s.miniprogramRoot&&"."!==s.miniprogramRoot&&"./"!==s.miniprogramRoot){const i={};for(const t in v)i[path_1.default.posix.relative(e.miniprogramRoot,t)]=v[t];v=i}v["project.config.json"]=JSON.stringify({miniprogramRoot:"",__compileDebugInfo__:i.__compileDebugInfo__||{}}),delete v["project.private.config.json"]}else v["project.config.json"]=JSON.stringify({miniprogramRoot:e.miniprogramRoot||"",__compileDebugInfo__:i.__compileDebugInfo__||{}}),delete v["project.private.config.json"];return e.type===config_1.COMPILE_TYPE.miniProgram&&(null===(l=i.setting)||void 0===l?void 0:l.codeProtect)&&(v=await(0,uglifyfilenames_1.uglifyFileNames)(e,v)),v}exports.compile=compile;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.compileWXSS=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),tools_1=require("../../../utils/tools"),locales_1=tslib_1.__importDefault(require("../../../utils/locales/locales")),config_1=require("../../../config"),taskstatus_1=require("../../../utils/taskstatus"),worker_thread_1=require("../../worker_thread"),common_1=require("../../../utils/common");async function compileWXSS(e,t,o){const{root:r="",setting:i={},onProgressUpdate:s=(()=>{}),devToolsCompileCache:a}=o,{minify:l,minifyWXSS:_,autoPrefixWXSS:c}=i,n=new taskstatus_1.TaskStatus(t),u=path_1.default.posix.join(r,t),f=await e.getFile(r,t);if(!l&&!_&&!c){s(n);const e=(0,tools_1.bufferToUtf8String)(f);return void 0===e&&(0,common_1.throwError)({msg:locales_1.default.config.FILE_NOT_UTF8.format(u),code:config_1.FILE_NOT_UTF8,filePath:u}),n.done(),s(n),{filePath:t,code:e}}async function d(){const o=await(0,worker_thread_1.runTask)(worker_thread_1.TASK_NAME.COMPILE_WXSS,{projectPath:e.projectPath,root:r,filePath:t,setting:i,code:f},e=>{e===worker_thread_1.ETaskStatus.progress?s(n):e===worker_thread_1.ETaskStatus.done&&(n.done(),s(n))});return o.error&&(0,common_1.throwError)({msg:o.error.message,code:o.error.code,filePath:u}),o.code}let h="";if(a){const o=(0,tools_1.normalizePath)(path_1.default.posix.join(e.projectPath,r,t)),s=`${o}_${JSON.stringify(i)}`;h=await a.getFile(o,s),h&&!i.codeProtect||(h=await d(),a.setFile(o,h,s))}else h=await d();return{filePath:t,code:h}}exports.compileWXSS=compileWXSS;
}(require("licia/lazyImport")(require), require)
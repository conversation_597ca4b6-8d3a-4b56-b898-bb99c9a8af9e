!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.compileJS=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),taskstatus_1=require("../../../utils/taskstatus"),worker_thread_1=require("../../worker_thread"),common_1=require("../../../utils/common"),config_1=require("../../../config"),tools_1=require("../../../utils/tools"),app_1=require("../../json/app"),common_2=require("../../json/common"),projectconfig_1=require("../../json/projectconfig"),game_1=tslib_1.__importDefault(require("../../json/game")),core_1=require("../../../core");async function formatBabelRoot(e,t,o,r){const a=e.type;if(a===config_1.COMPILE_TYPE.miniProgram){const t=await(0,app_1.getAppJSON)(e),a=(0,common_2.checkPagePathIsInIndependentSubpackage)(t,o);a&&(r=`${a.root}/${r}`),"object"==typeof t.functionalPages&&!0===t.functionalPages.independent&&o.startsWith("functional-pages/")&&(r="functional-pages/"+r),"string"==typeof t.openDataContext&&o.startsWith(t.openDataContext)&&(r=`${t.openDataContext}/${r}`),t.workers&&o.startsWith((0,tools_1.getWorkersPath)(t.workers))&&(r=`${(0,tools_1.getWorkersPath)(t.workers)}/${r}`)}else if(a===config_1.COMPILE_TYPE.miniGame){const t=await(0,game_1.default)(e),a=(0,common_2.checkFilePathIsInIndependentSubpackage)(t,o);a&&(r=`${a}/${r}`),"string"==typeof t.openDataContext&&o.startsWith(t.openDataContext)&&(r=`${t.openDataContext}/${r}`),t.workers&&o.startsWith((0,tools_1.getWorkersPath)(t.workers))&&(r=`${(0,tools_1.getWorkersPath)(t.workers)}/${r}`)}else if(a===config_1.COMPILE_TYPE.miniProgramPlugin||a===config_1.COMPILE_TYPE.miniGamePlugin){const t=await(0,core_1.getPluginJSON)(e);"string"==typeof t.workers&&o.startsWith(t.workers)&&(r=`${t.workers}/${r}`)}return(0,tools_1.normalizePath)(""+r)}async function compileJS(e,t,o){var r,a;const{setting:s={},onProgressUpdate:i=(()=>{}),root:n="",devToolsCompileCache:c}=o,l=path_1.default.posix.join(n,t);let _=[],p=o.babelRoot||"@babel/runtime";if(s.es7){const o=await(0,projectconfig_1.getProjectConfigJSON)(e);_=(null===(a=null===(r=o.setting)||void 0===r?void 0:r.babelSetting)||void 0===a?void 0:a.ignore)||[],p=await formatBabelRoot(e,n,t,p)}const u=new taskstatus_1.TaskStatus(t),g=o.sourceCode?o.sourceCode:await e.getFile(n,t);async function m(){const o=await(0,worker_thread_1.runTask)(worker_thread_1.TASK_NAME.COMPILE_JS,{projectPath:e.projectPath,root:n,filePath:t,setting:s,code:g,babelRoot:p,babelIgnore:_},e=>{e===worker_thread_1.ETaskStatus.progress?i(u):e===worker_thread_1.ETaskStatus.done&&(u.done(),i(u))});return o.error&&(0,common_1.throwError)({msg:o.error.message,code:o.error.code,filePath:l}),o}let f={};if(c){const o=(0,tools_1.normalizePath)(path_1.default.posix.join(e.projectPath,n,t)),r=`${o}_${JSON.stringify(s)}`;f=await c.getFile(o,r),f&&!s.codeProtect||(f=await m(),c.setFile(o,f,r))}else f=await m();return Object.assign({filePath:t},f)}exports.compileJS=compileJS;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.compileJSON=exports.addSkylineRendererToComponents=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),app_1=require("../../json/app"),getExtJSON_1=require("../../json/app/getExtJSON"),common_1=require("../../../utils/common"),getPageJSON_1=require("../../json/page/getPageJSON"),taskstatus_1=require("../../../utils/taskstatus"),projectconfig_1=require("../../json/projectconfig"),common_2=require("../common");function addSkylineRendererToComponents(e,t){const o=new Set,n=new Set;function s(e,t){Object.values(t.usingComponents||{}).forEach(t=>{const s=t.startsWith("/")?t.substring(1)+".json":path_1.default.posix.join(path_1.default.posix.dirname(e),t+".json");o.has(s)||n.add(s)}),Object.values(t.componentGenerics||{}).forEach(t=>{if("object"==typeof t&&"string"==typeof t.default){const s=t.default.startsWith("/")?t.default.substring(1)+".json":path_1.default.posix.join(path_1.default.posix.dirname(e),t.default+".json");o.has(s)||n.add(s)}})}for(const t of Object.keys(e)){const o=JSON.parse(e[t]);"skyline"===o.renderer&&s(t,o)}for(;n.size>0;){const e=n.values().next().value;if(n.delete(e),o.add(e),t[e]&&"string"==typeof t[e]){const o=JSON.parse(t[e]);if("xr-frame"===o.renderer)continue;if("webview"===o.renderer)throw new Error(`The component (${e}) is configured with renderer: 'webview', but is used in skyline pages`);o.renderer="skyline",t[e]=JSON.stringify(o),s(e,o)}}}async function compilePageJSON(e,t,o,n){const{onProgressUpdate:s=(()=>{})}=n,a={};for(const n of t){const t=new taskstatus_1.TaskStatus(n);s(t);const i=await(0,getPageJSON_1.getPageJSON)(e,{miniprogramRoot:o,pagePath:n});a[path_1.default.posix.join(o,n+".json")]=JSON.stringify(Object.assign(Object.assign({},i),{__warning__:void 0})),t.done(),s(t)}return a}async function compileJSON(e,t){const{onProgressUpdate:o=(()=>{})}=t,n=await(0,projectconfig_1.getProjectConfigJSON)(e),{miniprogramRoot:s=""}=n;let a=new taskstatus_1.TaskStatus("app.json");o(a);const i=await(0,app_1.getAppJSON)(e);let r;a.done(),o(a);const p=await e.attr();(null==p?void 0:p.platform)&&(a=new taskstatus_1.TaskStatus("ext.json"),o(a),r=await(0,getExtJSON_1.getExtJSON)(e),a.done(),o(a));const c=(0,common_1.getAllPages)(i),l=await compilePageJSON(e,c,s,t),d=new Set(["app.json","ext.json"].concat(c.map(e=>e+".json"))),u=(0,common_1.getAllTargetTypeFilesWithOtherTypeFilesOfSameName)(e,".json",[".wxml",".js"],s).filter(common_2.isNotIgnoredByProjectConfig.bind(null,n,s)).filter(e=>{const t=path_1.default.posix.relative(s,e);return!d.has(t)}).map(e=>path_1.default.posix.relative(s,e).replace(/\.json$/,"")),g=await compilePageJSON(e,u,s,t);u.forEach(e=>d.add(e+".json")),addSkylineRendererToComponents(l,g);const f=e.getFileList(s,".json").filter(common_2.isNotIgnoredByProjectConfig.bind(null,n,s)).filter(e=>{const t=path_1.default.posix.relative(s,e);return!d.has(t)}),m=await(0,common_2.compileOther)(e,f,t),j=Object.assign(Object.assign(Object.assign({[path_1.default.posix.join(s,"app.json")]:JSON.stringify(Object.assign(Object.assign({},i),{__warning__:void 0}))},l),g),m);return r&&(j[path_1.default.posix.join(s,"ext.json")]=JSON.stringify(r)),j}exports.addSkylineRendererToComponents=addSkylineRendererToComponents,exports.compileJSON=compileJSON;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.compile=exports.compilePlugin=void 0;const tslib_1=require("tslib"),mini_program_1=require("./mini_program"),common_1=require("./common"),plugin_1=require("../json/plugin/plugin"),plugin_page_1=require("../json/plugin/plugin_page"),white_ext_list_1=require("../../utils/white_ext_list"),path_1=tslib_1.__importDefault(require("path")),projectconfig_1=require("../json/projectconfig"),summer=tslib_1.__importStar(require("../../summer/ci"));async function compilePlugin(i,e){const t=await(0,projectconfig_1.getProjectConfigJSON)(i),{MiniProgramWhiteList:o}=await(0,white_ext_list_1.getWhiteExtList)(),n=t.pluginRoot,s=i.getFileList(n,"").filter(common_1.isNotIgnoredByProjectConfig.bind(null,t,n)).filter(i=>o.has(path_1.default.posix.extname(i))),a=await(0,plugin_1.getDevPluginJSON)(i,!1),l=s.filter(i=>".json"===path_1.default.posix.extname(i)&&i!==path_1.default.posix.join(n,"plugin.json")),p={};for(const e of l){const t=await(0,plugin_page_1.getPluginPageJSON)({project:i,root:n,filePath:e});p[e]=JSON.stringify(t)}const r=s.filter(i=>".js"===path_1.default.posix.extname(i)).map(i=>path_1.default.posix.relative(n,i)),m=await(0,common_1.compileJSFiles)(i,r,n,e),g=s.filter(i=>".wxss"===path_1.default.posix.extname(i)).map(i=>path_1.default.posix.relative(n,i)),u=await(0,common_1.compileWXSSFiles)(i,g,n,e),c=s.filter(i=>".wxml"===path_1.default.posix.extname(i)).map(i=>path_1.default.posix.relative(n,i)),_=await(0,common_1.compileWXMLFiles)(i,c,n,e),f=s.filter(i=>{const e=path_1.default.posix.extname(i);return".js"!==e&&".json"!==e&&".wxss"!==e&&".wxml"!==e}),j=await(0,common_1.compileOther)(i,f,e);return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[path_1.default.posix.join(n,"plugin.json")]:JSON.stringify(a)},p),m),j),u),_)}async function compile(i,e){var t;const o=await(0,projectconfig_1.getProjectConfigJSON)(i);if(null===(t=o.setting)||void 0===t?void 0:t.useCompilerPlugins)return summer.compile(i,o,e,o.setting.useCompilerPlugins);const n=await compilePlugin(i,e),s=await(0,mini_program_1.compile)(i,e);return Object.assign(Object.assign(Object.assign({},n),s),{"project.config.json":JSON.stringify({miniprogramRoot:o.miniprogramRoot,pluginRoot:o.pluginRoot,__compileDebugInfo__:e.__compileDebugInfo__||{}})})}exports.compilePlugin=compilePlugin,exports.compile=compile;
}(require("licia/lazyImport")(require), require)
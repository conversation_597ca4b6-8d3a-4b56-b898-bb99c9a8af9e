!function(require, directRequire){
"use strict";function pad(e,t=2){return e>10**t?String(e):`${new Array(t).join("0")}${e}`.slice(-t)}function getPrintTime(){const e=new Date;return`${String(e.getFullYear())}-${pad(e.getMonth()+1)}-${pad(e.getDate())} ${pad(e.getHours())}:${pad(e.getMinutes())}:${pad(e.getSeconds())}.${pad(e.getMilliseconds(),3)}`}Object.defineProperty(exports,"__esModule",{value:!0}),exports.silentRecorder=exports.Recorder=exports.getPrintTime=void 0,exports.getPrintTime=getPrintTime;let taskId=5e7;function genTaskId(){return taskId++}class Recorder{constructor(e){this.sendProgress=e}start(e){const t=genTaskId(),r=e.startsWith("!");r||this.sendProgress(t,"doing",e);const s=()=>{r||this.sendProgress(t,"done","")};return s.id=t,s}async run(e,t){const r=this.start(e);try{return await t()}finally{r()}}}exports.Recorder=Recorder,exports.silentRecorder=new Recorder(()=>{});
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.SummerCompiler=void 0;const tslib_1=require("tslib"),plugingraph_1=require("./graph/plugingraph"),appgraph_1=require("./graph/appgraph"),path_1=tslib_1.__importDefault(require("path")),reactiveCache_1=require("../core/json/reactiveCache"),initPlugin_1=require("./initPlugin"),lodash_1=require("lodash"),locales_1=tslib_1.__importDefault(require("../utils/locales/locales")),plugin_driver_1=require("./plugin_driver"),persist_cache_1=tslib_1.__importDefault(require("./persist_cache")),tools_1=require("../utils/tools");let proxyProject;function initProxyProjectForJSON(t){if(proxyProject&&proxyProject.projectPath!==t.projectPath&&(proxyProject.clearResolver(),proxyProject=void 0),!proxyProject){let e={};function i(i,r){const o=t.stat(i,r);if(o)return o;const p=t.getTargetPath(i,r);for(const t in e)if(p.startsWith(t))return e[t].stat(path_1.default.posix.relative(t,p))}proxyProject=new Proxy(t,{get:(t,e,r)=>"stat"===e?i:Reflect.get(t,e,r)}),proxyProject.addResolver=t=>{(0,reactiveCache_1.cleanReactiveCache)(),e[t.root]=t},proxyProject.removeResolver=t=>{(0,reactiveCache_1.cleanReactiveCache)(),delete e[t.root]},proxyProject.clearResolver=()=>{e={},(0,reactiveCache_1.cleanReactiveCache)()}}return proxyProject}function getCacheBaseKey(t){const i=`${t.miniprogramRoot}|${t.pluginRoot}|${t.summerPlugins.join(",")}`;return(0,tools_1.generateMD5)(i)}class SummerCompiler{constructor(t,i,e){this.project=t,this.cachePath=i,this.options=e,this.proxyProject=initProxyProjectForJSON(t),this.projectPath=t.projectPath,this.persistCache=new persist_cache_1.default(i,getCacheBaseKey(e)),this.initPlugins(),this.initAppGraph(),"miniProgramPlugin"===this.options.compileType&&this.initPluginGraph()}getBabelSetting(){return this.options.babelSetting}initPlugins(){this.plugins=this.options.summerPlugins.map(t=>{let i,e={};return"string"==typeof t?i=t:(i=t[0],e=t[1]),(0,initPlugin_1.initPlugin)(i,this.project.projectPath,e)})}initAppGraph(){this.appGraph=new appgraph_1.AppGraph({type:"miniprogram",root:this.project.miniprogramRoot,persistCache:this.persistCache,plugins:this.plugins,compiler:this})}initPluginGraph(){this.pluginGraph=new plugingraph_1.PluginGraph({type:"plugin",root:this.project.pluginRoot,persistCache:this.persistCache,plugins:this.plugins,compiler:this})}updateOptions(t){var i,e;const r=this.options;if(this.options=t,this.persistCache.updateBaseCacheKey(getCacheBaseKey(t)),!(0,lodash_1.isEqual)(t.babelSetting,r.babelSetting)||!(0,lodash_1.isEqual)(this.options.summerPlugins,r.summerPlugins))return this.initPlugins(),this.appGraph.destroy(),null===(i=this.pluginGraph)||void 0===i||i.destroy(),this.initAppGraph(),void("miniProgramPlugin"===this.options.compileType&&this.initPluginGraph());this.options.compileType!==r.compileType&&("miniProgramPlugin"===this.options.compileType?this.initPluginGraph():(null===(e=this.pluginGraph)||void 0===e||e.destroy(),this.pluginGraph=void 0)),this.appGraph.root!==this.project.miniprogramRoot&&(this.appGraph.destroy(),this.initAppGraph()),this.pluginGraph&&this.pluginGraph.root!==this.project.pluginRoot&&(this.pluginGraph.destroy(),this.initPluginGraph())}destroy(){var t;this.appGraph.destroy(),null===(t=this.pluginGraph)||void 0===t||t.destroy(),this.proxyProject.clearResolver()}getStatus(){const t=(0,plugin_driver_1.genResovleExtConf)(this.plugins),i={},e=new Set;for(const r of["json","js","wxml","wxss","wxs"])i[r]={exts:t[r].reverse()},t[r].forEach(t=>{e.add(t)});return{codeExts:Array.from(e.keys()),codeConf:i}}clearCache(){var t;this.appGraph.clearCache(),null===(t=this.pluginGraph)||void 0===t||t.clearCache(),this.persistCache.clean(),(0,reactiveCache_1.cleanReactiveCache)()}async getConf({graphId:t},i){if("miniprogram"===t){return await this.appGraph.getConf(i)}if("plugin"===t){return await this.pluginGraph.getConf(i)}throw new Error("no support getConf for "+t)}async getCode(t,i){if("miniprogram"===t.graphId){return await this.appGraph.getDevCode(i,{package:t.package})}if("plugin"===t.graphId){return await this.pluginGraph.getDevCode(i)}throw new Error("no support getCode for "+t.graphId)}async getLocalFileList(t){if("miniprogram"===t)return await this.appGraph.getLocalFileList();if("plugin"===t)return await this.pluginGraph.getLocalFileList();throw new Error("no support getCode for "+t)}async compileSingleCode(t,i){if("miniprogram"===t.graphId){return await this.appGraph.compileSingleCode(t.filePath,t.sourceCode)}if("plugin"===t.graphId){return await this.pluginGraph.compileSingleCode(t.filePath,t.sourceCode)}throw new Error("no support getCode for "+t.graphId)}async compile(t,i){const e=await i.run(locales_1.default.config.SUMMER_COMPILE_MINIPROGRAM.format(),()=>this.appGraph.compile(i));if("miniProgram"===this.project.type)return e["project.config.json"]=JSON.stringify({miniprogramRoot:"",__compileDebugInfo__:{useSummer:!0}}),e;if("miniProgramPlugin"===this.project.type){const t={},r=await(await i.run(locales_1.default.config.SUMMER_COMPILE_PLUGIN.format(),()=>this.pluginGraph.compile(i)));return Object.keys(e).forEach(i=>{t[path_1.default.posix.join(this.project.miniprogramRoot,i)]=e[i]}),Object.keys(r).forEach(i=>{t[path_1.default.posix.join(this.project.pluginRoot,i)]=r[i]}),t["project.config.json"]=JSON.stringify({miniprogramRoot:this.project.miniprogramRoot,pluginRoot:this.project.pluginRoot,__compileDebugInfo__:{useSummer:!0}}),t}throw new Error("no support compile for  "+this.project.type)}}exports.SummerCompiler=SummerCompiler;
}(require("licia/lazyImport")(require), require)
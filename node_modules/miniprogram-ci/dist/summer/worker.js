!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.runSummerPluginHook=void 0;const error_1=require("./error"),initPlugin_1=require("./initPlugin");class PluginContainer{constructor(){this.initedPlugins=new Map}getPluginInstance(n,r,e){let i=this.initedPlugins.get(n);return i||(i=(0,initPlugin_1.initPlugin)(n,r,e),this.initedPlugins.set(n,i)),i}clear(){this.initedPlugins.clear()}}const pluginContainer=new PluginContainer;async function runSummerPluginHook(n){var r;if("clear"===n.command)return pluginContainer.clear(),{};const{plugin:e,projectPath:i,pluginOption:t,method:o,args:u}=n,l=pluginContainer.getPluginInstance(e,i,t);if("runMethod"===n.command){const n=null===(r=l.workerMethods)||void 0===r?void 0:r[o];if(!n||"function"!=typeof n)throw new Error(`the ${n} is not a workerMethod of plugin(${e})`);try{return{result:await n(...u)}}catch(n){return{error:n instanceof error_1.SummerError?n.toJSON():n}}}return{}}exports.runSummerPluginHook=runSummerPluginHook;
}(require("licia/lazyImport")(require), require)
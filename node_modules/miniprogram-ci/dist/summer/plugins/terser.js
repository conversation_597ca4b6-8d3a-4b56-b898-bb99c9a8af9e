"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const config_1=require("../../config"),javascript_1=require("./base/javascript"),error_1=require("../error"),terser=()=>require("terser"),babelCodeFrame=()=>require("babel-code-frame");function default_1(){return{name:"summer-terser",workerMethods:{doCompress:async(e,r)=>require("terser").minify(e,{toplevel:!0,compress:{drop_console:!1,drop_debugger:!1},sourceMap:r})},async compress(e){const r=Object.keys(e).filter(e=>e.endsWith(".js")),o={};return await Promise.all(r.map(async r=>{if(e[r].length>=javascript_1.MAX_CODE_LENGTH)return o[r]=e[r],void(e[r+".map"]&&(o[r+".map"]=e[r+".map"]));const s=e[r+".map"]?{includeSources:!0,content:JSON.parse(e[r+".map"]),filename:r}:{includeSources:!0,content:void 0,filename:r},a=await this.runWorkerMethod("doCompress",e[r],s);if(a.error){const o=a.error,s=`file: ${r}\n ${a.error.message}\n ${require("babel-code-frame")(e[r],o.line,o.col>0?o.col:1)}`;throw(0,error_1.makeSummerError)(s,config_1.UGLIFY_JS_ERR,r)}o[r]=a.code,a.map&&(o[r+".map"]=a.map)})),Object.assign(Object.assign({},e),o)}}}exports.default=default_1;
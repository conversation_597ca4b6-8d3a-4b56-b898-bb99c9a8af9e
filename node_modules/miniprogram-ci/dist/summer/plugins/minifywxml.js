"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const tslib_1=require("tslib"),config_1=require("../../config"),log_1=tslib_1.__importDefault(require("../../utils/log")),error_1=require("../error"),minifyWXMLMod=require("../../core/worker_thread/task/minifywxml"),{generateWXMLFromTokens:generateWXMLFromTokens,Tokenizer:Tokenizer}=minifyWXMLMod;function default_1(e,r){const{minify:o=!0}=r||{};return{name:"summer-minifywxml",workerMethods:{async doCompress(e,r){try{const o=new Tokenizer(e.replace(/\r\n/g,"\n"),r),t=[],n=o.generateTokens(t);if(0!==n)throw new Error("minifywxml tokenizer error ret: "+n);return generateWXMLFromTokens(t)}catch(e){throw log_1.default.error(e),(0,error_1.makeSummerError)(e.msg,config_1.MINIFY_WXML_ERR,r)}}},async compress(e){const r={};if(o){const o=Object.keys(e).filter(e=>e.endsWith(".wxml"));await Promise.all(o.map(async o=>{const t=e[o];if(t&&t.length>0){const t=await this.runWorkerMethod("doCompress",e[o],o);r[o]=t}}))}return Object.assign(Object.assign({},e),r)}}}exports.default=default_1;
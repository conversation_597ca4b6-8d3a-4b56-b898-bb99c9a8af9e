"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.importWxssCssReg=exports.importWxssReg=void 0;const tslib_1=require("tslib"),fs_extra_1=tslib_1.__importDefault(require("fs-extra")),path_1=tslib_1.__importDefault(require("path")),error_1=require("../error"),common_1=require("../../utils/common"),tools_1=require("../../utils/tools"),less=()=>require("less");function default_1(e,s){return{name:"summer-less",resolveExt:{wxss:"less"},async load(t,r){var o;if(r.endsWith(".less")){let t=await fs_extra_1.default.readFile(r,"utf-8");if(this.rootPath){const e=(0,tools_1.normalizePath)(await(0,common_1.getMiniprogramRoot)(this.rootPath)),s=path_1.default.posix.join(e,"app.less");if(r!==s&&fs_extra_1.default.existsSync(s)){const s=(0,tools_1.pathRelative)(e,path_1.default.dirname(r))||".";t=`@import (optional, reference) '${"."===s?".":(0,tools_1.pathRelative)(s,"./")}/app.less';\n${t}`}}const a=[];t=t.replace(exports.importWxssReg,(e,s,t)=>(a.push(t),e.replace(t,t+"?css")));try{const i=await require("less").render(t,{sourceMap:{outputSourceFiles:!0},paths:this.rootPath?[this.rootPath]:[],filename:r,globalVars:null!==(o=null==s?void 0:s.globalVars)&&void 0!==o?o:{}}),l=i.css.replace(exports.importWxssCssReg,(e,s,t)=>{const r=t.slice(0,-4);return a.includes(r)?e.replace(t,r):e});if(i.imports.length)for(const e of i.imports)this.addWatchFile(e);let p=void 0;return i.map&&(p=JSON.parse(i.map),p.sources=p.sources.map(s=>path_1.default.posix.relative(e,s))),{sourceCode:l,inputMap:p}}catch(e){throw(0,error_1.makeSummerError)(e,error_1.SummerErrors.SUMMER_PLUGIN_CODE_ERR)}}}}}exports.importWxssReg=/(?:^|\s)?(?:@import)(?:\s*)?(["'])([^"']+.wxss)\1(?:\s*);/g,exports.importWxssCssReg=/(?:^|\s)?(?:@import)(?:\s*)?(["'])([^"']+.wxss\?css)\1(?:\s*);/g,exports.default=default_1;
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const tslib_1=require("tslib"),types_1=require("../types"),fs_1=tslib_1.__importDefault(require("fs")),error_1=require("../error"),babel7=()=>require("@babel/core"),pluginTransformTypescript=()=>require("@babel/plugin-transform-typescript"),pluginReplaceTsExportAssignment=e=>{const r=e.template("\n    module.exports = ASSIGNMENT;\n  ");return{name:"replace-ts-export-assignment",visitor:{TSExportAssignment(e){e.replaceWith(r({ASSIGNMENT:e.node.expression}))}}}},pluginReplaceTsImportEqualsDeclaration=e=>{const r=e.template("\n    const ID = require(SOURCE);\n  ");return{name:"replace-ts-import-equals-declaration",visitor:{TSImportEqualsDeclaration(e){e.replaceWith(r({ID:e.node.id,SOURCE:e.node.moduleReference.expression}))}}}};function default_1(){return{name:"summer-typescript",resolveExt:{js:"ts"},workerMethods:{doTransform(e,r){let t;try{t=require("@babel/core").transform(e,{babelrc:!1,plugins:[[pluginReplaceTsImportEqualsDeclaration,{}],[pluginReplaceTsExportAssignment,{}],[require("@babel/plugin-transform-typescript"),{}]],sourceFileName:r,sourceMaps:!1,ast:!0,configFile:!1,code:!1})}catch(e){throw(0,error_1.makeSummerError)(e,error_1.SummerErrors.SUMMER_PLUGIN_CODE_ERR,r)}return{sourceCode:e,inputMap:void 0,astInfo:{ast:t.ast,type:types_1.AstType.Babel}}}},async load(e,r){if(r.endsWith(".ts")){const e=fs_1.default.readFileSync(r,{encoding:"utf-8"});return await this.runWorkerMethod("doTransform",e,r)}}}}exports.default=default_1;
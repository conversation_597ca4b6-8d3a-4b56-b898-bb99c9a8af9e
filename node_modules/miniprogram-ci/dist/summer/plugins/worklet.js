"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const config_1=require("../../config"),types_1=require("../types"),_pluginTransformWorklet=()=>require("../../utils/babel_plugin_worklet"),babel7=()=>require("@babel/core"),worklet=(e,r)=>{const t=babel7();let o;try{const s={babelrc:!1,plugins:[require("../../utils/babel_plugin_worklet")],sourceFileName:r,inputSourceMap:!1,configFile:!1,code:!1,ast:!0,cloneInputAst:!1};if(e.astInfo){if(e.astInfo.type!==types_1.AstType.Babel)throw new Error("ast type is not babel");o=t.transformFromAstSync(e.astInfo.ast,e.sourceCode,s)}else{if(null===e.sourceCode)throw new Error("source.targetCode is null");o=babel7().transform(e.sourceCode,s)}}catch(e){const t=`file: ${r}\n ${e.message}`,o=new Error(t);throw o.code=config_1.BABEL_TRANS_JS_ERR,o}return{sourceCode:e.sourceCode,inputMap:e.inputMap,astInfo:{ast:o.ast,type:types_1.AstType.Babel}}};function default_1(e,r){return{name:"summer-worklet",workerMethods:{worklet:worklet},async transform(e,r,t,{isBabelIgnore:o}){if(r.endsWith(".js")&&!o){return e.sourceCode.includes('"worklet"')||e.sourceCode.includes("'worklet'")?await this.runWorkerMethod("worklet",e,t):e}return e}}}exports.default=default_1;
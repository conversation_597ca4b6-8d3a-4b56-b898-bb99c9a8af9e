"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.random=void 0;const tslib_1=require("tslib"),tools_1=require("../../utils/tools"),error_1=require("../error"),fs_extra_1=tslib_1.__importDefault(require("fs-extra")),path_1=tslib_1.__importDefault(require("path")),common_1=require("../../utils/common"),sass=()=>require("sass");function random(){return(0,tools_1.generateMD5)(`${Math.random()}${Date.now()}`)}exports.random=random;const importWxssReg=/(?:^|\s)?(?:@import)(?:\s*)?(["'])([^"']+.wxss)\1(?:\s*);/g,importCssReg=/(?:^|\s)?(?:@import)(?:\s*)?(["'])([^"']+.css)\1(?:\s*);/g;function default_1(s){return{name:"summer-sass",resolveExt:{wxss:["sass","scss"]},async load(t,e){if(e.endsWith(".scss")||e.endsWith(".sass")){let t=await fs_extra_1.default.readFile(e,"utf-8");if(this.rootPath){const s=(0,tools_1.normalizePath)(await(0,common_1.getMiniprogramRoot)(this.rootPath)),o=path_1.default.posix.join(s,"global");if(e!==o+".scss"&&e!==o+".sass"){let o=path_1.default.extname(e);const r=(0,tools_1.pathRelative)(s,path_1.default.dirname(e))||".";fs_extra_1.default.existsSync(path_1.default.posix.join(s,"global"+o))||(o=".sass"===o?".scss":".sass"),fs_extra_1.default.existsSync(path_1.default.posix.join(s,"global"+o))&&(t=".sass"===o?`@use '${"."===r?".":(0,tools_1.pathRelative)(r,"./")}/global${o}'\n${t}`:`@use '${"."===r?".":(0,tools_1.pathRelative)(r,"./")}/global${o}';\n${t}`)}}const o=[];t=t.replace(importWxssReg,(s,t,e)=>(o.push(e),s.replace(e,e.slice(0,-4)+"css")));const r=path_1.default.posix.dirname((0,tools_1.pathRelative)(s,e));return new Promise((s,a)=>{require("sass").render({file:e,data:t,sourceMap:!0,sourceMapContents:!0,omitSourceMapUrl:!0,outFile:e.slice(0,-5)+".wxss",includePaths:this.rootPath?[this.rootPath]:[]},(t,i)=>{if(t){const s=(0,error_1.makeSummerError)(t,error_1.SummerErrors.SUMMER_PLUGIN_CODE_ERR);return void a(s)}if(!i)return void a(new Error("no result"));const l=i.css.toString("utf-8").replace(importCssReg,(s,t,e)=>{const r=e.slice(0,-3)+"wxss";return o.includes(r)?s.replace(e,r):s});if(i.stats.includedFiles.length>0)for(const s of i.stats.includedFiles)s!==e&&this.addWatchFile(s);const n=i.map?JSON.parse(i.map.toString("utf-8")):void 0;n&&(n.sourceRoot=r),s({sourceCode:l,inputMap:n})})})}}}}exports.default=default_1;
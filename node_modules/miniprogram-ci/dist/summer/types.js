!function(require, directRequire){
"use strict";var FileType,JSONType,AstType;Object.defineProperty(exports,"__esModule",{value:!0}),exports.AstType=exports.JSONType=exports.FileType=exports.BasicCodeExts=void 0,exports.BasicCodeExts=["js","wxml","wxss","json","wxs"],function(e){e.JSON="json",e.WXML="wxml",e.WXSS="wxss",e.WXS="wxs",e.JS="js"}(FileType=exports.FileType||(exports.FileType={})),function(e){e.APP="app",e.Page="page",e.EXT="ext",e.SITEMAP="sitemap",e.THEME="theme"}(JSONType=exports.JSONType||(exports.JSONType={})),function(e){e.Babel="babel",e.Acorn="acorn"}(AstType=exports.AstType||(exports.AstType={}));
}(require("licia/lazyImport")(require), require)
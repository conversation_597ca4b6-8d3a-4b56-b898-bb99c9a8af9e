!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const tslib_1=require("tslib"),tools_1=require("../utils/tools"),fs=tslib_1.__importStar(require("fs-extra")),path_1=tslib_1.__importDefault(require("path"));class PersistCache{constructor(e){this.cachePath=e}getFilePath(e){const t=(0,tools_1.generateMD5)(e);return{cacheFile:path_1.default.join(this.cachePath,t),infoFile:path_1.default.join(this.cachePath,t+".json")}}async get(e){const{cacheFile:t,infoFile:a}=this.getFilePath(e);try{const e=await fs.readFile(a,"utf8"),s=JSON.parse(e);return{data:await fs.readFile(t,{encoding:s.encoding||null}),info:s}}catch(e){}return{}}async set(e,t){var a;const{cacheFile:s,infoFile:i}=this.getFilePath(e);try{const e=(null===(a=t.info)||void 0===a?void 0:a.encoding)||null;await fs.writeFile(s,t.data,e),await fs.writeFile(i,JSON.stringify(t.info),"utf8")}catch(e){}}async remove(e){const{cacheFile:t,infoFile:a}=this.getFilePath(e);try{await fs.unlink(t),await fs.unlink(a)}catch(e){}}async clean(){const e=fs.readdirSync(this.cachePath);await Promise.all(e.map(async e=>{try{await fs.unlink(path_1.default.join(this.cachePath,e))}catch(e){}}))}}class FakePersistCache{async get(e){return{}}async set(e,t){}async remove(e){}async clean(){}}class LogicPersistCache{constructor(e,t){this.baseCacheKey=t,this.persistCache=e?new PersistCache(e):new FakePersistCache}updateBaseCacheKey(e){this.baseCacheKey!==e&&(this.persistCache.clean(),this.baseCacheKey=e)}getCacheKey(e){return`${e.independentRoot}|${e.source}`}async get(e,t,a){var s;const i=path_1.default.posix.join(e,t,a.path),c=path_1.default.posix.join(e,t,a.source),n=await this.persistCache.get(i);if((null===(s=n.info)||void 0===s?void 0:s.baseCacheKey)===this.baseCacheKey){if(n.info&&n.info.cacheKey===this.getCacheKey(a)){const e=fs.statSync(c);if(n.info.mtimeMs===e.mtimeMs)return console.log("use cache",a.path),JSON.parse(n.data)}}else this.persistCache.remove(i)}async set(e,t,a,s){const i=path_1.default.posix.join(e,t,a.path),c=path_1.default.posix.join(e,t,a.source),n=fs.statSync(c);this.persistCache.set(i,{info:{encoding:"utf8",cacheKey:this.getCacheKey(a),baseCacheKey:this.baseCacheKey,mtimeMs:n.mtimeMs},data:JSON.stringify(s)})}async clean(){this.persistCache.clean()}}exports.default=LogicPersistCache;
}(require("licia/lazyImport")(require), require)
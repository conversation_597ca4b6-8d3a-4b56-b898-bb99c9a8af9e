!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.PluginDriver=exports.getPluginContext=exports.genResovleExtConf=void 0;const error_1=require("./error"),worker_thread_1=require("../core/worker_thread");function genResovleExtConf(r){const e={json:["json"],wxml:["wxml"],wxss:["wxss"],js:["js"],wxs:["wxs"]};for(const o of r)if(o.resolveExt)for(const r of["json","wxml","wxss","js","wxs"]){const t=o.resolveExt[r];if("string"==typeof t)e[r].includes(t)||e[r].push(t);else if(Array.isArray(t))for(const o of t)e[r].includes(o)||e[r].push(o)}return e}function throwPluginError(r,e,{hook:o,id:t}={}){const n=(0,error_1.makeSummerError)(r);throw n.code||(n.code=error_1.SummerErrors.SUMMER_PLUGIN_ERR),n.plugin=e,o&&(n.hook=o),t&&!n.path&&(n.path=t),r.stack&&(n.stack=r.stack),n}function getPluginContext(r,e){return{addWatchFile(){throw new Error("addWatchFile should be implemented by replaceContext")},async runWorkerMethod(o,...t){const n={command:"runMethod",plugin:r.name,projectPath:e.project.projectPath,pluginOption:{},method:o,args:t},s=await(0,worker_thread_1.runTask)(worker_thread_1.TASK_NAME.SUMMER_HOOK,n);if(s.error){if("SummerError"===s.error.type)throw new error_1.SummerError(s.error);throw s.error}return s.result},error:e=>throwPluginError(e,r.name)}}exports.genResovleExtConf=genResovleExtConf,exports.getPluginContext=getPluginContext;class PluginDriver{constructor(r,e){this.graph=r,this.options=e,this.pluginContexts=new Map,this.plugins=e.plugins,this.resolveExtConf=genResovleExtConf(e.plugins);for(const e of this.plugins)this.pluginContexts.set(e,getPluginContext(e,r))}runHook(r,e,o,t){const n=o[r];if(!n)return;let s=this.pluginContexts.get(o);return t&&(s=t(s,o)),Promise.resolve().then(()=>n.apply(s,e)).catch(e=>throwPluginError(e,o.name,{hook:r}))}hookFirst(r,e,o){let t=Promise.resolve(void 0);for(const n of this.plugins)t=t.then(t=>null!=t?t:this.runHook(r,e,n,o));return t}hookParallel(r,e,o){const t=[];for(const n of this.plugins){const s=this.runHook(r,e,n,o);s&&t.push(s)}return Promise.all(t).then(()=>{})}hookReduceArg0(r,[e,...o],t,n){let s=Promise.resolve(e);for(const e of this.plugins)s=s.then(s=>{const i=[s,...o],l=this.runHook(r,i,e,n);return l?l.then(r=>t.call(this.pluginContexts.get(e),s,r,e)):s});return s}}exports.PluginDriver=PluginDriver;
}(require("licia/lazyImport")(require), require)
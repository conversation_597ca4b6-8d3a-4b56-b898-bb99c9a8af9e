!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Resolver=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),targetCodeExts=["json","wxml","wxss","js","wxs"];function getAllExts(t){const e=[];for(const s of targetCodeExts)for(const o of t[s])e.includes(o)||e.push(o);return e}function getExtToTarget(t){const e={};for(const s of targetCodeExts){const o=[];for(const i of t[s])e[i]=e[i]||{},e[i][s]=[...o],o.push(i)}return e}class Resolver{constructor(t,e,s){this.graph=t,this.root=e,this.extConf=s,this.fileSet=new Set,this.resolveInfoMap=new Map,this.onFileChange=(t,e)=>{var s;if(this.isCodeFile(e)&&("unlink"===t||"add"===t))if("add"===t)this.updateFile(e);else{const t=this.getExt(e),o=this.resolve(e);for(const e of o){const o=this.getExt(e.path);this.resolveInfoMap.delete(e.path);for(const i of this.extToTarget[t][o]){const t=e.path.slice(0,e.path.length-o.length)+i;if(null===(s=this.graph.project.stat(this.root,t))||void 0===s?void 0:s.isFile){this.updateFile(t);break}}}}},this.allExts=getAllExts(s),this.extToTarget=getExtToTarget(s),this.updateFiles()}updateFiles(){const t=this.graph.project.getFileList(this.root).map(t=>t.replace(new RegExp("^"+this.root),""));for(const e of t){const t=this.getExt(e);this.allExts.includes(t)&&(this.fileSet.add(e),this.updateFile(e))}}resolve(t){const e=[];if(this.isCodeFile(t))for(const s of this.resolveInfoMap.values())s.source===t&&e.push(s);return e}stat(t){return this.resolveInfoMap.has(t)?{isFile:!0,isDirectory:!1}:void 0}isCodeFile(t){const e=path_1.default.extname(t).replace(/^./,"");return this.allExts.includes(e)}getExt(t){return path_1.default.extname(t).replace(/^./,"")}updateFile(t){if(t.endsWith(".d.ts"))return;const e=this.getExt(t);for(const s of targetCodeExts)if(this.extToTarget[e][s]){const o=t.substr(0,t.length-e.length)+s,i=this.resolveInfoMap.get(o);if(i){if(!this.extToTarget[e][s].includes(i.sourceExt))continue}this.resolveInfoMap.set(o,{path:o,source:t,sourceExt:e})}}}exports.Resolver=Resolver;
}(require("licia/lazyImport")(require), require)
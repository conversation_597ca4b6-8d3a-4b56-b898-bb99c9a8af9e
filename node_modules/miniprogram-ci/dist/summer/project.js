!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Project=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),fs_1=tslib_1.__importDefault(require("fs")),tools_1=require("../utils/tools"),events_1=tslib_1.__importDefault(require("events"));class Project{constructor(t,e,i,r){this.projectPath=t,this._dirSet=new Set,this._fileSet=new Set,this.type="miniProgram",this.privateKey="",this.event=new events_1.default,this.updateOptions(r),this._fileSet=new Set(e.filter(t=>!this.isIgnore(t))),this._dirSet=new Set(i)}getFilesAndDirs(){return{files:Array.from(this._fileSet),dirs:Array.from(this._dirSet)}}async attr(){return this._attr}updateOptions(t){this.appid=t.appid,this._attr=t.attr,this.updateType(t.attr,t.compileType),this.miniprogramRoot=t.miniprogramRoot,this.pluginRoot=t.pluginRoot}updateType(t,e){if(null==t?void 0:t.gameApp)return"miniGamePlugin"===e?void(this.type="miniGamePlugin"):void(this.type="miniGame");this.type=e}getExtAppid(){throw new Error("Method not implemented.")}updateFiles(){throw new Error("Method not implemented.")}isIgnore(t){return t.endsWith(".d.ts")}cacheDirName(t){this._dirSet.has(t)||(this._dirSet.add(t),this.cacheDirName(path_1.default.posix.dirname(t)))}getTargetPath(t,e){return(0,tools_1.normalizePath)(path_1.default.posix.join(t,e)).replace(/\/$/,"").replace(/^\//,"")}stat(t,e){const i=this.getTargetPath(t,e);return this._fileSet.has(i)?{isFile:!0,isDirectory:!1}:this._dirSet.has(i)?{isFile:!1,isDirectory:!0}:void 0}getFileSize(t,e){const i=this.getTargetPath(t,e),r=(0,tools_1.normalizePath)(path_1.default.posix.join(this.projectPath,i));return fs_1.default.statSync(r).size}getFile(t,e){const i=this.getTargetPath(t,e),r=(0,tools_1.normalizePath)(path_1.default.posix.join(this.projectPath,i));return fs_1.default.readFileSync(r,null)}getJson(t,e){const i=this.getFile(t,e).toString("utf-8");try{return JSON.parse(i)}catch(i){throw new Error("JSON parse failed: "+this.getTargetPath(t,e))}}getFileList(t,e=""){return Array.from(this._fileSet).filter(i=>(!e||path_1.default.posix.extname(i)===e)&&(!t||0===i.indexOf(t)))}onFileChange(t,e){if("add"!==t&&"addDir"!==t||(this.cacheDirName(path_1.default.posix.dirname(e)),this.isIgnore(e)||this._fileSet.add(e)),"addDir"===t&&this.cacheDirName(e),"unlink"===t&&this._fileSet.has(e)&&this._fileSet.delete(e),"unlinkDir"===t&&this._dirSet.has(e)){this._dirSet.delete(e);const t=e+"/",i=Array.from(this._dirSet);for(const e of i)0===e.indexOf(t)&&this._dirSet.delete(e);const r=Array.from(this._fileSet);for(const e of r)0===e.indexOf(t)&&this._fileSet.delete(e)}this.event.emit("fileChange",t,e)}}exports.Project=Project;
}(require("licia/lazyImport")(require), require)
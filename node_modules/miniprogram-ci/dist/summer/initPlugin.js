!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.initPlugin=void 0;const tslib_1=require("tslib"),index_1=tslib_1.__importDefault(require("./plugins/index"));function initPlugin(i,e,t){i.startsWith("summer-")&&(i=i.replace("summer-",""));const n=index_1.default.load(i);if(n)return n(e,t);throw new Error("not found plugin for "+i)}exports.initPlugin=initPlugin;
}(require("licia/lazyImport")(require), require)
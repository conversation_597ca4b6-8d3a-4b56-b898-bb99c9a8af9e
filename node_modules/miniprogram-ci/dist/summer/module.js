!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const types_1=require("./types"),tools_1=require("../utils/tools"),error_1=require("./error");class JsTag{constructor(){this.isLargeFile=!1,this.isBabelIgnore=!1,this.helpers=[]}setBabelIgnore(){this.isBabelIgnore=!0}setLargeFile(){this.isLargeFile=!0}addHelpers(e){for(const s of e)this.helpers.includes(s)||this.helpers.push(s)}toJSON(){return{isLargeFile:this.isLargeFile,isBabelIgnore:this.isBabelIgnore,helpers:this.helpers}}}class Module{constructor(e,s,t,r){this.graph=e,this.path=s,this.sourcePath=t,this.fileType=r,this.md5="",this.depFiles=[],this.independentRoot="",this.loadStart=Date.now(),r===types_1.FileType.JS&&(this.jsTag=new JsTag)}setError(e){this.error=e,this.loadEnd=Date.now()}setSource(e){var s,t;this.source=e,e.target&&(this.target=e.target,null===(s=this.jsTag)||void 0===s||s.addHelpers(e.target.helpers||[]),e.target=void 0),e.largeFile&&(null===(t=this.jsTag)||void 0===t||t.setLargeFile()),this.fileType===types_1.FileType.JSON&&(this.json=JSON.parse(this.source.sourceCode)),this.md5=(0,tools_1.generateMD5)(this.source.sourceCode),this.loadEnd=Date.now()}toCodeFile(){var e;return this.error?this.error instanceof error_1.SummerError?{path:this.path,error:this.error.toJSON()}:{path:this.path,error:this.error}:Object.assign({path:this.path,md5:this.md5,jsTag:null===(e=this.jsTag)||void 0===e?void 0:e.toJSON()},this.target)}toJSON(){return{code:"",map:void 0,path:this.path,sourcePath:this.sourcePath,depFileIds:this.depFiles}}addWatchFile(e){-1===this.depFiles.indexOf(e)&&this.depFiles.push(e)}}exports.default=Module;
}(require("licia/lazyImport")(require), require)
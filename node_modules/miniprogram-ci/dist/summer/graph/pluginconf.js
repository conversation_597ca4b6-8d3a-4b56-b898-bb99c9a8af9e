!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.PluginConf=exports.resolvePath=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),core_1=require("../../core"),locales_1=tslib_1.__importDefault(require("../../utils/locales/locales"));function resolvePath(t,o){const e=path_1.default.posix.dirname(t);let s=null;return s=o.startsWith("/")?o.replace(/^\//,""):path_1.default.posix.join(e,o),s}exports.resolvePath=resolvePath;class PluginConf{constructor(t,o){this.graph=o,this.pages=new Map,this.comps=new Map,this.proxyProject=t.proxyProject,this.proxyProject.addResolver(o.resolver)}destroy(){this.proxyProject.removeResolver(this.graph.resolver)}async build(t){this.resetState(),await this.loadPlugin(t)}async resetState(){this.plugin=void 0,this.pages.clear(),this.comps.clear()}async loadPlugin(t){const o=await t.run(locales_1.default.config.SUMMER_COMPILE.format("plugin.json"),()=>(0,core_1.getPluginJSON)(this.proxyProject));this.plugin=o;const e=new Set;for(const t of Object.values(o.pages||{}))e.add(t);await t.run(locales_1.default.config.SUMMER_COMPILE_PLUGIN_PAGE_JSON.format(e.size),async()=>{for(const[t]of e.entries())await this.loadPage(t);for(const t of Object.values(o.publicComponents||{}))await this.loadComp(t);for(const t of Object.values(o.usingComponents||{}))await this.loadComp(t)})}async loadPage(t){const o=await(0,core_1.getPluginPageJSON)({project:this.proxyProject,root:this.graph.root,filePath:path_1.default.posix.join(this.graph.root,t+".json")});this.pages.set(t,o);for(const e of Object.values(o.usingComponents||{})){const o=resolvePath(t,e);await this.loadComp(o)}}async loadComp(t){if(this.comps.has(t))return;const o=await(0,core_1.getPluginPageJSON)({project:this.proxyProject,root:this.graph.root,filePath:path_1.default.posix.join(this.graph.root,t+".json")});this.comps.set(t,o);for(const e of Object.values(o.usingComponents||{})){const o=resolvePath(t,e);await this.loadComp(o)}}}exports.PluginConf=PluginConf;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadContainer=void 0;const tslib_1=require("tslib"),request_1=tslib_1.__importDefault(require("request")),utils_1=require("./utils"),util_1=require("util"),request_2=require("../utils/request"),request=(0,util_1.promisify)(request_1.default);async function uploadContainer(e){var t;const{currentEnv:o,cloudAPI:a}=await(0,utils_1.initEnvironmentByProject)(e.project,e.env),r=e.version;if("package"===r.uploadType){const o=await a.tcbDescribeCloudBaseBuildService({envId:e.env,serviceName:e.version.serverName}),i=(0,utils_1.zipFile)(e.containerRoot,{});console.log("[ ] uploading container package..."),console.time("[+] upload elapsed");const s=await request({url:o.uploadUrl,proxy:(0,request_2.getCiProxy)(),method:"PUT",headers:Object.assign({"Content-type":"application/zip"},null===(t=o.uploadHeaders)||void 0===t?void 0:t.reduce((e,{key:t,value:o})=>(e[t]=o,e),{})),body:i.generateNodeStream()});console.timeEnd("[+] upload elapsed"),console.log("[+] upload file status",s.statusCode),r.packageName=o.packageName,r.packageVersion=o.packageVersion}const i=await a.tcbCreateCloudBaseRunServerVersion(Object.assign(Object.assign({},r),{envId:o.envId}));return console.log("[+] create version done.",i),i}exports.uploadContainer=uploadContainer;
}(require("licia/lazyImport")(require), require)
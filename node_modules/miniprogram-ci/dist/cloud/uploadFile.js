!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadFiles=void 0;const tslib_1=require("tslib"),log_1=tslib_1.__importDefault(require("../utils/log")),error_1=require("../utils/error"),config_1=require("../config"),locales_1=tslib_1.__importDefault(require("../utils/locales/locales")),request_1=require("../utils/request"),sign_1=require("../utils/sign"),urlConfig=tslib_1.__importStar(require("../utils/url_config")),utils_1=require("./utils"),{resolve:resolve,relative:relative}=require("path"),{readdir:readdir,readFile:readFile}=require("fs").promises,COS=require("cos-nodejs-sdk-v5"),requiredParams=["project","env","path"];async function uploadFiles(e,t){var r,i;const{currentEnv:o}=await(0,utils_1.initEnvironmentByProject)(e.project,e.env);requiredParams.forEach(t=>{if(!e[t])throw new error_1.CodeError(locales_1.default.config.PARAM_ERROR.format("cloud.storage",t),config_1.PARAM_ERROR)});const{project:a,remotePath:s="",path:n,concurrency:l=5}=e;let c=s.endsWith("/")?s:s+"/";if(c.startsWith("/")&&(c=c.slice(1)),"staticstorage"!==t)if("storage"!==t);else{const e=null===(i=o.storages)||void 0===i?void 0:i[0];if(!e)throw new Error(`invalid storage status: ${e}. exiting`);await beginUpload(n,e,c,a,l)}else{const e=null===(r=o.staticStorages)||void 0===r?void 0:r[0];if(!e||"online"!==e.status)throw new Error(`invalid static storage status: ${e.status}. exiting`);await beginUpload(n,e,c,a,l)}}async function beginUpload(e,t,r,i,o){const a=await getFiles(e),s=a.map(t=>relative(e,t));log_1.default.info("[storage]","done. begin upload to cos.");const n=await putObjectToCos(s.map((e,i)=>({Bucket:t.bucket,Region:t.region,Key:r+e,Body:a[i]})),i,t,o);log_1.default.info("[storage]",n)}async function getFiles(e){const t=await readdir(e,{withFileTypes:!0}),r=await Promise.all(t.map(t=>{const r=resolve(e,t.name);return t.isDirectory()?getFiles(r):r}));return Array.prototype.concat(...r)}async function putObjectToCos(e,t,r,i){const o=new COS({Proxy:(0,request_1.getCiProxy)(),getAuthorization:await getAuthorizationThunk(t,r)}),a=await callCiServerApi(t,"CLOUD_ROUTE_GETCOSMETA",{action:"batchencode",bucket:r.bucket,mpappid:t.appid,paths:e.map(e=>e.Key)},e=>JSON.parse(JSON.parse(e).data.data).x_cos_meta_field_strs),s=new Map(e.map((e,t)=>[e.Key,a[t]]));try{const t=e.length;let r="";const a=async e=>{if(!e)return;const t=await o.putObject(Object.assign(Object.assign({},e),{Body:await readFile(e.Body),Headers:{"x-cos-meta-fileid":s.get(e.Key)}}));return r=e.Key,t};for(await a(e.shift());e.length>0;){const o=e.splice(0,Math.min(e.length,i)).map(a);log_1.default.info(`[storage] Uploading ${t-e.length} / ${t}: ${r}`),await Promise.all(o)}return{success:!0}}catch(e){throw new Error("upload to cos failed: "+e.message)}}async function getAuthorizationThunk(e,t){return async function(r,i){var o;const a=Date.now(),s=await callCiServerApi(e,"CLOUD_ROUTE_GETCOSAUTH",{region:t.region,bucket:t.bucket}),n=null===(o=null==s?void 0:s.Response)||void 0===o?void 0:o.Credentials;if(!n)throw new Error("getFederalToken failed: "+JSON.stringify(n));i({TmpSecretId:n.TmpSecretId,TmpSecretKey:n.TmpSecretKey,XCosSecurityToken:n.Token,StartTime:~~(a/1e3),ExpiredTime:s.Response.ExpiredTime})}}async function callCiServerApi(e,t,r,i){const o=await(0,sign_1.getSignature)(e.privateKey,e.appid),a=await e.getExtAppid(),{resp:s,body:n}=await(0,request_1.request)({url:urlConfig.cloudCosUploadURL,method:"post",body:JSON.stringify({appid:e.appid,extAppid:a||void 0,signature:o,action:t,agentReq:r}),headers:{"content-type":"application/json"}});if(413===s.statusCode)throw new Error("Body too large");if(!n)throw new Error("Empty body "+JSON.stringify(s));if(i){return i(n)}const l="string"==typeof n?JSON.parse(n):n,c=JSON.parse(l.data.content);if(l.errCode)throw new Error(`${l.errCode} ${l.errMsg}`);return c}exports.uploadFiles=uploadFiles;
}(require("licia/lazyImport")(require), require)
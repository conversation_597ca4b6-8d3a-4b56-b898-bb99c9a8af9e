!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.uploadFunction=void 0;const tslib_1=require("tslib"),cloudapi_1=require("./cloudapi"),cloudAPI=tslib_1.__importStar(require("../vendor/cloud-api")),utils_1=require("./utils"),log_1=tslib_1.__importDefault(require("../utils/log")),error_1=require("../utils/error"),config_1=require("../config"),locales_1=tslib_1.__importDefault(require("../utils/locales/locales")),HelloWordCode="UEsDBBQACAAIALB+WU4AAAAAAAAAAAAAAAAIABAAaW5kZXguanNVWAwAAZ9zXPuec1z1ARQAdY7BCsIwEETv+Yoll6ZQ+wOhnv0DD+IhxkWC664kWwmI/27V3IpzGuYNw3RzQSiaU9TOG6x3yVrGW0gMEzh8IOsAUVixfkwgOoV47WHawtPAooUVIRxJLs7ukEhgL5nOtl/h79qf+GBZeIM1FbXHdac9aKC9cDwTDfCb9eblzRtQSwcI6+pcr4AAAADOAAAAUEsBAhUDFAAIAAgAsH5ZTuvqXK+AAAAAzgAAAAgADAAAAAAAAAAAQKSBAAAAAGluZGV4LmpzVVgIAAGfc1z7nnNcUEsFBgAAAAABAAEAQgAAAMYAAAAAAA==",requiredParams=["project","name","path"];async function uploadFunction(e){requiredParams.forEach(t=>{if(!e[t])throw new error_1.CodeError(locales_1.default.config.PARAM_ERROR.format("cloud.uploadFunction",t),config_1.PARAM_ERROR)});const{project:t,remoteNpmInstall:o=!1,name:n,path:a,env:c}=e,i=await t.getExtAppid();(0,cloudapi_1.initCloudAPI)(i||t.appid),log_1.default.info(`will upload code under ${a} as cloudfunction '${n}' of env ${c}. remote-npm-install: ${o}`);const{envList:u}=await cloudAPI.tcbGetEnvironments({},{request:(0,cloudapi_1.boundTransactRequest)(t),transactType:cloudAPI.TransactType.IDE}),r=u.find(e=>e.envId===c);if(!r)throw new Error("env not found");const l=r.functions[0].region,{clsLogsetId:s,clsTopicId:d}=getLogServiceProperties(r),p=await(0,cloudapi_1.get3rdCloudCodeSecret)(t);log_1.default.info("checking cloudfunction status, will only proceed on normal status"),await waitFuncDeploy({namespace:c,region:l,functionName:n,codeSecret:p,topts:{request:(0,cloudapi_1.boundTransactRequest)(e.project),transactType:cloudAPI.TransactType.IDE}});let A,f=!1;try{if(A=await cloudAPI.scfGetFunctionInfo({namespace:c,region:"",functionName:n,codeSecret:p},{request:(0,cloudapi_1.boundTransactRequest)(t),transactType:cloudAPI.TransactType.IDE}),"DeleteFailed"===A.status)throw new Error("delete failed")}catch(e){if("ResourceNotFound.Function"!==e.code)throw e;f=!0}if(log_1.default.info("get cloudfunction info done"),log_1.default.info(`will ${f?"create":"update"} cloudfunction`),f)await cloudAPI.scfCreateFunction({functionName:n,code:{zipFile:HelloWordCode},handler:"index.main",description:"",memorySize:256,timeout:3,environment:{variables:[]},role:"TCB_QcsRole",runtime:"Nodejs8.9",namespace:c,region:l,stamp:"MINI_QCBASE",installDependency:o,codeSecret:p,clsLogsetId:s,clsTopicId:d},{request:(0,cloudapi_1.boundTransactRequest)(e.project),transactType:cloudAPI.TransactType.IDE}),log_1.default.info("create cloudfunction done, continue to update code");else{if("Updating"===A.status)throw new Error("there's another ongoing update, please wait for it to complete and try again later");const t=o?"TRUE":"FALSE";A.installDependency!==t&&(log_1.default.info("updating cloudfunction info"),await cloudAPI.scfUpdateFunctionInfo({namespace:c,region:l,functionName:n,installDependency:o,clsLogsetId:s,clsTopicId:d},{request:(0,cloudapi_1.boundTransactRequest)(e.project),transactType:cloudAPI.TransactType.IDE}),log_1.default.info("update cloudfunction info done, waiting for it to take into effect"),await waitFuncDeploy({namespace:c,region:l,functionName:n,codeSecret:p,topts:{request:(0,cloudapi_1.boundTransactRequest)(e.project),transactType:cloudAPI.TransactType.IDE}}),log_1.default.info("cloudfunction info updated"))}const g=(0,utils_1.zipFile)(a,{ignore:o?["node_modules"]:void 0}),I=await(0,utils_1.zipToBuffer)(g);log_1.default.info("zip file done, updating cloudfunction code"),await cloudAPI.scfUpdateFunction({functionName:n,namespace:c,region:l,handler:"index.main",installDependency:o,fileData:I.toString("base64"),codeSecret:p},{request:(0,cloudapi_1.boundTransactRequest)(e.project),transactType:cloudAPI.TransactType.IDE}),log_1.default.info("cloudfunction code updated, "+(o?"installing dependencies in the cloud and deploying":"deploying")),await waitFuncDeploy({namespace:c,region:l,functionName:n,codeSecret:p,topts:{request:(0,cloudapi_1.boundTransactRequest)(e.project),transactType:cloudAPI.TransactType.IDE}}),log_1.default.info("deployed");return{filesCount:Object.keys(g.files).length,packSize:I.byteLength}}exports.uploadFunction=uploadFunction;const waitFuncDeploy=async e=>new Promise(async(t,o)=>{let n=!1;const{namespace:a,region:c,functionName:i,onStatusUpdate:u=(t=>{log_1.default.info(`env ${e.namespace}'s cloudfunction '${e.functionName}' status: ${t}`)}),maxWaitTimeout:r=9e5,codeSecret:l}=e,s=setTimeout(()=>{n||(n=!0,o(new Error("timeout waiting for function to deploy")))},r);try{let o="";const s=+new Date;for(;!n&&+new Date-s<r;){const r=await cloudAPI.scfGetFunctionInfo({namespace:a,region:c,functionName:i,codeSecret:l},e.topts);switch(r.status!==o&&(u(r.status),o=r.status),r.status){case"Creating":case"Updating":case"Publishing":case"UpdatingAndPublishing":case"UpdateFailed":break;case"CreateFailed":throw new Error("create function failed: "+r.statusDesc);case"Active":n=!0,t(void 0)}}}catch(e){try{log_1.default.error(`upload ${a} ${i} failed: `,"string"==typeof e?e:JSON.stringify(e))}catch(e){log_1.default.error(`upload ${a} ${i} failed: `,e.toString())}clearTimeout(s),o(e)}});function getLogServiceProperties(e){let t,o;try{const n=e.logServices[0];t=n.logsetId,o=n.topicId}catch(e){}return{clsLogsetId:t,clsTopicId:o}}
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.preview=void 0;const tslib_1=require("tslib"),upload_1=require("./upload"),terminalQrcode_1=require("./utils/terminalQrcode"),fs_1=tslib_1.__importDefault(require("fs")),config_1=require("../config"),error_1=require("../utils/error"),locales_1=tslib_1.__importDefault(require("../utils/locales/locales")),log_1=tslib_1.__importDefault(require("../utils/log"));async function preview(e){const r=Object.assign({},e);if(r.test=!0,r.version="0.0.1",r.qrcodeFormat||(r.qrcodeFormat="terminal"),!["base64","image","terminal"].includes(r.qrcodeFormat))throw new error_1.CodeError(locales_1.default.config.INVALID.format("qrcodeFormat"),config_1.PARAM_ERROR);if(["base64","image"].includes(r.qrcodeFormat)&&("string"!=typeof r.qrcodeOutputDest||"."===r.qrcodeOutputDest.trim()))throw new error_1.CodeError(locales_1.default.config.INVALID.format("qrcodeOutputDest"),config_1.PARAM_ERROR);const o=await(0,upload_1.innerUpload)(r);if(!o.respBody.qrcode_img)throw new Error("No `qrcode_img` in response.");if("terminal"===r.qrcodeFormat)try{const e=await(0,terminalQrcode_1.generateTerminalQrcode)(o.respBody.qrcode_img);log_1.default.log(e),log_1.default.log("terminal qrcode shown above")}catch(e){log_1.default.error("Termianl qrcode generate failed, but you can still visit the dev version on your cell phone.")}else if(r.qrcodeOutputDest)if("image"===r.qrcodeFormat)try{fs_1.default.writeFileSync(r.qrcodeOutputDest,Buffer.from(o.respBody.qrcode_img,"base64")),log_1.default.info(`Qrcode image saved, file path: '${r.qrcodeOutputDest}'`)}catch(e){throw new Error("write qrcode image error: "+JSON.stringify(e))}else if("base64"===r.qrcodeFormat)try{fs_1.default.writeFileSync(r.qrcodeOutputDest,"data:image/jpeg;base64,"+o.respBody.qrcode_img,"utf8"),log_1.default.info(`Qrcode base64 file saved, file path: '${r.qrcodeOutputDest}'`)}catch(e){throw new Error("write qrcode base64 error: "+JSON.stringify(e))}return{subPackageInfo:o.subPackageInfo,pluginInfo:o.pluginInfo}}exports.preview=preview;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.innerUpload=exports.upload=exports.SIGNATURE_FILE_NAME=void 0;const tslib_1=require("tslib"),request_1=require("../utils/request"),compile_1=require("../core/compile"),pack_1=require("./utils/pack"),zlib_1=tslib_1.__importDefault(require("zlib")),sign_1=require("../utils/sign"),tools_1=require("../utils/tools"),config_1=require("../config"),taskstatus_1=require("../utils/taskstatus"),log_1=tslib_1.__importDefault(require("../utils/log")),error_1=require("../utils/error"),locales_1=tslib_1.__importDefault(require("../utils/locales/locales")),querystring_1=tslib_1.__importDefault(require("querystring")),url_config_1=require("../utils/url_config"),jsonParse_1=require("../utils/jsonParse"),cache_1=require("../utils/cache"),cos_upload_1=require("./cos-upload"),filterUnusedFile_1=require("./utils/filterUnusedFile");exports.SIGNATURE_FILE_NAME="ci.signature";const MIN_COS_UPLOAD_SIZE=5242880;async function upload(e){var o;const t=await innerUpload(e);return(null===(o=t.respBody)||void 0===o?void 0:o.dev_plugin_id)&&(log_1.default.log("Development Version Plugin ID: "+t.respBody.dev_plugin_id),t.devPluginId=t.respBody.dev_plugin_id),{subPackageInfo:t.subPackageInfo,pluginInfo:t.pluginInfo,devPluginId:t.devPluginId}}async function innerUpload(e){const{project:o,setting:t={},desc:r=`robot ${e.robot||"1"} use miniprogram-ci to upload at ${(0,tools_1.formatTime)(new Date)}`,version:i="",robot:a="1",onProgressUpdate:l=function(e){console.log(""+e)},test:s,pagePath:n,searchQuery:u,threads:p=0,bigPackageSizeSupport:_,allowIgnoreUnusedFiles:d=!0}=e;let{useCOS:c}=e;if(process.env.COMPILE_THREADS=p.toString(),!i)throw new error_1.CodeError(locales_1.default.config.PARAM_ERROR.format("upload","version"),config_1.PARAM_ERROR);if(!o)throw new error_1.CodeError(locales_1.default.config.PARAM_ERROR.format("upload","project"),config_1.PARAM_ERROR);cache_1.cacheManager.clean();let f=await(0,compile_1.compile)(o,{setting:t,onProgressUpdate:l});!1!==d&&(f=await(0,filterUnusedFile_1.filterUnusedFile)(!!s,o,f));const g=config_1.CI_VERSION,b={codeprotect:t.codeProtect?1:0,type:o.type,appid:o.appid,version:i,desc:r,robot:a},y={scene:e.scene||1011};let S;n&&(y.path=n,b.path=n),u&&(y.query=querystring_1.default.parse(u)),b.debugLaunchInfo=JSON.stringify(y),n&&u&&(b.path+="?"+u),s&&_&&(b.bigPackageSizeSupport=1);let q={};try{S=await o.getFile(o.miniprogramRoot,"ext.json"),q=JSON.parse(S.toString("utf-8"))}catch(e){}if(q&&(q.extEnable&&(b.extAppId=q.extAppid),q.directCommit)){let e="";e=q.extEnable?"The code will be uploaded into the waiting list of extAppid.":"The code will be uploaded into the draft box of the third-party platform.",log_1.default.warn(e)}try{const e=new taskstatus_1.TaskStatus(locales_1.default.config.UPLOAD.toString());l(e);const t=`${s?url_config_1.TEST_SOURCE_URL:url_config_1.UPLOAD_URL}?${querystring_1.default.stringify(b)}`;let r,i=!1,n=0;const u=(0,pack_1.pack)(f),p=zlib_1.default.gzipSync(u.buffer);if(log_1.default.info("useCOS parameter: ",c),log_1.default.info("upload zip buffer size: ",p.length),void 0===c&&(c=p.length>5242880),c){log_1.default.info("upload by COS: ",c);const e=await(0,cos_upload_1.uploadByCos)(p,t,o,a);e.fallback?(i=e.fallback,log_1.default.info("upload by COS failed, fallback to http way")):(r={body:e.body},n=e.uploadCOSCostTime)}if(!c||i){const e=await(0,sign_1.getSignature)(o.privateKey,o.appid);f[exports.SIGNATURE_FILE_NAME]=JSON.stringify({signature:e,version:g});const i=(0,pack_1.pack)(f),a=zlib_1.default.gzipSync(i.buffer);log_1.default.info("request url:",t);let l=(await(0,request_1.request)({url:t,method:"post",body:a})).body.toString();if(r=(0,jsonParse_1.jsonRespParse)(l,t),0!==r.errCode)throw new Error(l)}e.done(),l(e);const _={respBody:r.body};if(Array.isArray(r.body.subpackage_info)){const e=r.body.subpackage_info;_.subPackageInfo=e}if(Array.isArray(r.body.ext_plugin_info)){const e=r.body.ext_plugin_info;_.pluginInfo=e.map(e=>({pluginProviderAppid:e.provider,version:e.version,size:e.size}))}return _}catch(e){throw new error_1.CodeError(e.toString(),config_1.UPLOAD_CGI_ERR)}}exports.upload=upload,exports.innerUpload=innerUpload;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.generateTerminalQrcode=void 0;const qrcodeTerminal=require("qrcode-terminal"),QrCode=require("qrcode-reader"),Jimp=require("jimp");async function generateTerminalQrcode(e){return new Promise((r,o)=>{const n=Buffer.from(e,"base64");Jimp.read(n,(function(n,t){n&&o(n);const c=new QrCode;c.callback=function(n,t){n&&o(n),t&&t.result?qrcodeTerminal.generate(t.result,e=>{r(e)}):o("qrcode decode error, no result, qrcodeDataURI: "+e)},c.decode(t.bitmap)}))})}exports.generateTerminalQrcode=generateTerminalQrcode;
}(require("licia/lazyImport")(require), require)
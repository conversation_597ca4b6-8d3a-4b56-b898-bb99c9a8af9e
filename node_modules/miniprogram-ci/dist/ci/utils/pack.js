!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.pack=void 0;const B_PROTOCOL=0,B_PROTOCOL_VERSION=1,B_FILEINFO_LEN=2,B_FILEDATA_LEN=3,B_PROTOCOL_END=4;function pack(t){const e=[Buffer.alloc(1),Buffer.alloc(4),Buffer.alloc(4),<PERSON>uff<PERSON>.alloc(4),Buffer.alloc(1)];e[0][0]=190,e[1].writeInt32BE(0,0),e[4][0]=237;const f=Object.keys(t),r=f.length,c=[],n=[];let o=0;for(const e of f){const f=e.replace(/\\/g,"/"),r=f.startsWith("/")?f:"/"+f,l=Buffer.from(r);c.push(l);const a=t[e];let B;if(a instanceof Buffer)B=a;else{if("string"!=typeof a)throw new Error("pack invalid data: "+e);B=Buffer.from(a,"utf8")}n.push(B),/\.js\.map$/.test(e)||(o+=B.length,o+=l.length)}let l=18+12*r+Buffer.concat(c).length;const a=c.map((t,e)=>{const f=Buffer.alloc(4);f.writeInt32BE(t.length,0);const r=Buffer.alloc(4),c=n[e].length,o=l;r.writeInt32BE(o,0),l+=c;const a=Buffer.alloc(4);return a.writeInt32BE(c,0),Buffer.concat([f,t,r,a])}),B=Buffer.alloc(4);B.writeInt32BE(r,0),a.unshift(B);const s=Buffer.concat(a),u=Buffer.concat(n);e[2].writeInt32BE(s.length,0),e[3].writeInt32BE(u.length,0);const i=Buffer.concat(e);return{validSize:o,buffer:Buffer.concat([i,s,u])}}exports.pack=pack;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.packFiledir=exports.packFileMap=void 0;const tslib_1=require("tslib"),fs_1=tslib_1.__importDefault(require("fs")),path_1=tslib_1.__importDefault(require("path")),crypto_1=tslib_1.__importDefault(require("crypto")),glob_1=tslib_1.__importDefault(require("glob")),packFileMap=(e,t={})=>{const r=[Buffer.alloc(1),Buffer.alloc(4),Buffer.alloc(4),Buffer.alloc(4),Buffer.alloc(1)];r[0][0]=190,r[1].writeInt32BE(0,0),r[4][0]=237;let o=0;const l=[],c=[],f={};let i=0;for(const r in e){const n=Buffer.isBuffer(e[r])?e[r]:Buffer.from(e[r]);if(t.needMd5){const e=crypto_1.default.createHash("md5");e.update(n);const o=e.digest("hex");if(f[r]=o,t.ignoreFileMd5&&t.ignoreFileMd5[r]===o)continue}const a=Buffer.from("/"+r.replace(/\\/g,"/"));o++,l.push(a),c.push(n),/\.js\.map$/.test(r)||(i+=n.length,i+=a.length,i+=12)}let n=18+12*o+Buffer.concat(l).length;const a=l.map((e,t)=>{const r=Buffer.alloc(4);r.writeInt32BE(e.length,0);const o=Buffer.alloc(4),l=c[t].length,f=n;o.writeInt32BE(f,0),n+=l;const i=Buffer.alloc(4);return i.writeInt32BE(l,0),Buffer.concat([r,e,o,i])}),s=Buffer.alloc(4);s.writeInt32BE(o,0),a.unshift(s);const u=Buffer.concat(a),p=Buffer.concat(c);r[2].writeInt32BE(u.length,0),r[3].writeInt32BE(p.length,0);const B=Buffer.concat(r);return i+=18,{data:Buffer.concat([B,u,p]),totalSize:i,fileMd5Info:f}};exports.packFileMap=packFileMap;const packFiledir=(e,t={})=>new Promise((r,o)=>{const l=Object.assign({nodir:!0},t);(0,glob_1.default)(e+"/**",l,(l,c)=>{if(l)return o(l);const f={};c.forEach(t=>{const r=fs_1.default.readFileSync(t),o=path_1.default.relative(e,t);f[o]=r});const i=(0,exports.packFileMap)(f,t);r(i)})});exports.packFiledir=packFiledir;
}(require("licia/lazyImport")(require), require)
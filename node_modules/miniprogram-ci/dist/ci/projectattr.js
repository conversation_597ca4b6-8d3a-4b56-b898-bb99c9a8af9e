!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getProjectAttr=void 0;const tslib_1=require("tslib"),request_1=require("../utils/request"),sign_1=require("../utils/sign"),log_1=tslib_1.__importDefault(require("../utils/log")),config_1=require("../config"),url_config_1=require("../utils/url_config"),jsonParse_1=require("../utils/jsonParse"),testCache={};async function getProjectAttr(e,t){try{if(global.useAttrCache&&testCache[t])return testCache[t];const r=await(0,sign_1.getSignature)(e,t),{body:o}=await(0,request_1.request)({url:url_config_1.GET_ATTR_URL,method:"post",body:JSON.stringify({appid:t,signature:r}),headers:{"content-type":"application/json"}}),s=(0,jsonParse_1.jsonRespParse)(o,url_config_1.GET_ATTR_URL);if(0===s.errCode)return global.useAttrCache&&(testCache[t]=s.data),s.data;log_1.default.error(`get ${t} project attr fail errCode: ${s.errCode}, errMsg: ${s.errMsg}`)}catch(e){throw log_1.default.error(`get ${t} project attr fail ${e}`),e}return config_1.DefaultProjectAttr}exports.getProjectAttr=getProjectAttr;
}(require("licia/lazyImport")(require), require)
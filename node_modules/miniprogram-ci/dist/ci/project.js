!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.Project=void 0;const tslib_1=require("tslib"),path_1=tslib_1.__importDefault(require("path")),glob_1=tslib_1.__importDefault(require("glob")),fs_1=tslib_1.__importDefault(require("fs")),projectattr_1=require("./projectattr"),tools_1=require("../utils/tools"),config_1=require("../config"),locales_1=tslib_1.__importDefault(require("../utils/locales/locales")),error_1=require("../utils/error"),request_1=require("../utils/request"),projectconfig_1=require("../core/json/projectconfig"),defaultIgnores=["node_modules/**/*","**/node_modules/**","**/.git/**",".git/**/*","**/.svn/**",".svn/**/*",".DS_Store","**/.DS_Store"];class Project{constructor(t){this.miniprogramRoot="",this.pluginRoot="",this._appid="",this._projectPath="",this._dirSet=new Set,this._fileSet=new Set,this._fileBufferCache={},this._privateKey="",this._ignores=[];const{appid:e,type:i,projectPath:r,ignores:s,privateKey:o="",privateKeyPath:a=""}=t;if(!e)throw new error_1.CodeError(locales_1.default.config.SHOULD_NOT_BE_EMPTY.format("appid"),config_1.PARAM_ERROR);if(!r)throw new error_1.CodeError(locales_1.default.config.SHOULD_NOT_BE_EMPTY.format("projectPath"),config_1.PARAM_ERROR);if(o)this._privateKey=o;else{if(!a)throw new error_1.CodeError(locales_1.default.config.SHOULD_NOT_BE_EMPTY.format("privateKeyPath"),config_1.PARAM_ERROR);try{this._privateKey=fs_1.default.readFileSync(a).toString("utf8")}catch(t){throw new error_1.CodeError(t.toString(),config_1.PARAM_ERROR)}}this._appid=e,this._type=i||"miniProgram",this._projectPath=path_1.default.isAbsolute(r)?(0,tools_1.normalizePath)(r):(0,tools_1.normalizePath)(path_1.default.join(process.cwd(),r)),this.init(s)}init(t=[]){(0,request_1.initGlobalProxy)(),this._ignores=t,this.updateFiles()}cacheDirName(t){this._dirSet.has(t)||(this._dirSet.add(t),this.cacheDirName(path_1.default.posix.dirname(t)))}getTargetPath(t,e){return(0,tools_1.normalizePath)(path_1.default.posix.join(t,e)).replace(/\/$/,"").replace(/^\//,"")}updateFiles(){this._fileBufferCache={};const t=glob_1.default.sync("**",{nodir:!1,ignore:[...defaultIgnores,...this._ignores],nosort:!0,strict:!1,silent:!0,cwd:this.projectPath,absolute:!1,mark:!0,dot:!0});for(const e of t){const t=e.replace(/\\/g,path_1.default.posix.sep),i=fs_1.default.statSync(path_1.default.posix.join(this.projectPath,e));i.isDirectory()&&this.cacheDirName(t.replace(/\/$/,"")),i.isFile()&&(this._fileSet.add(t),this.cacheDirName(path_1.default.posix.dirname(t)))}}async attr(){return this._attr||(this._attr=await(0,projectattr_1.getProjectAttr)(this._privateKey,this._appid)),this._attr}get projectPath(){return this._projectPath}get appid(){return this._appid}get type(){return this._type}get privateKey(){return this._privateKey||""}getFilesAndDirs(){return{files:Array.from(this._fileSet),dirs:Array.from(this._dirSet)}}async getExtAppid(){if(this._extAppid)return this._extAppid;if(null!==this._extAppid)try{const t=await(0,projectconfig_1.getProjectConfigJSON)(this),{miniprogramRoot:e=""}=t,i=await this.getFile(e,"ext.json"),r=JSON.parse(i.toString("utf-8"));return r.extEnable&&r.extAppid?this._extAppid=r.extAppid:this._extAppid=null,this._extAppid}catch(t){this._extAppid=null}}stat(t,e){const i=this.getTargetPath(t,e);if(this._fileSet.has(i)){return{isFile:!0,isDirectory:!1,size:fs_1.default.statSync(path_1.default.posix.join(this.projectPath,i)).size}}if(this._dirSet.has(i))return{isFile:!1,isDirectory:!0}}getFile(t,e){const i=this.getTargetPath(t,e),r=(0,tools_1.normalizePath)(path_1.default.posix.join(this.projectPath,i));return this._fileBufferCache[i]?this._fileBufferCache[i]:fs_1.default.readFileSync(r,null)}getFileList(t,e=""){return Array.from(this._fileSet).filter(i=>(!e||path_1.default.posix.extname(i)===e)&&(!t||0===i.indexOf(t)))}async onFileChange(t,e){if(e=(0,tools_1.normalizePath)(e).replace(/\/$/,"").replace(/^\//,""),"add"!==t&&"addDir"!==t||(this.cacheDirName(path_1.default.posix.dirname(e)),this._fileSet.add(e)),"addDir"===t&&this.cacheDirName(e),"unlink"===t&&this._fileSet.has(e)&&this._fileSet.delete(e),"unlinkDir"===t&&this._dirSet.has(e)){this._dirSet.delete(e);const t=e+"/",i=Array.from(this._dirSet);for(const e of i)0===e.indexOf(t)&&this._dirSet.delete(e);const r=Array.from(this._fileSet);for(const e of r)0===e.indexOf(t)&&this._fileSet.delete(e)}"change"===t&&this._fileSet.has(e)&&delete this._fileBufferCache[e]}}exports.Project=Project;
}(require("licia/lazyImport")(require), require)
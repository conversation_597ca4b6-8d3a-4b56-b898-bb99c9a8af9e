export declare namespace PluginJSON {
    interface IPluginJSON {
        publicComponents: {
            [props: string]: string;
        };
        pages: {
            [page: string]: string;
        };
        usingComponents: {
            [props: string]: string;
        };
        main: string;
        themeLocation?: string;
        darkmode?: boolean;
        lazyCodeLoading?: "requiredComponents";
        workers?: string;
    }
}

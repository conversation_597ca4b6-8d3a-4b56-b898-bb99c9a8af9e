export declare namespace ProjectConfigJSON {
    interface IProjectConfigPackOption {
        type: string;
        value: string;
    }
    interface IProjectConfigJSON {
        miniprogramRoot?: string;
        pluginRoot?: string;
        pluginAppid?: string;
        jsserverRoot?: string;
        packOptions?: {
            ignore: IProjectConfigPackOption[];
            include: IProjectConfigPackOption[];
        };
        setting?: {
            babelSetting?: {
                outputPath?: string;
                ignore?: any[];
            };
            ignoreDevUnusedFiles?: boolean;
            ignoreUploadUnusedFiles?: boolean;
            useCompilerPlugins?: false | Array<string | [string, any]>;
        };
    }
}

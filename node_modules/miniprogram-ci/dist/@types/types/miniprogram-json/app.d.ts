import { BaseInfo } from './base';
export declare namespace AppJSON {
    interface IWindow {
        backgroundColorTop?: string;
        backgroundColorBottom?: string;
        backgroundColor?: string;
        enablePullDownRefresh?: boolean;
        navigationBarTextStyle?: 'black' | 'white';
        navigationBarTitleText?: string;
        navigationStyle?: 'default' | 'custom';
        backgroundTextStyle?: 'dark' | 'light';
        onReachBottomDistance?: number;
        pageOrientation?: 'portrait' | 'auto' | 'landscape';
        navigationBarBackgroundColor?: string;
        renderingMode?: 'seperated' | 'mixed';
        restartStrategy?: 'homePage' | 'homePageAndLatestPage';
    }
    interface ITabBar {
        custom?: boolean;
        list: Array<{
            pagePath: string;
            text?: string;
            iconPath?: string;
            selectedIconPath?: string;
        }>;
        borderStyle?: 'black' | 'white';
        position?: 'bottom' | 'top';
        color?: string;
        selectedColor?: string;
        backgroundColor?: string;
    }
    interface IAppJSON {
        __warning__?: string;
        pages: Array<string>;
        window?: IWindow;
        plugins?: {
            [key: string]: IPluginConfig;
        };
        entryPagePath?: string;
        permission?: {
            'scope.userLocation'?: {
                desc: string;
            };
        };
        workers?: string | {
            path: string;
            isSubpackage: boolean;
        };
        subPackages?: Array<AppJSON.ISubpackageItem>;
        subpackages?: Array<AppJSON.ISubpackageItem>;
        preloadRule?: {
            [key: string]: {
                network?: 'all' | 'wifi';
                packages: Array<string>;
            };
        };
        usingComponents?: {
            [key: string]: string;
        };
        componentPlaceholder?: {
            [key: string]: string;
        };
        tabBar?: ITabBar;
        appBar?: {};
        requiredBackgroundModes?: Array<string>;
        navigateToMiniProgramAppIdList?: Array<string>;
        mimeTypeDeclarations?: {
            [key: string]: Array<string>;
        };
        networkTimeout?: {
            request?: number;
            connectSocket?: number;
            uploadFile?: number;
            downloadFile?: number;
        };
        debug?: boolean;
        resizable?: boolean;
        functionalPages?: boolean | {
            independent: boolean;
        };
        cloud?: boolean;
        openDataContext?: string;
        openLocationPagePath?: string;
        sitemapLocation?: string;
        serviceProviderTicket?: string;
        style?: 'v2';
        useExtendedLib?: {
            [key: string]: boolean | string;
        };
        entranceDeclare?: {
            locationMessage?: {
                path?: string;
                query?: string;
            };
        };
        darkmode?: boolean;
        themeLocation?: string;
        theme?: string;
        lazyCodeLoading?: 'requiredComponents';
        renderer?: 'skyline' | 'webview';
        resolveAlias?: {
            [key: string]: string;
        };
        requiredPrivateInfos?: Array<requiredPrivateInfosValues>;
        accountCardPackage?: {
            root: string;
            cardList: Array<{
                type: number;
                componentPath: string;
            }>;
        };
    }
    interface IPluginConfig {
        provider: string;
        version: string;
        path?: string;
    }
    interface ISubpackageItem {
        name?: string;
        root: string;
        pages: string[];
        plugins: {
            [alias: string]: IPluginConfig;
        };
        independent: boolean;
        useExtendedLib?: {
            [key: string]: boolean | string;
        };
    }
    type requiredPrivateInfosValues = 'getFuzzyLocation' | 'getLocation' | 'onLocationChange' | 'startLocationUpdate' | 'startLocationUpdateBackground' | 'chooseLocation' | 'choosePoi' | 'chooseAddress';
    interface IWindowConfig {
        navigationBarBackgroundColor?: string;
        navigationBarTextStyle?: BaseInfo.NavigationBarTextStyle;
        navigationBarTitleText?: string;
        navigationStyle?: BaseInfo.NavigationStyle;
        backgroundColor?: string;
        backgroundTextStyle?: BaseInfo.BackgroundTextStyle;
        backgroundColorTop?: string;
        backgroundColorBottom?: string;
        enablePullDownRefresh?: boolean;
        onReachBottomDistance?: number;
        disableScroll?: boolean;
        pageOrientation?: BaseInfo.PageOrientation;
        enableFullScreen?: boolean;
    }
    type TabbarBorderStyle = 'black' | 'white';
    type TabbarPosition = 'bottom' | 'top';
    interface ITabbarConfig {
        custom?: boolean;
        list: ITabbarItem[];
        color: string;
        selectedColor: string;
        backgroundColor: string;
        borderStyle?: TabbarBorderStyle;
        position?: TabbarPosition;
    }
    interface ITabbarItem {
        pagePath: string;
        text?: string;
        iconPath?: string;
        selectedIconPath?: string;
        badge?: {};
    }
    interface INetworkTimeoutConfig {
        request: number;
        uploadFile: number;
        connectSocket: number;
        downloadFile: number;
    }
    interface IPreloadRuleConfig {
        network: 'all' | 'wifi';
        packages: string[];
    }
    interface IAppConfig {
        pages: string[];
        entryPagePath?: string;
        workers?: string | {
            path: string;
            isSubpackage: boolean;
        };
        resizable?: boolean;
        subpackages?: Array<ISubpackageItem>;
        subPackages?: Array<ISubpackageItem>;
        debug?: boolean;
        customClose?: boolean;
        tabBar?: ITabbarConfig;
        navigateToMiniProgramAppIdList?: string[];
        widgets?: any[];
        cloud?: boolean;
        global: {
            window: IWindowConfig;
        };
        page: {
            [pagePath: string]: {
                window: IWindowConfig;
            };
        };
        networkTimeout?: INetworkTimeoutConfig;
        ext?: any;
        extAppid?: string;
        plugins: {
            [alias: string]: IPluginConfig;
        };
        mainPlugins: {
            [alias: string]: IPluginConfig;
        };
        preloadRule: {
            [pagePath: string]: IPreloadRuleConfig;
        };
        permission?: any;
        showStatusBar: boolean;
        loadingImageInfo: {
            path: string;
        };
        requiredBackgroundModes: string[];
        functionalPages?: boolean | {
            independent: boolean;
        };
        style?: 'v2';
        __warning__?: string;
    }
}

/// <reference types="node" />
import type { Analyzer } from '../vendor/code-analyse';
export * from './miniprogram-json/index';
export declare namespace MiniProgramCI {
    interface IStat {
        isFile: boolean;
        isDirectory: boolean;
        size?: number;
    }
    type ProjectType = 'miniProgram' | 'miniGame' | 'miniProgramPlugin' | 'miniGamePlugin';
    interface IProject {
        nameMappingFromDevtools?: IStringKeyMap<string>;
        appid: string;
        type: ProjectType;
        projectPath: string;
        privateKey: string;
        miniprogramRoot: string;
        pluginRoot: string;
        attr(): Promise<IProjectAttr>;
        stat(prefix: string, filePath: string): IStat | undefined;
        getFile(prefix: string, filePath: string): Buffer;
        getFileList(prefix: string, extName: string): string[];
        getExtAppid(): Promise<string | void>;
        updateFiles(): void;
        getFilesAndDirs(): {
            files: string[];
            dirs: string[];
        };
    }
    interface IStringKeyMap<T = any> {
        [propName: string]: T;
    }
    interface IValidateResult {
        warning: string;
        error: Array<{
            errorType: string;
            errorProperty: string;
            correctType?: Array<string>;
            requireProperty?: string;
        }>;
    }
    type FN<R = any> = (...args: any[]) => R;
    type FN1<A = any, R = any> = (a: A) => R;
    type FN2<A = any, B = any, R = any> = (a: A, b: B) => R;
    type FN3<A = any, B = any, C = any, R = any> = (a: A, b: B, c: C) => R;
    type FN4<A = any, B = any, C = any, D = any, R = any> = (a: A, b: B, c: C, d: D) => R;
    interface IAnyObject {
        [key: string]: any;
    }
    interface ICompileSettings {
        es6?: boolean;
        es7?: boolean;
        minify?: boolean;
        codeProtect?: boolean;
        minifyJS?: boolean;
        minifyWXML?: boolean;
        minifyWXSS?: boolean;
        autoPrefixWXSS?: boolean;
        disableUseStrict?: boolean;
        compileWorklet?: boolean;
    }
    interface ITaskStatus {
        id: string;
        message: string;
        status: 'doing' | 'done';
    }
    interface IDevToolsCompileCache {
        init: (project: IProject) => Promise<void>;
        get: (key: string) => any;
        set: (key: string, value: any) => void;
        remove: (key?: string | undefined) => void;
        getFile: (filePath: string, infoKey?: string) => Promise<any>;
        setFile: (filePath: string, value: any, infoKey?: string) => void;
        removeFile: (filePath: string) => void;
        clean: () => void;
        getAllCacheFiles: () => string[];
    }
    interface IDevtoolsDebugInfo {
        from: 'devtools' | 'ci';
        useNewCompileModule: boolean;
        devtoolsVersion: string;
        compileSetting: MiniProgramCI.IAnyObject;
        ciVersion: string;
    }
    interface ICompileOptions {
        nameMapping?: IStringKeyMap<string>;
        setting?: ICompileSettings;
        onProgressUpdate?: (task: ITaskStatus) => void;
        devToolsCompileCache?: IDevToolsCompileCache;
        __compileDebugInfo__?: IDevtoolsDebugInfo;
        compilePages?: string[];
        analyzer?: Analyzer;
    }
    interface IProjectAttr {
        platform: boolean;
        appType: number;
        gameApp?: boolean;
        isSandbox: boolean;
        released: boolean;
        setting: {
            MaxCodeSize: number;
            MaxSubpackageSubCodeSize: number;
            MaxSubpackageFullCodeSize: number;
            NavigateMiniprogramLimit: number;
            MaxSubPackageLimit: number;
            MinTabbarCount: number;
            MaxTabbarCount: number;
            MaxCustomTabbarCount: number;
            MaxTabbarIconSize: number;
        };
    }
    interface IWarnItem {
        jsPath: string;
        code: string;
        tips: string;
        msg: string;
        startLine?: number;
        endLine?: number;
    }
}
export type ProjectType = MiniProgramCI.ProjectType;
export type IProject = MiniProgramCI.IProject;
export type IStat = MiniProgramCI.IStat;
export type IAnyObject = MiniProgramCI.IAnyObject;
export declare const CheckOption: {
    PACKAGE_SIZE_LIMIT: number;
    IMAGE_AND_AUDIO_LIMIT: number;
    CONTAINS_OTHER_PKG_JS: number;
    CONTAINS_OTHER_PKG_COMPONENTS: number;
    CONTAINS_OTHER_PKG_PLUGINS: number;
    JS_COMPRESS_OPEN: number;
    WXML_COMPRESS_OPEN: number;
    WXSS_COMPRESS_OPEN: number;
    CONTAINS_UNUSED_PLUGINS: number;
    PLUGIN_OVER_SIZE: number;
    USE_EXTENDLIB_WITHOUT_DEFINED: number;
    LAZYCODE_LOADING_OPEN: number;
    CONTAINS_UNUSED_COMPONENTS: number;
    CONTAINS_UNUSED_CODES: number;
    CONTAINS_APPSECRET: number;
};
export type ICheckOption = keyof typeof CheckOption;
export interface ICheckItemList {
    version?: number;
    checkList?: ICheckItem[];
    regList?: {
        [optionName: string]: any;
    };
    innerPluginAppidList?: string[];
}
export interface ICheckItem {
    name?: ICheckOption;
    enabled: boolean;
    desc: string;
    descEn: string;
    docURL?: string;
    level: number;
    handlerText?: string;
    handlerTextEn?: string;
    text?: string;
    limit?: number;
    detail?: any;
    title: string;
    titleEn: string;
    typeName: string;
    typeNameEn: string;
    success?: boolean;
}
export type ICheckResultItem = Pick<ICheckItem, 'name' | 'success' | 'text' | 'detail' | 'docURL'>;

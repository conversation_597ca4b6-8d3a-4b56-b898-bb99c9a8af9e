export declare const CI_VERSION = "1.9.26";
export declare const PARAM_ERROR = 10000;
export declare const WXML_NOT_FOUND = 10007;
export declare const JS_NOT_FOUND = 10008;
export declare const BABEL_TRANS_JS_ERR = 10032;
export declare const UGLIFY_JS_ERR = 10033;
export declare const BABILI_JS_ERR = 10034;
export declare const JS_ES6_ERR = 10035;
export declare const FILE_FLAT_ERR = 10036;
export declare const POST_WXSS_ERR = 10037;
export declare const MINIFY_WXML_ERR = 10038;
export declare const SUMMER_PLUGIN_ERR = 10045;
export declare const SUMMER_PLUGIN_CODE_ERR = 10046;
export declare const GAME_PLUGIN_LIB_MD5_NOT_MATCH = 10081;
export declare const PLUGIN_JSON_FILE_NOT_FOUND = 10091;
export declare const PLUGIN_JSON_CONTENT_ERR = 10092;
export declare const PLUGIN_JSON_PARSE_ERR = 10093;
export declare const FILE_NOT_FOUND = 10005;
export declare const JSON_PARSE_ERR = 10006;
export declare const FILE_NOT_UTF8 = 10031;
export declare const JSON_CONTENT_ERR = 10009;
export declare const APP_JSON_NOT_FOUND = 20000;
export declare const GET_SIGNATURE_RAND_STRING_ERR = 20001;
export declare const GENERATE_LOCAL_SIGNATURE_ERR = 20002;
export declare const UPLOAD_CGI_ERR = 20003;
export declare const CODE_PROTECT_TRANSLATE_FILENAME = 20004;
export declare const UPLOAD_JS_SERVER_CGI_ERR = 20005;
export declare const GET_LATEST_VERSION_CGI_ERR = 20006;
export declare const PROJECT_TYPE_ERROR = 30000;
export declare const MINI_PROGRAM_MAIN_PACKAGE_ROOT = "__APP__";
export declare const MINI_GAME_MAIN_PACKAGE_ROOT = "__GAME__";
export declare const MINI_GAME_WORKERS_PACKAGE_ROOT = "workers.js";
export declare const APP_TYPE: {
    NORMAL: number;
    PLUGIN: number;
    SHOP: number;
    MINISHOP: number;
    GAME: number;
    CARD: number;
    NATIVE: number;
};
export declare enum COMPILE_TYPE {
    miniProgram = "miniProgram",
    miniProgramPlugin = "miniProgramPlugin",
    miniGame = "miniGame",
    miniGamePlugin = "miniGamePlugin"
}
export declare const TABBAR_ICON_WHITE_LIST: string[];
export declare const DefaultProjectAttr: {
    platform: boolean;
    appType: number;
    isSandbox: boolean;
    released: boolean;
    setting: {
        MaxCodeSize: number;
        MaxSubpackageSubCodeSize: number;
        MaxSubpackageFullCodeSize: number;
        NavigateMiniprogramLimit: number;
        MaxSubPackageLimit: number;
        MinTabbarCount: number;
        MaxTabbarCount: number;
        MaxCustomTabbarCount: number;
        MaxTabbarIconSize: number;
    };
};
export declare const jsonVariablePropertyWhiteList: {
    windowPropertWhiteList: string[];
    tabBarPropertyWhiteList: string[];
    tabbarListItemPropertyWhiteList: string[];
};
export declare const extendedLibMap: {
    kbone: {
        packages: string[];
    };
    weui: {
        packages: string[];
    };
};

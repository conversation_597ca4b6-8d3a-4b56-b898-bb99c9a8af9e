import { MiniProgramCI } from "../types";
interface ICreateTimeTriggerOptions {
    project: MiniProgramCI.IProject;
    envId: string;
    functionName: string;
    triggersConfig: {
        name: string;
        type: string;
        config: string;
    }[];
}
export declare function createTimeTrigger(options: ICreateTimeTriggerOptions): Promise<"创建定时触发器成功" | "请检查传入的 triggersConfig 是否符合格式">;
export {};

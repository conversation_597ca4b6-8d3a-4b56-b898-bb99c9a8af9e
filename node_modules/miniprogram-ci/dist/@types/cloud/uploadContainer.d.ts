import { MiniProgramCI } from '../types';
interface VersionOptions {
    region?: string;
    uploadType: string;
    flowRatio: number;
    cpu: number;
    mem: number;
    minNum: number;
    maxNum: number;
    policyType: string;
    policyThreshold: number;
    containerPort: number;
    serverName: string;
    repositoryType?: string;
    dockerfilePath?: string;
    buildDir?: string;
    envParams?: string;
    repository?: string;
    branch?: string;
    versionRemark?: string;
    packageName?: string;
    packageVersion?: string;
    imageInfo?: {
        repositoryName: string;
        isPublic: boolean;
        tagName: string;
        serverAddr: string;
        imageUrl: string;
    };
    codeDetail?: {
        name?: {
            name?: string | null;
            fullName?: string | null;
        };
        url?: string;
    };
    imageSecretInfo?: {
        registryServer?: string;
        userName?: string;
        password?: string;
        email?: string;
    };
    imagePullSecret?: string;
    customLogs?: string;
    initialDelaySeconds?: number;
    mountVolumeInfo?: {
        name?: string;
        mountPath?: string;
        readOnly?: boolean;
        nfsVolumes?: {
            server?: string;
            path?: string;
            readOnly?: boolean;
        }[];
    }[];
    addIntranetDns?: boolean;
    mountWxToken?: boolean;
    useHttpRoute?: boolean;
}
interface IAPITCBCreateCloudBaseRunServerVersionResult {
    result: string | null;
    versionName: string | null;
}
interface IContainerOptions {
    project: MiniProgramCI.IProject;
    env: string;
    version: VersionOptions;
    containerRoot: string;
}
export declare function uploadContainer(options: IContainerOptions): Promise<IAPITCBCreateCloudBaseRunServerVersionResult>;
export {};

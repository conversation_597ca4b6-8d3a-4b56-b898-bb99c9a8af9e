/// <reference types="node" />
import JSZip from 'jszip';
import { MiniProgramCI } from '@/types';
import * as cloudAPI from '../vendor/cloud-api';
interface IZipFileOptions {
    ignore?: string[];
    name?: string;
    zip?: JSZip;
    includeRoot?: boolean;
}
export declare const zipFile: (filePath: string, { ignore, name, zip, includeRoot }?: IZipFileOptions) => JSZip;
export declare const zipToBuffer: (zip: JSZip, onUpdate?: any) => Promise<Buffer>;
export declare function initEnvironmentByProject(project: MiniProgramCI.IProject, env: string): Promise<{
    currentEnv: IAPIEnvironment;
    cloudAPI: typeof cloudAPI;
}>;
export {};

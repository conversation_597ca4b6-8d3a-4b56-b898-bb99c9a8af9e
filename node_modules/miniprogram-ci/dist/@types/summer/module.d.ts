import { FileType, GenerateDescription, ModuleJSON, SourceDescription } from './types';
import { CodeError, CodeFile } from './devtool';
import { BaseGraph } from './graph/basegraph';
import { SummerError } from './error';
declare class JsTag {
    isLargeFile: boolean;
    isBabelIgnore: boolean;
    helpers: string[];
    constructor();
    setBabelIgnore(): void;
    setLargeFile(): void;
    addHelpers(helpers: string[]): void;
    toJSON(): {
        isLargeFile: boolean;
        isBabelIgnore: boolean;
        helpers: string[];
    };
}
export default class Module {
    private readonly graph;
    readonly path: string;
    readonly sourcePath: string;
    fileType: FileType;
    md5: string;
    json: any;
    error?: SummerError | Error;
    jsTag?: JsTag;
    source?: SourceDescription;
    target?: GenerateDescription;
    depFiles: string[];
    independentRoot: string;
    loadingPromise: Promise<Boolean> | undefined;
    loadStart: number;
    loadEnd: number;
    constructor(graph: BaseGraph, path: string, sourcePath: string, fileType: FileType);
    setError(error: SummerError | Error): void;
    setSource(source: SourceDescription): void;
    toCodeFile(): CodeFile | CodeError;
    toJSON(): ModuleJSON;
    addWatchFile(file: string): void;
}
export {};

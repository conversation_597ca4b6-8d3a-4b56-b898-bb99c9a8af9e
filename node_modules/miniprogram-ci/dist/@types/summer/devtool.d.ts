/// <reference types="node" />
import { IBasicCodeExt, SourceMap, SummerPluginOptions } from './types';
import { MiniProgramCI } from '../types';
interface DevtoolMessageHub {
    showStatus: (id: number, message: string) => void;
    hideStatus: (id: number) => void;
}
export type ProgressUpdate = (id: number, status: 'doing' | 'done', message: string) => void;
interface IProject {
    id: string;
    appid: string;
    projectid: string;
    projectname: string;
    miniprogramRoot: string;
    pluginRoot?: string;
}
interface EventMsg {
    type: 'event';
    name: string;
    data: any;
}
interface ProgressMsg {
    type: 'progress';
    id: number;
    taskId: number;
    status: 'doing' | 'done';
    message: string;
}
interface RequestMsg {
    type: 'request';
    id: number;
    name: string;
    data: any;
}
interface ResponseMsg {
    type: 'response';
    id: number;
    data: any;
    error?: any;
}
interface InitMsg {
    type: 'init';
    data: any;
}
export type ProcessMessage = InitMsg | RequestMsg | ResponseMsg | EventMsg | ProgressMsg;
export type IPackageCodeOptions = {
    package: string;
    partialCompilePath?: string[];
};
declare const miniprogram = "miniprogram";
declare const plugin = "plugin";
export type GraphId = typeof miniprogram | typeof plugin;
export type IGetConfOptions = {
    graphId: GraphId;
};
export type ICompileSingleCodeOptions = {
    graphId: GraphId;
    filePath: string;
    sourceCode?: string;
};
export declare const MainPkg = "__APP__";
export declare const FullPkg = "__FULL__";
export type IGetCodeOptions = {
    graphId: GraphId;
    cacheMd5: Record<string, string>;
    package?: string;
};
export interface CodeFile {
    path: string;
    md5: string;
    code: string;
    map?: SourceMap;
    jsTag?: {
        isLargeFile: boolean;
        isBabelIgnore: boolean;
        helpers: string[];
    };
}
export interface CodeError {
    path: string;
    error: any;
}
export type CodeFiles = Record<string, CodeFile | CodeError>;
export type LocalFiles = Record<string, {
    size: number;
}>;
export interface ICompilerStatus {
    codeExts: string[];
    codeConf: {
        [key in IBasicCodeExt]: {
            exts: string[];
            template?: {
                ext: string;
                content: string;
            };
        };
    };
}
export type ICompileResult = Record<string, string | Buffer>;
export interface IAppConf {
    app: any;
    packages: Record<string, any>;
    pages: Record<string, any>;
    comps: Record<string, any>;
    sitemap: any;
    theme: any;
}
export interface IPluginConf {
    plugin: any;
    pages: Record<string, any>;
    comps: Record<string, any>;
}
type ISummerPluginConfig = Array<string | [string, SummerPluginOptions]>;
export type IBabelSetting = {
    outputPath?: string;
    ignore?: any[];
};
export interface ICompilerOptions {
    appid: string;
    attr: any;
    compileType: MiniProgramCI.ProjectType;
    miniprogramRoot: string;
    pluginRoot: string;
    summerPlugins: ISummerPluginConfig;
    babelSetting?: IBabelSetting;
}
export declare class SummerCompiler {
    projectPath: string;
    private cachePath;
    private options;
    private devtoolMessagehub;
    readonly isSummer = true;
    private process;
    private codeCache;
    private promiseCache;
    private messageHub;
    status: ICompilerStatus | undefined;
    constructor(projectPath: string, cachePath: string, options: ICompilerOptions, devtoolMessagehub: DevtoolMessageHub);
    init(files: string[], dirs: string[], proxyProject: any): Promise<void>;
    loadStatus(): Promise<void>;
    destroy(): void;
    clearCache(): Promise<void>;
    updateOptions(options: ICompilerOptions): void;
    fileChange(type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', targetPath: string): void;
    private invalidCodeCache;
    private onProgressUpdate;
    private getConf;
    private getCode;
    ready(): Promise<true>;
    getAppJSON(project: IProject, mergeExtJSON: boolean): Promise<any>;
    getPageJSON(project: IProject, pagePath: string): Promise<any>;
    getAllPageAndComponentJSON(): Promise<string[]>;
    getAllSortedJSFiles(): Promise<{
        jsPagesFiles: string[];
        components: string[];
        otherJsFiles: string[];
    }>;
    getWxmlAndWxsFiles(packagePath: string): Promise<{
        wxmlFiles: string[];
        wxsFiles: string[];
        content: Record<string, string>;
    }>;
    getWxssFiles(packagePath: string): Promise<{
        wxssFiles: string[];
        content: Record<string, string>;
    }>;
    getWxssMap(graphId: GraphId, wxssPath: string): SourceMap | undefined;
    getMainPkgSortedJSFiles(): Promise<{
        hasAppJS: boolean;
        allFiles: string[];
        pageFiles: string[];
        functionalPageFiles: string[];
        workerFiles: string[];
        componentFiles: string[];
        otherFiles: string[];
    }>;
    getSubPkgSortedJSFiles(rootPath: string): Promise<{
        allFiles: string[];
        pageFiles: string[];
        componentFiles: string[];
        otherFiles: string[];
    }>;
    compileJS(project: IProject, options: {
        root: string;
        filePath: string;
        babelRoot: string;
        sourceCode?: string;
        setting: {
            es6: boolean;
            es7: boolean;
            minify: boolean;
            disableUseStrict: boolean;
        };
    }): Promise<{
        isLargeFile?: boolean | undefined;
        isBabelIgnore?: boolean | undefined;
        helpers?: string[] | undefined;
        filePath: string;
        code: string;
        map: SourceMap | undefined;
    }>;
    compile(project: IProject, options: {
        nameMapping?: Record<string, any>;
        onProgressUpdate: (status: any) => void;
        devToolsCompileCache?: {
            init: (project: IProject) => Promise<void>;
            get: (key: string) => any;
            set: (key: string, value: any) => void;
            remove: (key?: string | undefined) => void;
            getFile: (filePath: string, infoKey?: string) => Promise<any>;
            setFile: (filePath: string, value: any, infoKey?: string) => void;
            removeFile: (filePath: string) => void;
            clean: () => void;
            getAllCacheFiles: () => string[];
        };
        __compileDebugInfo__?: {
            from: 'devtools' | 'ci';
            useNewCompileModule: boolean;
            devtoolsVersion: string;
            compileSetting: any;
        };
        compilePages?: string[];
        analyzer?: any;
    }): Promise<ICompileResult>;
    getPluginJSON(project: IProject, localPath?: string): Promise<any>;
    getPluginPageJSON(project: IProject, pagePath: string): Promise<any>;
    getPluginJSFiles(): Promise<string[]>;
    getPluginComponents(): Promise<string[]>;
    getPluginWxssFiles(): Promise<{
        wxssFiles: string[];
        content: Record<string, string>;
    }>;
    getPluginWxmlAndWxsFiles(): Promise<{
        wxmlFiles: string[];
        wxsFiles: string[];
        content: Record<string, string>;
    }>;
    checkThemeJSON(project: IProject, options: {
        themeLocation: string;
        isPlugin?: boolean;
    }): Promise<any>;
    setProxy(proxyUrl: string): void;
    setLocale(locale: string): void;
    uglifyFileNames(project: IProject, result: Record<string, string | Buffer>, nameMapping: Record<string, string>): Promise<MiniProgramCI.IAnyObject>;
    getLocalFileList(): Promise<LocalFiles>;
    getPluginLocalFileList(): Promise<LocalFiles>;
    packNpm(project: IProject): Promise<void>;
    packNpmManually(packTaskList: Array<{
        packageJsonPath: string;
        miniprogramNpmDistDir: string;
    }>): Promise<void>;
    getGameJSON(project: IProject): Promise<void>;
}
export {};

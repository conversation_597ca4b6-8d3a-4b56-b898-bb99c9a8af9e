import { AsyncPluginHooks, FirstPluginHooks, ParallelPluginHooks, PluginContext, PluginHooks, SequentialPluginHooks, SummerPlugin } from './types';
import { BaseGraph } from './graph/basegraph';
interface IResolveExt {
    json?: string;
    wxml?: string;
    wxss?: string;
    js?: string;
    wxs?: string;
}
type ResolveValue<T> = T extends Promise<infer K> ? K : T;
type EnsurePromise<T> = Promise<ResolveValue<T>>;
type Arg0<H extends Exclude<keyof PluginHooks, 'resolveExt'>> = Parameters<PluginHooks[H]>[0];
export type ReplaceContext = (context: PluginContext, plugin: SummerPlugin) => PluginContext;
export interface ICompilerPlugin extends PluginHooks {
    resolveExt?: IResolveExt;
}
export declare function genResovleExtConf(plugins: SummerPlugin[]): {
    json: string[];
    wxml: string[];
    wxss: string[];
    js: string[];
    wxs: string[];
};
export declare function getPluginContext(plugin: SummerPlugin, graph: BaseGraph): PluginContext;
export declare class PluginDriver {
    private graph;
    private options;
    resolveExtConf: {
        json: string[];
        wxml: string[];
        wxss: string[];
        js: string[];
        wxs: string[];
    };
    plugins: SummerPlugin[];
    private pluginContexts;
    constructor(graph: BaseGraph, options: {
        plugins: SummerPlugin[];
    });
    private runHook;
    hookFirst<H extends AsyncPluginHooks & FirstPluginHooks>(hookName: H, args: Parameters<PluginHooks[H]>, replaceContext?: ReplaceContext | null): EnsurePromise<ReturnType<PluginHooks[H]>>;
    hookParallel<H extends AsyncPluginHooks & ParallelPluginHooks>(hookName: H, args: Parameters<PluginHooks[H]>, replaceContext?: ReplaceContext): Promise<void>;
    hookReduceArg0<H extends AsyncPluginHooks & SequentialPluginHooks>(hookName: H, [arg0, ...rest]: Parameters<PluginHooks[H]>, reduce: (reduction: Arg0<H>, result: ResolveValue<ReturnType<PluginHooks[H]>>, plugin: SummerPlugin) => Arg0<H>, replaceContext?: ReplaceContext | null): Promise<Arg0<H>>;
}
export {};

import { IStat } from '@/types';
import { BaseGraph } from './graph/basegraph';
declare const targetCodeExts: readonly ["json", "wxml", "wxss", "js", "wxs"];
type TargetCodeExt = typeof targetCodeExts[number];
type ResolveExtConf = Record<TargetCodeExt, string[]>;
export interface ResolveFileInfo {
    path: string;
    source: string;
    sourceExt: string;
}
export declare class Resolver {
    graph: BaseGraph;
    root: string;
    extConf: ResolveExtConf;
    fileSet: Set<string>;
    resolveInfoMap: Map<string, ResolveFileInfo>;
    private extToTarget;
    allExts: string[];
    constructor(graph: BaseGraph, root: string, extConf: ResolveExtConf);
    private updateFiles;
    onFileChange: (type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', path: string) => void;
    resolve(source: string): ResolveFileInfo[];
    stat(path: string): IStat | undefined;
    private isCodeFile;
    private getExt;
    private updateFile;
}
export {};

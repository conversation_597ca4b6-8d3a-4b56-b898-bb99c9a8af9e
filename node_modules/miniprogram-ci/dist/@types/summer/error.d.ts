export declare class SummerError extends Error {
    code?: number | string;
    path?: string;
    plugin?: string;
    hook?: string;
    constructor(error: any);
    toString(): string;
    toJSON(): {
        type: string;
        message: string;
        stack: string | undefined;
        code: string | number | undefined;
        path: string | undefined;
        plugin: string | undefined;
        hook: string | undefined;
    };
}
export declare const SummerErrors: {
    readonly SUMMER_PLUGIN_ERR: 10045;
    readonly SUMMER_PLUGIN_CODE_ERR: 10046;
};
export declare function makeSummerError(error: string | Error, code?: string | number, path?: string): SummerError;

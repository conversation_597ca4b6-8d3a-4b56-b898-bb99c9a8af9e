import { SummerCompiler } from '../summer';
import { IDevtoolProject } from '../types';
import { AppGraph } from './appgraph';
import { Recorder } from '../recorder';
export declare function resolvePath(currentPath: string, targetPath: string): string;
export declare class AppConf {
    graph: AppGraph;
    app: any;
    packages: Map<string, any>;
    pages: Map<string, any>;
    comps: Map<string, any>;
    sitemap: any;
    theme: any;
    proxyProject: IDevtoolProject;
    constructor(compiler: SummerCompiler, graph: AppGraph);
    destroy(): void;
    build(recorder: Recorder): Promise<void>;
    onFileChange: (type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', path: string) => void;
    private resetState;
    private loadApp;
    private loadPage;
    private loadComp;
    private loadTheme;
    private isExtendedLibComp;
}

import { CodeFile, CodeFiles, IPluginConf } from '../devtool';
import { Recorder } from '../recorder';
import { BaseGraph, IGraphOptions } from './basegraph';
export declare class PluginGraph extends BaseGraph {
    private pluginConf;
    private conf;
    constructor(options: IGraphOptions);
    destroy(): void;
    getConf(recorder: Recorder): Promise<IPluginConf>;
    ensureConf(recorder: Recorder): Promise<void>;
    compileSingleCode(filePath: string, sourceCode?: string): Promise<CodeFile>;
    getDevCode(recorder: Recorder): Promise<CodeFiles>;
    getProdCode(recorder: Recorder): Promise<CodeFiles>;
    protected getLocalCodeFileList(): string[];
    protected onFileChangeForGraph(type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', path: string): void;
    private getPackageFile;
    private getIndependentRoot;
    protected compileJSON(): Promise<{
        conf: IPluginConf;
        jsons: Record<string, string>;
    }>;
}

import { CodeFile, CodeFiles, IAppConf, IPackageCodeOptions } from '../devtool';
import { Recorder } from '../recorder';
import { BaseGraph, IGraphOptions } from './basegraph';
export declare class AppGraph extends BaseGraph {
    private appConf;
    private conf;
    constructor(options: IGraphOptions);
    destroy(): void;
    getConf(recorder: Recorder): Promise<IAppConf>;
    ensureConf(recorder: Recorder): Promise<void>;
    compileSingleCode(filePath: string, sourceCode?: string): Promise<CodeFile>;
    getDevCode(recorder: Recorder, options: IPackageCodeOptions): Promise<CodeFiles>;
    getProdCode(recorder: Recorder, options: IPackageCodeOptions): Promise<CodeFiles>;
    protected getLocalCodeFileList(): string[];
    protected onFileChangeForGraph(type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', path: string): void;
    private getPackageFile;
    private getIndependentRoot;
    private checkFilePackage;
    protected compileJSON(): Promise<{
        conf: IAppConf;
        jsons: {
            [x: string]: string;
        };
    }>;
}

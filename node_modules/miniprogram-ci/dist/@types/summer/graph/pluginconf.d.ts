import { SummerCompiler } from '../summer';
import { IDevtoolProject } from '../types';
import { PluginGraph } from './plugingraph';
import { Recorder } from '../recorder';
export declare function resolvePath(currentPath: string, targetPath: string): string;
export declare class PluginConf {
    graph: PluginGraph;
    plugin: any;
    pages: Map<string, any>;
    comps: Map<string, any>;
    proxyProject: IDevtoolProject;
    constructor(compiler: SummerCompiler, graph: PluginGraph);
    destroy(): void;
    build(recorder: Recorder): Promise<void>;
    private resetState;
    private loadPlugin;
    private loadPage;
    private loadComp;
}

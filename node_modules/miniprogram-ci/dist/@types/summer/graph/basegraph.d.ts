import { PluginDriver } from '../plugin_driver';
import { ModuleJSON, SummerPlugin } from '../types';
import Module from '../module';
import { SummerCompiler } from '../summer';
import { CodeFile, CodeFiles, IAppConf, ICompileResult, IPackageCodeOptions, IPluginConf } from '../devtool';
import { ResolveFileInfo, Resolver } from '../resolver';
import { Project } from '../project';
import { Recorder } from '../recorder';
import PersistCache from '../persist_cache';
type GraphType = 'miniprogram' | 'plugin' | 'other';
export type FileInfo = ResolveFileInfo & {
    independentRoot: string;
    isBabelIgnore: boolean;
};
export interface IGraphOptions {
    type: GraphType;
    root: string;
    persistCache: PersistCache;
    plugins: SummerPlugin[];
    compiler: SummerCompiler;
}
export declare abstract class BaseGraph {
    pluginDriver: PluginDriver;
    modulesByPath: Map<string, Module>;
    root: string;
    rootPath: string;
    persistCache: PersistCache;
    type: GraphType;
    compiler: SummerCompiler;
    project: Project;
    cachedModules: Map<string, ModuleJSON>;
    resolver: Resolver;
    private invalidated;
    private running;
    constructor(options: IGraphOptions);
    destroy(): void;
    clearCache(): void;
    private onFileChange;
    private invalidateModules;
    private loadModuleFromFile;
    private loadSourceForModule;
    protected doCompileSingleCode(info: FileInfo, sourceCode?: string): Promise<CodeFile>;
    protected getCodeFiles(files: FileInfo[], recorder: Recorder, useCache?: boolean): Promise<CodeFiles>;
    private readFile;
    private tranform;
    private generate;
    compile(recorder: Recorder): Promise<ICompileResult>;
    getLocalFileList(): Promise<Record<string, {
        size: number;
    }>>;
    private compileOther;
    private compileCodeWithoutJSON;
    protected isBabelSettingIgnore(fileInfo: ResolveFileInfo): boolean;
    protected abstract onFileChangeForGraph(type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', path: string): void;
    protected abstract getConf(recorder: Recorder): Promise<IAppConf | IPluginConf>;
    protected abstract getDevCode(recorder: Recorder, options?: IPackageCodeOptions): Promise<CodeFiles>;
    protected abstract getProdCode(recorder: Recorder, options?: IPackageCodeOptions): Promise<CodeFiles>;
    protected abstract compileJSON(recorder: Recorder): Promise<{
        conf: any;
        jsons: Record<string, string>;
    }>;
    protected abstract getLocalCodeFileList(): string[];
}
export {};

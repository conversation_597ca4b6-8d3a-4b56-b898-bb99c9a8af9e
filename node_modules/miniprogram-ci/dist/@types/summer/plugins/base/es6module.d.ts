import { SourceDescription, SummerPlugin } from '../../types';
export declare function getBabelRoot(independentRoot: string): string;
export declare function transformES6ModuleAndGenCode(source: SourceDescription, id: string, babelRoot: string, disableUseStrict: boolean): {
    code: string;
    map: {
        version: number;
        sources: string[];
        names: string[];
        sourceRoot?: string | undefined;
        sourcesContent?: string[] | undefined;
        mappings: string;
        file: string;
    };
    helpers: string[];
};
export default function (projectPath: string, options: {
    disableUseStrict: boolean;
}): SummerPlugin;

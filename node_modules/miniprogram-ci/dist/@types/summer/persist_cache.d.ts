import { FileInfo } from './graph/basegraph';
import { CodeFile } from './devtool';
export default class LogicPersistCache {
    private baseCacheKey;
    private persistCache;
    constructor(cachePath: string, baseCacheKey: string);
    updateBaseCacheKey(key: string): void;
    private getCacheKey;
    get(projectPath: string, root: string, file: FileInfo): Promise<CodeFile | undefined>;
    set(projectPath: string, root: string, file: FileInfo, data: CodeFile): Promise<void>;
    clean(): Promise<void>;
}

/// <reference types="node" />
import { IDevtoolProject, TypedEventEmitter } from './types';
import { ICompilerOptions } from './devtool';
import { MiniProgramCI } from '../types';
export interface IStat {
    isFile: boolean;
    isDirectory: boolean;
    size?: number;
}
export type FileChange = (type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', targetPath: string) => void;
type ProjectEventEmitter = TypedEventEmitter<{
    fileChange: FileChange;
}>;
type ProjectOptions = Omit<ICompilerOptions, 'summerPlugins'>;
export declare class Project implements IDevtoolProject {
    projectPath: string;
    appid: string;
    private _attr;
    private _dirSet;
    private _fileSet;
    event: ProjectEventEmitter;
    type: MiniProgramCI.ProjectType;
    privateKey: string;
    miniprogramRoot: string;
    pluginRoot: string;
    nameMappingFromDevtools?: MiniProgramCI.IStringKeyMap<string> | undefined;
    constructor(projectPath: string, files: string[], dirs: string[], options: ProjectOptions);
    getFilesAndDirs(): {
        files: string[];
        dirs: string[];
    };
    attr(): Promise<any>;
    updateOptions(options: ProjectOptions): void;
    private updateType;
    getExtAppid(): Promise<string | void>;
    updateFiles(): void;
    private isIgnore;
    private cacheDirName;
    getTargetPath(prefix: string, filePath: string): string;
    stat(prefix: string, filePath: string): IStat | undefined;
    getFileSize(prefix: string, filePath: string): number;
    getFile(prefix: string, filePath: string): Buffer;
    getJson<T = any>(prefix: string, filePath: string): T;
    getFileList(prefix?: string, extName?: string): string[];
    onFileChange(type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', targetPath: string): void;
}
export {};

/// <reference types="node" />
import { IProject } from '../types';
import type { RawSourceMap } from 'source-map';
import { IAppConf } from './devtool';
import * as BabelTypes from '@babel/types';
export type IBasicCodeExt = 'js' | 'wxml' | 'wxss' | 'json' | 'wxs';
export declare const BasicCodeExts: readonly ["js", "wxml", "wxss", "json", "wxs"];
export declare enum FileType {
    JSON = "json",
    WXML = "wxml",
    WXSS = "wxss",
    WXS = "wxs",
    JS = "js"
}
export declare enum JSONType {
    APP = "app",
    Page = "page",
    EXT = "ext",
    SITEMAP = "sitemap",
    THEME = "theme"
}
export interface ModuleJSON {
    code: string | null;
    map?: SourceMap;
    path: string;
    sourcePath?: string;
    depFileIds?: string[];
}
export interface SummerCache {
    modules: ModuleJSON[];
}
export interface SummerBuild {
    cache: SummerCache | undefined;
    output: {
        [key in string]: string;
    };
    watchFiles: string[];
}
export interface PluginContext {
    addWatchFile: (absFilePath: string) => void;
    error: (err: any) => never;
    runWorkerMethod: (method: string, ...args: any[]) => Promise<any>;
    rootPath?: string;
}
export type SourceMap = Omit<RawSourceMap, 'version'> & {
    version: number | string;
};
export declare enum AstType {
    Babel = "babel",
    Acorn = "acorn"
}
type AstInfo = {
    ast: AcornNode;
    type: AstType.Acorn;
} | {
    ast: BabelTypes.File;
    type: AstType.Babel;
};
export type SourceDescription = {
    sourceCode: string;
    inputMap?: SourceMap;
    astInfo?: AstInfo;
    target?: GenerateDescription;
    largeFile?: boolean;
};
export type GenerateDescription = {
    code: string;
    map?: SourceMap;
    helpers?: string[];
};
type LoadResult = SourceDescription | string | null | undefined;
export type AsyncPluginHooks = 'load' | 'transform' | 'optimize' | 'generate' | 'compress' | 'sealed';
export type FirstPluginHooks = 'load' | 'generate';
export type SequentialPluginHooks = 'transform' | 'optimize' | 'compress';
export type ParallelPluginHooks = 'sealed';
export type LoadHook = (this: PluginContext, targetPath: string, sourcePath: string, options: {
    independentRoot: string;
    isBabelIgnore: boolean;
}) => Promise<LoadResult> | LoadResult;
export type TransformHook = (this: PluginContext, source: SourceDescription, targetPath: string, sourcePath: string, options: {
    independentRoot: string;
    isBabelIgnore: boolean;
}) => Promise<SourceDescription> | SourceDescription;
type GenerateResult = GenerateDescription | string | null | undefined;
export type GenerateHook = (this: PluginContext, source: SourceDescription, targetPath: string, sourcePath: string, options: {
    independentRoot: string;
    isBabelIgnore: boolean;
}) => Promise<GenerateResult> | GenerateResult;
export type OptimizeHook = (this: PluginContext, codes: Record<string, string>, appConf: IAppConf) => Promise<Record<string, string>>;
export type CompressHook = (this: PluginContext, content: Record<string, string | Buffer>) => Promise<Record<string, string | Buffer>>;
export type SealedHook = (this: PluginContext, content: Record<string, string | Buffer>) => Promise<void>;
export interface PluginHooks {
    load: LoadHook;
    transform: TransformHook;
    generate: GenerateHook;
    optimize: OptimizeHook;
    compress: CompressHook;
    sealed: SealedHook;
}
interface ResolveExt {
    json?: string | string[];
    wxml?: string | string[];
    wxss?: string | string[];
    js?: string | string[];
    wxs?: string | string[];
}
export interface SummerPlugin extends Partial<PluginHooks> {
    name: string;
    supportWorker?: boolean;
    workerMethods?: Record<string, Function>;
    resolveExt?: ResolveExt;
}
export type SummerPluginOptions = Record<string, any>;
export interface TypedEventEmitter<T extends {
    [event: string]: (...args: any) => any;
}> {
    addListener<K extends keyof T>(event: K, listener: T[K]): this;
    emit<K extends keyof T>(event: K, ...args: Parameters<T[K]>): boolean;
    eventNames(): Array<keyof T>;
    getMaxListeners(): number;
    listenerCount(type: keyof T): number;
    listeners<K extends keyof T>(event: K): Array<T[K]>;
    off<K extends keyof T>(event: K, listener: T[K]): this;
    on<K extends keyof T>(event: K, listener: T[K]): this;
    once<K extends keyof T>(event: K, listener: T[K]): this;
    prependListener<K extends keyof T>(event: K, listener: T[K]): this;
    prependOnceListener<K extends keyof T>(event: K, listener: T[K]): this;
    rawListeners<K extends keyof T>(event: K): Array<T[K]>;
    removeAllListeners<K extends keyof T>(event?: K): this;
    removeListener<K extends keyof T>(event: K, listener: T[K]): this;
    setMaxListeners(n: number): this;
}
export type ChangeEvent = 'create' | 'update' | 'delete';
export type SummerWatcherEvent = {
    code: 'START';
} | {
    code: 'BUILD_END';
    duration: number;
    output: SummerBuild['output'];
} | {
    code: 'END';
};
export interface SummerWatcher extends TypedEventEmitter<{
    change: (id: string, change: {
        event: ChangeEvent;
    }) => void;
    close: () => void;
    event: (event: SummerWatcherEvent) => void;
    restart: () => void;
}> {
    close(): void;
}
export interface WatcherOptions {
    buildDelay?: number;
}
interface AcornNode {
    end: number;
    start: number;
    type: string;
}
export type IDevtoolProject = IProject & {
    onFileChange?: any;
};
export {};

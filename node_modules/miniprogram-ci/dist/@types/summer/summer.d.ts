import { CodeFile, CodeFiles, GraphId, IAppConf, ICompileResult, ICompilerOptions, ICompilerStatus, ICompileSingleCodeOptions, IGetCodeOptions, IGetConfOptions, IPluginConf, LocalFiles } from './devtool';
import { Project } from './project';
import { SummerPlugin } from './types';
import { PluginGraph } from './graph/plugingraph';
import { AppGraph } from './graph/appgraph';
import { Recorder } from './recorder';
import PersistCache from './persist_cache';
export declare class SummerCompiler {
    project: Project;
    private cachePath;
    private options;
    projectPath: string;
    appGraph: AppGraph;
    pluginGraph?: PluginGraph;
    plugins: SummerPlugin[];
    proxyProject: any;
    persistCache: PersistCache;
    constructor(project: Project, cachePath: string, options: ICompilerOptions);
    getBabelSetting(): import("./devtool").IBabelSetting | undefined;
    private initPlugins;
    private initAppGraph;
    private initPluginGraph;
    updateOptions(options: ICompilerOptions): void;
    destroy(): void;
    getStatus(): ICompilerStatus;
    clearCache(): void;
    getConf({ graphId }: IGetConfOptions, recorder: Recorder): Promise<IAppConf | IPluginConf>;
    getCode(options: IGetCodeOptions, recorder: Recorder): Promise<CodeFiles>;
    getLocalFileList(graphId: GraphId): Promise<LocalFiles>;
    compileSingleCode(options: ICompileSingleCodeOptions, recorder: Recorder): Promise<CodeFile>;
    compile(options: any, recorder: Recorder): Promise<ICompileResult>;
}

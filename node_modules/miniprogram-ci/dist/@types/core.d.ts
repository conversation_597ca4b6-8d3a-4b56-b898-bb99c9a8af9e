import { IProject } from './types';
import { compile } from './core/compile/index';
import { getPageJSON } from './core/json/page/getPageJSON';
import { compileJS } from './core/compile/handler/js';
import { getDevPluginJSON as getPluginJSON } from './core/json/plugin/plugin';
import { getPluginPageJSON } from './core/json/plugin/plugin_page';
import { IFSAgent } from './utils/fsagent';
import { checkThemeJSON, mergeThemeJSONToAppJSON, mergeThemeJSONToPageJSON } from './core/json/theme';
import { schemaValidate } from './core/validate/schemaValidate';
export * as partialAnalyse from './core/analyse/partial';
export declare function setFSAgent(agent: IFSAgent): void;
export declare const setLocale: (tag: string) => void;
declare function getAppJSON(project: IProject): Promise<import("./types").AppJSON.IAppJSON>;
export { getAppJSON };
export { getPageJSON };
export { compileJS };
export { compile };
declare function getGameJSON(project: IProject): Promise<import("./types").IGameJSON>;
export { getGameJSON };
export { getPluginJSON };
export { getPluginPageJSON };
export { checkThemeJSON };
export { schemaValidate };
export { mergeThemeJSONToAppJSON };
export { mergeThemeJSONToPageJSON };
export declare function cleanCache(): void;

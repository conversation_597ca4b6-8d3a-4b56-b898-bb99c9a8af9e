/// <reference types="node" />
import { MiniProgramCI } from '../../types';
interface IPackOptions {
    needMd5?: boolean;
    ignoreFileMd5?: MiniProgramCI.IStringKeyMap;
}
interface IPackResult {
    data: Buffer;
    totalSize: number;
    fileMd5Info: MiniProgramCI.IStringKeyMap;
}
export declare const packFileMap: (fileMap: MiniProgramCI.IStringKeyMap, options?: IPackOptions) => IPackResult;
export declare const packFiledir: (packPath: string, options?: IPackOptions) => Promise<IPackResult>;
export {};

import { IProject, MiniProgramCI } from '../types';
export declare const SIGNATURE_FILE_NAME = "ci.signature";
interface IUploadOptions {
    project: IProject;
    version: string;
    setting?: MiniProgramCI.ICompileSettings;
    desc?: string;
    robot?: number;
    threads?: number;
    useCOS?: boolean;
    onProgressUpdate?: (task: MiniProgramCI.ITaskStatus | string) => void;
    allowIgnoreUnusedFiles?: boolean;
}
export interface IInnerUploadOptions extends IUploadOptions {
    test?: boolean;
    bigPackageSizeSupport?: boolean;
    qrcodeFormat?: 'base64' | 'image' | 'terminal';
    qrcodeOutputDest?: string;
    pagePath?: string;
    searchQuery?: string;
    scene?: number;
}
export type TSubPackageInfo = Array<{
    name: string;
    size: number;
}>;
export type TPluginInfo = Array<{
    pluginProviderAppid: string;
    version: string;
    size: number;
}>;
interface IUploadResult {
    subPackageInfo?: TSubPackageInfo;
    pluginInfo?: TPluginInfo;
    devPluginId?: string;
}
export interface IInnerUploadResult extends IUploadResult {
    respBody?: any;
}
export declare function upload(options: IUploadOptions): Promise<IUploadResult>;
export declare function innerUpload(options: IInnerUploadOptions): Promise<IInnerUploadResult>;
export {};

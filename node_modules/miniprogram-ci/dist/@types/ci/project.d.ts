/// <reference types="node" />
import { MiniProgramCI } from '../types';
type IProject = MiniProgramCI.IProject;
type IStat = MiniProgramCI.IStat;
type ProjectType = MiniProgramCI.ProjectType;
export interface ICreateProjectOptions {
    projectPath: string;
    type: ProjectType;
    appid: string;
    privateKey?: string;
    privateKeyPath?: string;
    ignores?: string[];
}
export declare class Project implements IProject {
    miniprogramRoot: string;
    pluginRoot: string;
    private _appid;
    private _extAppid?;
    private _type;
    private _projectPath;
    private _dirSet;
    private _fileSet;
    private _fileBufferCache;
    private _attr;
    private _privateKey;
    private _ignores;
    constructor(options: ICreateProjectOptions);
    private init;
    private cacheDirName;
    private getTargetPath;
    updateFiles(): void;
    attr(): Promise<MiniProgramCI.IProjectAttr>;
    get projectPath(): string;
    get appid(): string;
    get type(): MiniProgramCI.ProjectType;
    get privateKey(): string;
    getFilesAndDirs(): {
        files: string[];
        dirs: string[];
    };
    getExtAppid(): Promise<string | void>;
    stat(prefix: string, filePath: string): IStat | undefined;
    getFile(prefix: string, filePath: string): Buffer;
    getFileList(prefix?: string, extName?: string): string[];
    onFileChange(type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', targetPath: string): Promise<void>;
}
export {};

/**
 * 根据入口JS文件路径，分析出所有依赖的js文件
 * @public
 * @param filePath - 分析入口文件路径，相对路径
 * @param fileHelper - IFileHelper实例
 * @returns 返回依赖的JS模块数组
 */
export declare function analyseJsDependenies(startPath: string | string[], fileHelper: IFileHelper): Promise<{
    startModules: IModule[];
    modules: IModule[];
}>;

/**
 * 分析单个代码文件的依赖项
 * @param filePath 分析目标文件
 * @param fileHelper - IFileHelper实例
 * @returns 返回IDep依赖项数组
 */
export declare function analyseSingeFile(filePath: string, fileHelper: IFileHelper): Promise<IDep[]>;

/**
 * @public
 */
export declare class Analyzer {
    root: string;
    type: CompileType;
    plugins: AnalyzerPlugin[];
    compilerPlugins: ICompilerPlugin[];
    fileHelper: IFileHelper;
    modules: IModule[];
    graph: Graph | undefined;
    /** 文件是否已经发生改变？ */
    invalid: boolean;
    constructor(option: AnalyzerOption);
    private getEntryDep;
    analyse(): Promise<void>;
    watch(callback: () => void): void;
    serialize(): IAnalyseResult | undefined;
    callPlugin<T extends Exclude<keyof AnalyzerPlugin, 'name'>>(event: T, ...args: any): void;
}

/**
 * @public
 */
export declare interface AnalyzerOption {
    root: string;
    type: CompileType;
    fileHelper?: IFileHelper;
    plugins?: AnalyzerPlugin[];
    compilerPlugins?: ICompilerPlugin[];
}

/**
 * @public
 */
export declare interface AnalyzerPlugin {
    name: string;
    created?(analyzer: Analyzer): void;
    beforeAnalyseProcessStart?(graph: Graph, prevGraph: Graph | null): void;
    afterAnalyseProcessComplete?(graph: Graph, cost: number): void;
    beforeModuleBuild?(module: IModule): void;
    onModuleBuildSucceed?(module: IModule, cost: number): void;
    onDepResolveSucceed?(dep: IDep): void;
    onDepResolveFailed?(dep: IDep, error: Error): void;
    onModuleBuildFailed?(module: IModule, cost: number, error: Error): void;
    afterModuleBuild?(module: IModule, cost: number, error: Error | null): void;
    onModuleReuse?(module: IModule): void;
    onModuleReuseFailed?(module: IModule, error: Error): void;
    onModuleSerialize?(module: IModule, data: Record<string, any>): void;
    onSerialize?(data: Record<string, any>): void;
}

/**
 * @public
 */
export declare type CompileType = 'miniprogram' | 'plugin' | 'game' | 'gamePlugin';

/**
 * @public
 */
export declare class FileHelper implements IFileHelper {
    root: string;
    type: CompileType;
    compilerPlugins: ICompilerPlugin[];
    dirSet: Set<string>;
    fileSet: Set<string>;
    constructor(root: string, type?: CompileType, compilerPlugins?: ICompilerPlugin[]);
    private cacheDirName;
    get pattern(): string;
    init(): void;
    stat(filePath: string): IStat | undefined;
    mtime(filePath: string): number;
    exist(filePath: string): boolean;
    existDir(filePath: string): boolean;
    existFile(filePath: string): boolean;
    getFileList(prefix?: string, extName?: string): string[];
    getString(filePath: string): Promise<string>;
    getJSON(filePath: string): Promise<unknown>;
    readdir(dirPath: string): Promise<string[]>;
    onFileChange(type: 'unlink' | 'unlinkDir' | 'add' | 'addDir' | 'change', targetPath: string): Promise<void>;
    watchFileChange(callback: () => void): void;
}

/**
 * @public
 */
export declare type FileType = 'Js' | 'Wxs' | 'Wxml' | 'Wxss';

export declare function findAllDescendant(mod: IModule | IModule[], type: ModuleType): IModule[];

/**
 * @public
 */
export declare type FromType = 'json' | 'file' | 'rule';

/**
 * @public
 */
export declare class Graph {
    status: 'inited' | 'building' | 'completed';
    private fileHelper;
    private pluginDriver;
    private _modules;
    get modules(): IModule[];
    prevGraph: Graph | undefined;
    /** 分析全程耗时 单位ms */
    costTime: number;
    graphContext: GraphContext;
    private runningTasks;
    private pendingTasks;
    private resolve;
    constructor(options: GraphOptions, prevGraph?: Graph);
    private processDep;
    private processDeps;
    private processTask;
    private run;
    private getModule;
    /**
     * 启动构建
     */
    build(dep: IDep | IDep[]): Promise<void>;
    private _build;
}

declare class GraphContext {
    fileHelper: IFileHelper;
    compilerPlugins: ICompilerPlugin[];
    resolveAliasConf?: Array<{
        key: string;
        value: string;
    }>;
    es6Parser: {
        isLayaGame: boolean;
    };
    miniprogram: {
        useExtendedPackages: string[];
        accountCardPackage?: IAppJSON['accountCardPackage'];
    };
    constructor(options: GraphOptions);
    private initMiniprogram;
    isUsedTypescript(): boolean;
    isUsedLess(): boolean;
    isUsedSass(): boolean;
    setResolveAlias(resolveAlias: IAppJSON['resolveAlias']): void;
    resolveJsFromAlias(request: string): string | undefined;
}

declare interface GraphOptions {
    fileHelper: IFileHelper;
    compilerPlugins?: ICompilerPlugin[];
    pluginDriver?: Analyzer;
}

/**
 * @public
 */
export declare interface IAnalyseResult {
    files: IPackageFile[];
    modules: IModuleSerialize[];
    compilerPlugins: ICompilerPlugin[];
}

declare interface IAppJSON {
    pages?: string[];
    workers?: string | {
        path: string;
        isSubpackage: boolean;
    };
    subPackages?: ISubpackageItem[];
    subpackages?: ISubpackageItem[];
    usingComponents?: IStringKeyMap;
    plugins?: {
        [alias: string]: IPluginConfig;
    };
    functionalPages?: boolean | {
        independent?: boolean;
    };
    useExtendedLib?: {
        [index: string]: boolean | string;
    };
    sitemapLocation?: string;
    themeLocation?: string;
    tabBar?: {
        custom?: boolean;
    };
    appBar?: {
        custom?: boolean;
    };
    resolveAlias?: {
        [key: string]: string;
    };
    accountCardPackage?: {
        root: string;
        cardList: Array<{
            type: number;
            componentPath: string;
        }>;
    };
}

export declare type ICompilerPlugin = 'typescript' | 'less' | 'sass';

/**
 * @public
 */
export declare interface IDep {
    from: FromType;
    type: ModuleType;
    request: string;
    path: string | null;
    module: IModule | null;
    originModule: IModule | null;
    error: Error | null;
    resolve(graphContext: GraphContext): Promise<boolean>;
    serialize(): IDepSerialize;
}

/**
 * @public
 */
export declare interface IDepSerialize {
    from: FromType;
    type: ModuleType;
    request: string;
    path: string | null;
    moduleId: ModuleId | null;
    originModuleId: ModuleId;
    error?: string;
}

/**
 * @public
 */
export declare interface IFileHelper {
    stat(filePath: string): IStat | undefined;
    mtime(filePath: string): number;
    exist(filePath: string): boolean;
    existDir(filePath: string): boolean;
    existFile(filePath: string): boolean;
    getFileList(prefix?: string, extName?: string): string[];
    getString(filePath: string): Promise<string>;
    getJSON(filePath: string): Promise<unknown>;
    readdir(dirPath: string): Promise<string[]>;
    watchFileChange(callback: () => void): void;
    getLocalFileString?(filePath: string): Promise<string>;
}

/**
 * @public
 */
export declare interface IModule {
    id: ModuleId;
    type: ModuleType;
    path: string;
    parentDeps: Set<IDep>;
    deps: Set<IDep>;
    warnings: string[];
    errors: Error[];
    buildError: Error | null;
    usePlugins?: Set<string>;
    useTags?: Set<string>;
    build(graphContext: GraphContext): Promise<IDep[]>;
    reuse?(graphContext: GraphContext): Promise<void>;
    isValid(): boolean;
    serialize(): IModuleSerialize;
    childModules: IModule[];
    findChild(type: ModuleType): IModule | null;
    findChildren(type: ModuleType): IModule[];
}

/**
 * @public
 */
export declare interface IModuleSerialize {
    id: string;
    type: ModuleType;
    path: string;
    parentDeps: Array<IDepSerialize>;
    deps: Array<IDepSerialize>;
    warnings?: string[];
    errors?: string[];
    buildError?: string;
}

/**
 * @public
 */
export declare interface IPackageFile {
    path: string;
    ext: string;
    size: number;
    moduleId: ModuleId | null;
    subPackage: string | null;
}

declare interface IPluginConfig {
    version: string;
    provider: string;
    export?: string;
    genericsImplementation?: {
        [key: string]: IStringKeyMap<string>;
    };
}

/**
 * @public
 */
export declare interface IStat {
    isFile: boolean;
    isDirectory: boolean;
    mtime: number;
    size?: number;
}

declare interface IStringKeyMap<T = any> {
    [propName: string]: T;
}

declare interface ISubpackageItem {
    name?: string;
    root: string;
    pages: string[];
    plugins: {
        [alias: string]: IPluginConfig;
    };
    independent: boolean;
    useExtendedLib: {
        [index: string]: boolean | string;
    };
}

/**
 * @public
 */
export declare enum ModuleErrorType {
    JSON_PARSE_ERR = "JSON_PARSE_ERR",
    WXML_PARSE_ERR = "WXML_PARSE_ERR",
    WXSS_PARSE_ERR = "WXSS_PARSE_ERR",
    JS_PARSE_ERR = "JS_PARSE_ERR",
    JS_RESOLVE_DEPENDENCY_ERR = "JS_RESOLVE_DEPENDENCY_ERR",
    WORKER_DIR_NOT_EXIST = "WORKER_DIR_NOT_EXIST"
}

/**
 * @public
 */
export declare type ModuleId = string;

/**
 * @public
 */
export declare type ModuleType = 'Js' | 'Wxml' | 'Wxss' | 'Wxs' | 'Config' | 'MainPackage' | 'SubPackage' | 'Page' | 'Component' | 'Plugin' | 'Worker' | 'FunctionalPages' | 'ContextModule' | 'PluginPackage' | 'GameMainPackage' | 'GameSubPackage';

export { }

export declare function tcbGetUsers(opts: IAPITCBGetUsersOptions): Promise<IAPITCBGetUsersResult>;
export declare function tcbGetDbDistribution(opts: IAPITCBDescribeDbDistributionOptions): Promise<IAPITCBDescribeDbDistributionResult>;
export declare function tcbGetStorageACL(opts: IAPITCBDescribeStorageACLOptions): Promise<IAPITCBDescribeStorageACLResult>;
export declare function tcbGetMonitorData(opts: IAPITCBDescribeMonitorDataOptions): Promise<IAPITCBDescribeMonitorDataResult>;
export declare function tcbDescribeCurveData(opts: IAPITCBDescribeCurveDataOptions): Promise<IAPITCBDescribeCurveDataResult>;
export declare function tcbGetStorageACLTask(opts: IAPITCBDescribeStorageACLTaskOptions, isPoll?: boolean): Promise<IAPITCBDescribeStorageACLTaskResult>;
export declare function tcbModifyStorageACL(opts: IAPITCBModifyStorageACLOptions): Promise<IAPITCBModifyStorageACLResult>;
export declare function tcbDescribeDatabaseACL(opts: IAPITCBDescribeDatabaseACLOptions): Promise<IAPITCBDescribeDatabaseACLResult>;
export declare function tcbModifyDatabaseACL(opts: IAPITCBModifyDatabaseACLOptions): Promise<IAPITCBModifyDatabaseACLResult>;
export declare function tcbDatabaseMigrateImport(opts: IAPITCBDatabaseMigrateImportOptions): Promise<IAPITCBDatabaseMigrateImportResult>;
export declare function tcbDatabaseMigrateExport(opts: IAPITCBDatabaseMigrateExportOptions): Promise<IAPITCBDatabaseMigrateExportResult>;
export declare function tcbDatabaseMigrateQueryInfo(opts: IAPITCBDatabaseMigrateQueryInfoOptions, isPoll?: boolean): Promise<IAPITCBDatabaseMigrateQueryInfoResult>;
export declare function tcbModifySafeRule(opts: IAPITCBModifySafeRuleOptions): Promise<IAPITCBModifySafeRuleResult>;
export declare function tcbDescribeSafeRule(opts: IAPITCBDescribeSafeRuleOptions): Promise<IAPITCBDescribeSafeRuleResult>;
export declare function tcbCheckEnvId(opts: IAPITCBCheckEnvIdOptions): Promise<IAPITCBCheckEnvIdResult>;
export declare function tcbCreateEnvAndResource(opts: IAPITCBCreateEnvAndResourceOptions): Promise<IAPITCBCreateEnvAndResourceResult>;
export declare function tcbDescribePackages(opts: IAPITCBDescribePackagesOptions): Promise<IAPITCBDescribePackagesResult>;
export declare function tcbInqueryPrice(opts: IAPITCBInqueryPriceOptions): Promise<IAPITCBInqueryPriceResult>;
export declare function tcbCreateDeal(opts: IAPITCBCreateDealOptions): Promise<IAPITCBCreateDealResult>;
export declare function tcbDescribePayInfo(opts: IAPITCBDescribePayInfoOptions): Promise<IAPITCBDescribePayInfoResult>;
export declare function tcbQueryDeals(opts: IAPITCBQueryDealsOptions): Promise<IAPITCBQueryDealsResult>;
export declare function tcbQueryAllDeals(opts: IAPITCBQueryDealsOptions, acc?: IAPIDealInfo[], offset?: number): Promise<IAPITCBQueryDealsResult>;
export declare function tcbCancelDeal(opts: IAPITCBCancelDealOptions): Promise<IAPITCBCancelDealResult>;
export declare function tcbDeleteDeal(opts: IAPITCBDeleteDealOptions): Promise<IAPITCBDeleteDealResult>;
export declare function tcbCheckEnvPackageModify(opts: IAPITCBCheckEnvPackageModifyOptions): Promise<IAPITCBCheckEnvPackageModifyResult>;
export declare function tcbDescribeBillingInfo(opts: IAPITCBDescribeBillingInfoOptions): Promise<IAPITCBDescribeBillingInfoResult>;
export declare function tcbDescribeNextExpireTime(opts: IAPITCBDescribeNextExpireTimeOptions): Promise<IAPITCBDescribeNextExpireTimeResult>;
export declare function tcbDescribeInvoiceAmount(opts: IAPITCBDescribeInvoiceAmountOptions): Promise<IAPITCBDescribeInvoiceAmountResult>;
export declare function tcbDescribeInvoiceSubject(opts: IAPITCBDescribeInvoiceSubjectOptions): Promise<IAPITCBDescribeInvoiceSubjectResult>;
export declare function tcbSetInvoiceSubject(opts: IAPITCBSetInvoiceSubjectOptions): Promise<IAPITCBSetInvoiceSubjectResult>;
export declare function tcbDescribeInvoicePostInfo(opts: IAPITCBDescribeInvoicePostInfoOptions): Promise<IAPITCBDescribeInvoicePostInfoResult>;
export declare function tcbCreateInvoicePostInfo(opts: IAPITCBCreateInvoicePostInfoOptions): Promise<IAPITCBCreateInvoicePostInfoResult>;
export declare function tcbModifyInvoicePostInfo(opts: IAPITCBModifyInvoicePostInfoOptions): Promise<IAPITCBModifyInvoicePostInfoResult>;
export declare function tcbDeleteInvoicePostInfo(opts: IAPITCBDeleteInvoicePostInfoOptions): Promise<IAPITCBDeleteInvoicePostInfoResult>;
export declare function tcbCreateInvoice(opts: IAPITCBCreateInvoiceOptions): Promise<IAPITCBCreateInvoiceResult>;
export declare function tcbDescribeInvoiceList(opts: IAPITCBDescribeInvoiceListOptions): Promise<IAPITCBDescribeInvoiceListResult>;
export declare function tcbDescribeInvoiceDetail(opts: IAPITCBDescribeInvoiceDetailOptions): Promise<IAPITCBDescribeInvoiceDetailResult>;
export declare function tcbRevokeInvoice(opts: IAPITCBRevokeInvoiceOptions): Promise<IAPITCBRevokeInvoiceResult>;
export declare function tcbDescribeAuthentification(opts: IAPITCBDescribeAuthentificationOptions): Promise<IAPITCBDescribeAuthentificationResult>;
export declare function tcbDescribeEnvResourceException(opts: IAPITCBDescribeEnvResourceExceptionOptions): Promise<IAPITCBDescribeEnvResourceExceptionResult>;
export declare function tcbResourceRecover(opts: IAPITCBResourceRecoverOptions): Promise<IAPITCBResourceRecoverResult>;
export declare function tcbDescribeResourceRecoverJob(opts: IAPITCBDescribeResourceRecoverJobOptions): Promise<IAPITCBDescribeResourceRecoverJobResult>;
export declare function tcbDescribeVouchersInfoByDeal(opts: IAPITCBDescribeVouchersInfoByDealOptions): Promise<IAPITCBDescribeVouchersInfoByDealResult>;
export declare function tcbDescribeAllVouchersInfoByDeal(opts: IAPITCBDescribeVouchersInfoByDealOptions, acc?: IAPITCBVoucher[], page?: number): Promise<IAPITCBDescribeVouchersInfoByDealResult>;
export declare function tcbDescribeAmountAfterDeduction(opts: IAPITCBDescribeAmountAfterDeductionOptions): Promise<IAPITCBDescribeAmountAfterDeductionResult>;
export declare function tcbDescribeVouchersInfo(opts: IAPITCBDescribeVouchersInfoOptions): Promise<IAPITCBDescribeVouchersInfoResult>;
export declare function tcbDescribeAllVouchersInfo(opts: IAPITCBDescribeVouchersInfoOptions, acc?: IAPITCBVoucher[], page?: number): Promise<IAPITCBDescribeVouchersInfoResult>;
export declare function tcbDescribeVoucherPlanAvailable(opts: IAPITCBDescribeVoucherPlanAvailableOptions): Promise<IAPITCBDescribeVoucherPlanAvailableResult>;
export declare function tcbApplyVoucher(opts: IAPITCBApplyVoucherOptions): Promise<IAPITCBApplyVoucherResult>;
export declare function tcbDescribeVoucherApplication(opts: IAPITCBDescribeVoucherApplicationOptions): Promise<IAPITCBDescribeVoucherApplicationResult>;
export declare function tcbDeleteVoucherApplication(opts: IAPITCBDeleteVoucherApplicationOptions): Promise<IAPITCBDeleteVoucherApplicationResult>;
export declare function tcbDescribeMonitorResource(opts: IAPITCBDescribeMonitorResourceOptions): Promise<IAPITCBDescribeMonitorResourceResult>;
export declare function tcbDescribeMonitorPolicy(opts: IAPITCBDescribeMonitorPolicyOptions): Promise<IAPITCBDescribeMonitorPolicyResult>;
export declare function tcbCreateMonitorPolicy(opts: IAPITCBCreateMonitorPolicyOptions): Promise<IAPITCBCreateMonitorPolicyResult>;
export declare function tcbDeleteMonitorPolicy(opts: IAPITCBDeleteMonitorPolicyOptions): Promise<IAPITCBDeleteMonitorPolicyResult>;
export declare function tcbModifyMonitorPolicy(opts: IAPITCBModifyMonitorPolicyOptions): Promise<IAPITCBModifyMonitorPolicyResult>;
export declare function tcbDescribeMonitorCondition(opts: IAPITCBDescribeMonitorConditionOptions): Promise<IAPITCBDescribeMonitorConditionResult>;
export declare function tcbCreateMonitorCondition(opts: IAPITCBCreateMonitorConditionOptions): Promise<IAPITCBCreateMonitorConditionResult>;
export declare function tcbDeleteMonitorCondition(opts: IAPITCBDeleteMonitorConditionOptions): Promise<IAPITCBDeleteMonitorConditionResult>;
export declare function tcbModifyMonitorCondition(opts: IAPITCBModifyMonitorConditionOptions): Promise<IAPITCBModifyMonitorConditionResult>;
export declare function tcbDescribeDauData(opts: IAPITCBDescribeDauDataOptions): Promise<IAPITCBDescribeDauDataResult>;
export declare function tcbDescribeChangePay(opts: IAPITCBDescribeChangePayOptions): Promise<IAPITCBDescribeChangePayResult>;
export declare function tcbDescribeRestoreHistory(opts: IAPITCBDescribeRestoreHistoryOptions): Promise<IAPITCBDescribeRestoreHistoryResult>;
export declare function tcbCommonServiceAPI(opts: IAPITCBCommonServiceAPIOptions): Promise<IAPITCBCommonServiceAPIResult>;
export declare function tcbCreatePostpayPackage(opts: IAPITCBCreatePostpayPackageOptions): Promise<IAPITCBCreatePostpayPackageResult>;
export declare function tcbInqueryPostpayPrice(opts: IAPITCBInqueryPostpayPriceOptions): Promise<IAPITCBInqueryPostpayPriceResult>;
export declare function tcbDescribePostpayFreeQuotas(opts: IAPITCBDescribePostpayFreeQuotasOptions): Promise<IAPITCBDescribePostpayFreeQuotasResult>;
export declare function tcbModifyStorageSafeRule(opts: IAPITCBModifyStorageSafeRuleOptions): Promise<IAPITCBModifyStorageSafeRuleResult>;
export declare function tcbDescribeStorageSafeRule(opts: IAPITCBDescribeStorageSafeRuleOptions): Promise<IAPITCBDescribeStorageSafeRuleResult>;
export declare function tcbDescribeCDNChainTask(opts: IAPITCBDescribeCDNChainTaskOptions): Promise<IAPITCBDescribeCDNChainTaskResult>;
export declare function tcbDescribeLoginConfigs(opts: IAPITCBDescribeLoginConfigsOptions): Promise<IAPITCBDescribeLoginConfigsResult>;
export declare function tcbCreateLoginConfig(opts: IAPITCBCreateLoginConfigOptions): Promise<IAPITCBCreateLoginConfigResult>;
export declare function tcbUpdateLoginConfig(opts: IAPITCBUpdateLoginConfigOptions): Promise<IAPITCBUpdateLoginConfigResult>;
export declare function tcbDescribeSecurityRule(opts: IAPITCBDescribeSecurityRuleOptions): Promise<IAPITCBDescribeSecurityRuleResult>;
export declare function tcbModifySecurityRule(opts: IAPITCBModifySecurityRuleOptions): Promise<IAPITCBModifySecurityRuleResult>;
export declare function tcbCreateStaticStore(opts: IAPITCBCreateStaticStoreOptions): Promise<IAPITCBCreateStaticStoreResult>;
export declare function tcbDestroyStaticStore(opts: IAPITCBDestroyStaticStoreOptions): Promise<IAPITCBDestroyStaticStoreResult>;
export declare function tcbDescribeStaticStore(opts: IAPITCBDescribeStaticStoreOptions): Promise<IAPITCBDescribeStaticStoreResult>;
export declare function tcbDescribeEnvLimit(opts: IAPITCBDescribeEnvLimitOptions): Promise<IAPITCBDescribeEnvLimitResult>;
export declare function tcbDescribeAccountInfoByPlatformId(opts: IAPITCBDescribeAccountInfoByPlatformIdOptions): Promise<IAPITCBDescribeAccountInfoByPlatformIdResult>;
export declare function tcbDescribeEnvFreeQuota(opts: IAPITCBDescribeEnvFreeQuotaOptions): Promise<IAPITCBDescribeEnvFreeQuotaResult>;
export declare function tcbDescribeHostingDomain(opts: IAPITCBDescribeHostingDomainOptions): Promise<IAPITCBDescribeHostingDomainResult>;
export declare function tcbDescribeCloudBaseRunResource(opts: IAPITCBDescribeCloudBaseRunResourceOptions): Promise<IAPITCBDescribeCloudBaseRunResourceResult>;
export declare function tcbDescribeCloudBaseRunServers(opts: IAPITCBDescribeCloudBaseRunServersOptions): Promise<IAPITCBDescribeCloudBaseRunServersResult>;
export declare function tcbCreateCloudBaseRunResource(opts: IAPITCBCreateCloudBaseRunResourceOptions): Promise<IAPITCBCreateCloudBaseRunResourceResult>;
export declare function tcbDescribeCloudBaseRunBuildServer(opts: IAPITCBDescribeCloudBaseRunBuildServerOptions): Promise<IAPITCBDescribeCloudBaseRunBuildServerResult>;
export declare function tcbDescribeCloudBaseRunServer(opts: IAPITCBDescribeCloudBaseRunServerOptions): Promise<IAPITCBDescribeCloudBaseRunServerResult>;
export declare function tcbDescribeCloudBaseRunContainerSpec(opts: IAPITCBDescribeCloudBaseRunContainerSpecOptions): Promise<IAPITCBDescribeCloudBaseRunContainerSpecResult>;
export declare function tcbCreateCloudBaseRunServerVersion(opts: IAPITCBCreateCloudBaseRunServerVersionOptions): Promise<IAPITCBCreateCloudBaseRunServerVersionResult>;
export declare function tcbDescribeCloudBaseRunServerVersion(opts: IAPITCBDescribeCloudBaseRunServerVersionOptions): Promise<IAPITCBDescribeCloudBaseRunServerVersionResult>;
export declare function tcbEstablishCloudBaseRunServer(opts: IAPITCBEstablishCloudBaseRunServerOptions): Promise<IAPITCBEstablishCloudBaseRunServerResult>;
export declare function tcbDeleteCloudBaseRunResource(opts: IAPITCBDeleteCloudBaseRunResourceOptions): Promise<IAPITCBDeleteCloudBaseRunResourceResult>;
export declare function tcbDescribeCloudBaseRunPodList(opts: IAPITCBDescribeCloudBaseRunPodListOptions): Promise<IAPITCBDescribeCloudBaseRunPodListResult>;
export declare function tcbDescribeCloudBaseRunBuildLog(opts: IAPITCBDescribeCloudBaseRunBuildLogOptions): Promise<IAPITCBDescribeCloudBaseRunBuildLogResult>;
export declare function tcbDescribeCloudBaseBuildService(opts: IAPITCBDescribeCloudBaseBuildServiceOptions): Promise<IAPITCBDescribeCloudBaseBuildServiceResult>;
export declare function tcbDescribeCloudBaseRunVersionException(opts: IAPITCBDescribeCloudBaseRunVersionExceptionOptions): Promise<IAPITCBDescribeCloudBaseRunVersionExceptionResult>;
export declare function tcbModifyCloudBaseRunServerVersion(opts: IAPITCBModifyCloudBaseRunServerVersionOptions): Promise<IAPITCBModifyCloudBaseRunServerVersionResult>;
export declare function tcbDescribeCloudBaseGWAPI(opts: IAPITCBDescribeCloudBaseGWAPIOptions): Promise<IAPITCBDescribeCloudBaseGWAPIResult>;
export declare function tcbDescribeCloudBaseRunBuildStages(opts: IAPITCBDescribeCloudBaseRunBuildStagesOptions): Promise<IAPITCBDescribeCloudBaseRunBuildStagesResult>;
export declare function tcbDescribeCloudBaseRunBuildSteps(opts: IAPITCBDescribeCloudBaseRunBuildStepsOptions): Promise<IAPITCBDescribeCloudBaseRunBuildStepsResult>;
export declare function tcbDescribeCloudBaseRunBuildStepLog(opts: IAPITCBDescribeCloudBaseRunBuildStepLogOptions): Promise<IAPITCBDescribeCloudBaseRunBuildStepLogResult>;
export declare function tcbDeleteCloudBaseRunServerVersion(opts: IAPITCBDeleteCloudBaseRunServerVersionOptions): Promise<IAPITCBDeleteCloudBaseRunServerVersionResult>;
export declare function tcbDeleteCloudBaseRunImageRepo(opts: IAPITCBDeleteCloudBaseRunImageRepoOptions): Promise<IAPITCBDeleteCloudBaseRunImageRepoResult>;
export declare function tcbRollUpdateCloudBaseRunServerVersion(opts: IAPITCBRollUpdateCloudBaseRunServerVersionOptions): Promise<IAPITCBRollUpdateCloudBaseRunServerVersionResult>;
export declare function tcbModifyCloudBaseRunServerFlowConf(opts: IAPITCBModifyCloudBaseRunServerFlowConfOptions): Promise<IAPITCBModifyCloudBaseRunServerFlowConfResult>;
export declare function tcbDeleteCloudBaseRunServer(opts: IAPITCBDeleteCloudBaseRunServerOptions): Promise<IAPITCBDeleteCloudBaseRunServerResult>;
export declare function tcbDescribeCloudBaseCodeRepos(opts: IAPITCBDescribeCloudBaseCodeReposOptions): Promise<IAPITCBDescribeCloudBaseCodeReposResult>;
export declare function tcbDescribeCloudBaseCodeBranch(opts: IAPITCBDescribeCloudBaseCodeBranchOptions): Promise<IAPITCBDescribeCloudBaseCodeBranchResult>;
export declare function tcbCreateHostingDomain(opts: IAPITCBCreateHostingDomainOptions): Promise<IAPITCBCreateHostingDomainResult>;
export declare function tcbDescribePostpayPackageList(_opts: IAPITCBDescribePostpayPackageListOptions): Promise<IAPITCBDescribePostpayPackageListResult>;
export declare function tcbQueryActivityPrice(opts: IAPITCBQueryActivityPriceOptions): Promise<IAPITCBQueryActivityPriceResult>;
export declare function tcbDeleteHostingDomain(opts: IAPITCBDeleteHostingDomainOptions): Promise<IAPITCBDeleteHostingDomainResult>;
export declare function tcbCheckQualification(opts: IAPITCBCheckQualificationOptions): Promise<IAPITCBCheckQualificationResult>;
export declare function tcbModifyHostingDomain(opts: IAPITCBModifyHostingDomainOptions): Promise<IAPITCBModifyHostingDomainResult>;
export declare function tcbCreateActivityDeal(opts: IAPITCBCreateActivityDealOptions): Promise<IAPITCBCreateActivityDealResult>;
export declare function tcbDescribeActivityGoods(opts: IAPITCBDescribeActivityGoodsOptions): Promise<IAPITCBDescribeActivityGoodsResult>;
export declare function tcbInqueryPackagePrice(opts: IAPITCBInqueryPackagePriceOptions): Promise<IAPITCBInqueryPackagePriceResult>;
export declare function tcbOnlineHostingDomain(opts: IAPITCBOnlineHostingDomainOptions): Promise<IAPITCBOnlineHostingDomainResult>;
export declare function tcbDescribeEnvPostpayPackage(opts: IAPITCBDescribeEnvPostpayPackageOptions): Promise<IAPITCBDescribeEnvPostpayPackageResult>;
export declare function tcbDescribePostpayQuotaLimit(opts: IAPITCBDescribePostpayQuotaLimitOptions): Promise<IAPITCBDescribePostpayQuotaLimitResult>;
export declare function tcbUpdatePostpayQuotaLimitStatus(opts: IAPITCBUpdatePostpayQuotaLimitStatusOptions): Promise<IAPITCBUpdatePostpayQuotaLimitStatusResult>;
export declare function tcbUpdatePostpayQuotaLimit(opts: IAPITCBUpdatePostpayQuotaLimitOptions): Promise<IAPITCBUpdatePostpayQuotaLimitResult>;
export declare function tcbUpdateScfConfig(opts: IAPITCBUpdateScfConfigOptions): Promise<IAPITCBUpdateScfConfigResult>;
export declare function tcbCreateInstallExtensionTask(opts: IAPITCBCreateInstallExtensionTaskOptions): Promise<IAPITCBCreateInstallExtensionTaskResult>;
export declare function tcbCreateUninstallExtensionTask(opts: IAPITCBCreateUninstallExtensionTaskOptions): Promise<IAPITCBCreateUninstallExtensionTaskResult>;
export declare function tcbDescribeExtensionInstalled(opts: IAPITCBDescribeExtensionInstalledOptions): Promise<IAPITCBDescribeExtensionInstalledResult>;
export declare function tcbDescribeExtensionTaskStatus(opts: IAPITCBDescribeExtensionTaskStatusOptions): Promise<IAPITCBDescribeExtensionTaskStatusResult>;
export declare function tcbDescribeExtensionTemplates(opts: IAPITCBDescribeExtensionTemplatesOptions): Promise<IAPITCBDescribeExtensionTemplatesResult>;
export declare function tcbDescribeExtensionUpgrade(opts: IAPITCBDescribeExtensionUpgradeOptions): Promise<IAPITCBDescribeExtensionUpgradeResult>;
export declare function tcbDescribeExtensions(opts: IAPITCBDescribeExtensionsOptions): Promise<IAPITCBDescribeExtensionsResult>;
export declare function tcbCreateUpgradeExtensionTask(opts: IAPITCBCreateUpgradeExtensionTaskOptions): Promise<IAPITCBCreateUpgradeExtensionTaskResult>;
export declare function tcbDescribeCloudBaseRunOperationDetails(opts: IAPITCBDescribeCloudBaseRunOperationDetailsOptions): Promise<IAPITCBDescribeCloudBaseRunOperationDetailsResult>;
export declare function tcbDescribeCloudBaseRunVersionSnapshot(opts: IAPITCBDescribeCloudBaseRunVersionSnapshotOptions): Promise<IAPITCBDescribeCloudBaseRunVersionSnapshotResult>;
export declare function tcbDescribeQcloudScene(opts: IAPITCBDescribeQcloudSceneOptions): Promise<IAPITCBDescribeQcloudSceneResult>;
export declare function tcbDescribeSmsQuotas(opts: IAPITCBDescribeSmsQuotasOptions): Promise<IAPITCBDescribeSmsQuotasResult>;
export declare function tcbDescribeSmsAttrInfo(opts: IAPITCBDescribeSmsAttrInfoOptions): Promise<IAPITCBDescribeSmsAttrInfoResult>;
export declare function tcbDescribeTcbBalance(opts: IAPITCBDescribeTcbBalanceOptions): Promise<IAPITCBDescribeTcbBalanceResult>;
export declare function tcbDescribeSmsRecords(opts: IAPITCBDescribeSmsRecordsOptions): Promise<IAPITCBDescribeSmsRecordsResult>;
export declare function tcbDescribeCloudBaseRunServiceDomain(opts: IAPITCBDescribeCloudBaseRunServiceDomainOptions): Promise<IAPITCBDescribeCloudBaseRunServiceDomainResult>;
export declare function tcbModifyEnv(opts: IAPITCBModifyEnvOptions): Promise<IAPITCBModifyEnvResult>;
export declare function tcbDescribeWxCloudBaseRunEnvs(opts: IAPITCBDescribeWxCloudBaseRunEnvsOptions): Promise<IAPITCBGetEnvironmentsResult>;
export declare function tcbDescribeWxCloudBaseRunSubNets(opts: IAPITCBDescribeWxCloudBaseRunSubNetsOptions): Promise<IAPITCBDescribeWxCloudBaseRunSubNetsResult>;
export declare function tcbRefundPostpaidPackage(opts: IAPITCBRefundPostpaidPackageOptions): Promise<IAPITCBRefundPostpaidPackageResult>;
export declare function tcbQueryPostpaidPackageDeals(opts: IAPITCBQueryPostpaidPackageDealsOptions): Promise<IAPITCBQueryPostpaidPackageDealsResult>;
export declare function tcbSearchClsLog(opts: IAPITCBSearchClsLogOptions): Promise<IAPITCBSearchClsLogResult>;
export declare function tcbDescribeAuditRule(opts: IAPITCBDescribeAuditRuleOptions): Promise<IAPITCBDescribeAuditRuleResult>;
export declare function tcbDescribeCollections(opts: IAPITCBDescribeCollectionsOptions): Promise<IAPITCBDescribeCollectionsResult>;
export declare function tcbCreateAuditRules(opts: IAPITCBCreateAuditRulesOptions): Promise<IAPITCBCreateAuditRulesResult>;
export declare function tcbDeleteAuditRule(opts: IAPITCBDeleteAuditRuleOptions): Promise<IAPITCBDeleteAuditRuleResult>;
export declare function tcbModifyAuditRule(opts: IAPITCBModifyAuditRuleOptions): Promise<IAPITCBModifyAuditRuleResult>;
export declare function tcbDescribeAuditResults(opts: IAPITCBDescribeAuditResultsOptions): Promise<IAPITCBDescribeAuditResultsResult>;
export declare function tcbUnfreezeSecurityAuditRecord(opts: IAPITCBUnfreezeSecurityAuditRecordOptions): Promise<IAPITCBUnfreezeSecurityAuditRecordResult>;
export declare function tcbDescribeSecurityAuditConfig(opts: IAPITCBDescribeSecurityAuditConfigOptions): Promise<IAPITCBDescribeSecurityAuditConfigResult>;
export declare function tcbDeleteSecurityAuditConfig(opts: IAPITCBDeleteSecurityAuditConfigOptions): Promise<IAPITCBDeleteSecurityAuditConfigResult>;
export declare function tcbCreateSecurityAuditConfig(opts: IAPITCBCreateSecurityAuditConfigOptions): Promise<IAPITCBCreateSecurityAuditConfigResult>;
export declare function tcbDescribeTriggerServiceParameters(opts: IAPITCBDescribeTriggerServiceParametersOptions): Promise<IAPITCBDescribeTriggerServiceParametersResult>;
export declare function tcbCreateTriggerConfigs(opts: IAPITCBCreateTriggerConfigsOptions): Promise<IAPITCBCreateTriggerConfigsResult>;
export declare function tcbDescribeTriggerConfigs(opts: IAPITCBDescribeTriggerConfigsOptions): Promise<IAPITCBDescribeTriggerConfigsResult>;
export declare function tcbUpdateTriggerConfig(opts: IAPITCBUpdateTriggerConfigOptions): Promise<IAPITCBUpdateTriggerConfigResult>;
export declare function tcbDeleteTriggerConfigs(opts: IAPITCBDeleteTriggerConfigsOptions): Promise<IAPITCBDeleteTriggerConfigsResult>;
export declare function tcbCreateCopyEnvTask(opts: IAPITCBCreateCopyEnvTaskOptions): Promise<IAPITCBCreateCopyEnvTaskResult>;
export declare function tcbDescribeExtensionsInstalled(opts: IAPITCBDescribeExtensionsInstalledOptions): Promise<IAPITCBDescribeExtensionsInstalledResult>;

export declare function flexdbListTables(opts: IAPIFlexDBListTablesOptions): Promise<IAPIFlexDBListTablesResult>;
export declare function flexdbCreateTable(opts: IAPIFlexDBCreateTableOptions): Promise<IAPIFlexDBCreateTableResult>;
export declare function flexdbDeleteTable(opts: IAPIFlexDBDeleteTableOptions): Promise<IAPIFlexDBDeleteTableResult>;
export declare function flexdbQuery(opts: IAPIFlexDBQueryOptions): Promise<IAPIFlexDBQueryResult>;
export declare function flexdbPutItem(opts: IAPIFlexDBPutItemOptions): Promise<IAPIFlexDBPutItemResult>;
export declare function flexdbUpdateItem(opts: IAPIFlexDBUpdateItemOptions): Promise<IAPIFlexDBUpdateItemResult>;
export declare function flexdbDeleteItem(opts: IAPIFlexDBDeleteItemOptions): Promise<IAPIFlexDBDeleteItemResult>;
export declare function flexdbCount(opts: IAPIFlexDBCountOptions): Promise<IAPIFlexDBCountResult>;
export declare function flexdbDescribeTable(opts: IAPIFlexDBDescribeTableOptions): Promise<IAPIFlexDBDescribeTableResult>;
export declare function flexdbUpdateTable(opts: IAPIFlexDBUpdateTableOptions): Promise<IAPIFlexDBUpdateTableResult>;
export declare function flexdbRunCommands(opts: IAPIFlexDBRunCommandsOptions): Promise<IAPIFlexDBRunCommandsResult>;
export declare function flexdbModifyTableNames(opts: IAPIFlexDBModifyTableNamesOptions): Promise<IAPIFlexDBModifyTableNamesResult>;
export declare function flexdbDescribeRestoreTables(opts: IAPIFLEXDBDescribeRestoreTablesOptions): Promise<IAPIFLEXDBDescribeRestoreTablesResult>;
export declare function flexdbDescribeRestoreTask(opts: IAPIFLEXDBDescribeRestoreTaskOptions): Promise<IAPIFLEXDBDescribeRestoreTaskResult>;
export declare function flexdbRestoreTCBTables(opts: IAPIFLEXDBRestoreTCBTablesOptions): Promise<IAPIFLEXDBRestoreTCBTablesResult>;
export declare function flexdbDescribeRestoreTime(opts: IAPIFLEXDBDescribeRestoreTimeOptions): Promise<IAPIFLEXDBDescribeRestoreTimeResult>;
export declare function flexdbModifyNameSpace(opts: IAPIFLEXDBModifyNameSpaceOptions): Promise<IAPIFLEXDBModifyNameSpaceResult>;

import { ITransactOptions } from '../../transaction/transactor';
export declare function scfListFunctions(opts: IAPISCFListFunctionsOptions, topts?: ITransactOptions): Promise<IAPISCFListFunctionsResult>;
export declare function scfListAllFunctions(opts: IAPISCFListFunctionsOptions, acc?: IAPICloudFunctionInfo[], offset?: number, topts?: ITransactOptions): Promise<IAPISCFListFunctionsResult>;
export declare function scfCreateFunction(opts: IAPISCFCreateFunctionOptions, topts?: ITransactOptions): Promise<IAPISCFCreateFunctionResult>;
export declare function scfUpdateFunction(opts: IAPISCFUpdateFunctionOptions, topts?: ITransactOptions): Promise<IAPISCFUpdateFunctionResult>;
export declare function scfUpdateFunctionIncrementalCode(opts: IAPISCFUpdateFunctionIncrementalCodeOptions, topts?: ITransactOptions): Promise<IAPISCFUpdateFunctionIncrementalCodeResult>;
export declare function scfDeleteFunction(opts: IAPISCFDeleteFunctionOptions, topts?: ITransactOptions): Promise<IAPISCFDeleteFunctionResult>;
export declare function scfGetFunctionInfo(opts: IAPISCFGetFunctionInfoOptions, topts?: ITransactOptions): Promise<IAPISCFGetFunctionInfoResult>;
export declare function scfUpdateFunctionInfo(opts: IAPISCFUpdateFunctionInfoOptions, topts?: ITransactOptions): Promise<IAPISCFUpdateFunctionInfoResult>;
export declare function scfListFunctionTestModels(opts: IAPISCFListFunctionTestModelsOptions, topts?: ITransactOptions): Promise<IAPISCFListFunctionTestModelsResult>;
export declare function scfGetFunctionTestModel(opts: IAPISCFGetFunctionTestModelOptions, topts?: ITransactOptions): Promise<IAPISCFGetFunctionTestModelResult>;
export declare function scfCreateFunctionTestModel(opts: IAPISCFCreateFunctionTestModelOptions, topts?: ITransactOptions): Promise<IAPISCFCreateFunctionTestModelResult>;
export declare function scfDeleteFunctionTestModel(opts: IAPISCFDeleteFunctionTestModelOptions, topts?: ITransactOptions): Promise<IAPISCFDeleteFunctionTestModelResult>;
export declare function scfUpdateFunctionTestModel(opts: IAPISCFUpdateFunctionTestModelOptions, topts?: ITransactOptions): Promise<IAPISCFUpdateFunctionTestModelResult>;
export declare function scfBatchCreateTrigger(opts: IAPISCFBatchCreateTriggerOptions, topts?: ITransactOptions): Promise<IAPISCFBatchCreateTriggerResult>;
export declare function scfGetFunctionAddress(opts: IAPISCFGetFunctionAddressOptions, topts?: ITransactOptions): Promise<IAPISCFGetFunctionAddressResult>;
export declare function scfInvokeFunction(opts: IAPISCFInvokeFunctionOptions, topts?: ITransactOptions): Promise<IAPISCFInvokeFunctionResult>;
export declare function scfGetFunctionLogs(opts: IAPISCFGetFunctionLogsOptions, isPoll?: boolean, topts?: ITransactOptions): Promise<IAPISCFGetFunctionLogsResult>;
export declare function scfUpdateAlias(opts: IAPISCFUpdateAliasOptions): Promise<IAPISCFUpdateAliasResult>;
export declare function scfGetAlias(opts: IAPISCFGetAliasOptions): Promise<IAPISCFGetAliasResult>;
export declare function scfPublishVersion(opts: IAPISCFPublishVersionOptions): Promise<IAPISCFPublishVersionResult>;
export declare function scfListVersionByFunction(opts: IAPISCFListVersionByFunctionOptions): Promise<IAPISCFListVersionByFunctionResult>;
export declare function scfGetProvisionedConcurrencyConfig(opts: IAPISCFGetProvisionedConcurrencyConfigOptions): Promise<IAPISCFGetProvisionedConcurrencyConfigResult>;
export declare function scfPutProvisionedConcurrencyConfig(opts: IAPISCFPutProvisionedConcurrencyConfigOptions): Promise<IAPISCFPutProvisionedConcurrencyConfigResult>;

import { ITransactOptions } from '../../transaction/transactor';
export declare function tcbGetEnvironments(opts: IAPITCBGetEnvironmentsOptions, topts?: ITransactOptions): Promise<IAPITCBGetEnvironmentsResult>;
export declare function tcbGetResourceLimit(opts: IAPITCBGetResourceLimitOptions): Promise<IAPITCBGetResourceLimitResult>;
export declare function tcbDescribeQuotaData(opts: IAPITCBDescribeQuotaDataOptions): Promise<IAPITCBDescribeQuotaDataResult>;
export declare function tcbDescribeEnvAccountCircle(opts: IAPITCBDescribeEnvAccountCircleOptions): Promise<IAPITCBDescribeEnvAccountCircleResult>;
export declare function tcbDescribeStatData(opts: IAPITCBDescribeStatDataOptions): Promise<IAPITCBDescribeStatDataResult>;

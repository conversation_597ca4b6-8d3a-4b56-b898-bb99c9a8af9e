import { JSONValidation } from '../../utils/validator';
import { TransactType } from '../transactor';
import { EventEmitter } from 'eventemitter3';
export type TTransactionContractRecord<I, O> = {
    timestamps: [number, number];
    input: I;
    maybeBrokenOutput: O;
    rawOutput?: unknown;
    isPoll: boolean;
    httpAgentIdentity: ITransactionContractHTTPAgentIdentity;
    error?: Error;
};
export interface ITransactionContract<I, O> {
    on(evt: 'record', fn: (record: TTransactionContractRecord<I, O>) => void): void;
}
export interface ITransactionContractHTTPAgentIdentity {
    cgi_id: number;
    service: string;
    path?: string;
    action?: string;
    version?: string;
    region?: string;
    bucket?: string;
    requestmethod?: string;
    params?: string;
}
export interface ITransactionContractOptions<I, O> {
    outputTransformationThrows?(possibleOutput: any, transactType: TransactType): O;
    inputTransformation?(input: I, transactType: TransactType): any;
}
export type ReportableRecord = {
    inputName: string;
} & TTransactionContractRecord<any, any>;
export declare const allContractsRecords: ReportableRecord[];
export interface IContractRecorder {
    on(evt: 'record', fn: (r: ReportableRecord) => void): void;
    emit(evt: 'record', r: ReportableRecord): void;
}
export declare const allContractRecorder: IContractRecorder;
export default class TransactionContract<I, O> extends EventEmitter implements ITransactionContract<I, O> {
    readonly inputTypeName: string;
    readonly outputTypeName: string;
    readonly outputValidation: JSONValidation;
    readonly getHttpAgentIdentity: (untransformedInput: I) => ITransactionContractHTTPAgentIdentity;
    readonly options: ITransactionContractOptions<I, O>;
    private validator;
    constructor(inputTypeName: string, outputTypeName: string, outputValidation: JSONValidation, getHttpAgentIdentity: (untransformedInput: I) => ITransactionContractHTTPAgentIdentity, options?: ITransactionContractOptions<I, O>);
    outputTransformationThrows(possibleOutput: any, transactType: TransactType): O;
    inputTransformation(input: I, transactType: TransactType): any;
    validOutput(data: any): data is O;
    validOutputThrows(data: any): void;
    commitRecord(r: {
        timestamps: [number, number];
        rawOutput?: any;
        input: I;
        output: O;
        error?: Error;
        httpAgentIdentity: ITransactionContractHTTPAgentIdentity;
    }, isPoll?: boolean): void;
}

import Factory from './factory';
export declare const scfListFunctionsContract: Factory<ITCSCFListFunctionsInput, ITCSCFListFunctionsOutput>;
export declare const scfCreateFunctionContract: Factory<ITCSCFCreateFunctionInput, ITCSCFCreateFunctionOutput>;
export declare const scfUpdateFunctionContract: Factory<ITCSCFUpdateFunctionInput, ITCSCFUpdateFunctionOutput>;
export declare const scfUpdateFunctionIncrementalCodeContract: Factory<ITCSCFUpdateFunctionIncrementalCodeInput, ITCSCFUpdateFunctionIncrementalCodeOutput>;
export declare const scfDeleteFunctionContract: Factory<ITCSCFDeleteFunctionInput, ITCSCFDeleteFunctionOutput>;
export declare const scfGetFunctionInfoContract: Factory<ITCSCFGetFunctionInfoInput, ITCSCFGetFunctionInfoOutput>;
export declare const scfUpdateFunctionInfoContract: Factory<ITCSCFUpdateFunctionInfoInput, ITCSCFUpdateFunctionInfoOutput>;
export declare const scfListFunctionTestModelsContract: Factory<ITCSCFListFunctionTestModelsInput, ITCSCFListFunctionTestModelsOutput>;
export declare const scfGetFunctionTestModelContract: Factory<ITCSCFGetFunctionTestModelInput, ITCSCFGetFunctionTestModelOutput>;
export declare const scfCreateFunctionTestModelContract: Factory<ITCSCFCreateFunctionTestModelInput, ITCSCFCreateFunctionTestModelOutput>;
export declare const scfDeleteFunctionTestModelContract: Factory<ITCSCFDeleteFunctionTestModelInput, ITCSCFDeleteFunctionTestModelOutput>;
export declare const scfUpdateFunctionTestModelContract: Factory<ITCSCFUpdateFunctionTestModelInput, ITCSCFUpdateFunctionTestModelOutput>;
export declare const scfBatchCreateTriggerContract: Factory<ITCSCFBatchCreateTriggerInput, ITCSCFBatchCreateTriggerOutput>;
export declare const scfGetFunctionAddressContract: Factory<ITCSCFGetFunctionAddressInput, ITCSCFGetFunctionAddressOutput>;
export declare const scfInvokeFunctionContract: Factory<ITCSCFInvokeFunctionInput, ITCSCFInvokeFunctionOutput>;
export declare const scfGetFunctionLogsContract: Factory<ITCSCFGetFunctionLogsInput, ITCSCFGetFunctionLogsOutput>;
export declare const scfUpdateAliasContract: Factory<ITCSCFUpdateAliasInput, ITCSCFUpdateAliasOutput>;
export declare const scfGetAliasContract: Factory<ITCSCFGetAliasInput, ITCSCFGetAliasOutput>;
export declare const scfPublishVersionContract: Factory<ITCSCFPublishVersionInput, ITCSCFPublishVersionOutput>;
export declare const scfListVersionByFunctionContract: Factory<ITCSCFListVersionByFunctionInput, ITCSCFListVersionByFunctionOutput>;
export declare const scfGetProvisionedConcurrencyConfigContract: Factory<ITCSCFGetProvisionedConcurrencyConfigInput, ITCSCFGetProvisionedConcurrencyConfigOutput>;
export declare const scfPutProvisionedConcurrencyConfigContract: Factory<ITCSCFPutProvisionedConcurrencyConfigInput, ITCSCFPutProvisionedConcurrencyConfigOutput>;

import Factory from './factory';
export declare const tcbGetEnvironmentsContract: Factory<ITCTCBGetEnvironmentsInput, ITCTCBGetEnvironmentsOutput>;
export declare const tcbGetUsersContract: Factory<ITCTCBGetUsersInput, ITCTCBGetUsersOutput>;
export declare const tcbGetResourceLimitContract: Factory<ITCTCBGetResourceLimitInput, ITCTCBGetResourceLimitOutput>;
export declare const tcbDescribeStatDataContract: Factory<ITCTCBDescribeStatDataInput, ITCTCBDescribeStatDataOutput>;
export declare const tcbDescribeQuotaDataContract: Factory<ITCTCBDescribeQuotaDataInput, ITCTCBDescribeQuotaDataOutput>;
export declare const tcbDescribeDbDistributionContract: Factory<ITCTCBDescribeDbDistributionInput, ITCTCBDescribeDbDistributionOutput>;
export declare const tcbDescribeMonitorDataContract: Factory<ITCTCBDescribeMonitorDataInput, ITCTCBDescribeMonitorDataOutput>;
export declare const tcbDescribeCurveDataContract: Factory<ITCTCBDescribeCurveDataInput, ITCTCBDescribeCurveDataOutput>;
export declare const tcbDescribeStorageACLContract: Factory<ITCTCBDescribeStorageACLInput, ITCTCBDescribeStorageACLOutput>;
export declare const tcbModifyStorageACLContract: Factory<ITCTCBModifyStorageACLInput, ITCTCBModifyStorageACLOutput>;
export declare const tcbDescribeStorageACLTaskContract: Factory<ITCTCBDescribeStorageACLTaskInput, ITCTCBDescribeStorageACLTaskOutput>;
export declare const tcbDescribeDatabaseACLContract: Factory<ITCTCBDescribeDatabaseACLInput, ITCTCBDescribeDatabaseACLOutput>;
export declare const tcbModifyDatabaseACLContract: Factory<ITCTCBModifyDatabaseACLInput, ITCTCBModifyDatabaseACLOutput>;
export declare const tcbDatabaseMigrateImportContract: Factory<ITCTCBDatabaseMigrateImportInput, ITCTCBDatabaseMigrateImportOutput>;
export declare const tcbDatabaseMigrateExportContract: Factory<ITCTCBDatabaseMigrateExportInput, ITCTCBDatabaseMigrateExportOutput>;
export declare const tcbDatabaseMigrateQueryInfoContract: Factory<ITCTCBDatabaseMigrateQueryInfoInput, ITCTCBDatabaseMigrateQueryInfoOutput>;
export declare const tcbModifySafeRuleContract: Factory<ITCTCBModifySafeRuleInput, ITCTCBModifySafeRuleOutput>;
export declare const tcbDescribeSafeRuleContract: Factory<ITCTCBDescribeSafeRuleInput, ITCTCBDescribeSafeRuleOutput>;
export declare const tcbCreateEnvAndResourceContract: Factory<ITCTCBCreateEnvAndResourceInput, ITCTCBCreateEnvAndResourceOutput>;
export declare const tcbCheckEnvIdContract: Factory<ITCTCBCheckEnvIdInput, ITCTCBCheckEnvIdOutput>;
export declare const tcbDescribeEnvAccountCircleContract: Factory<ITCTCBDescribeEnvAccountCircleInput, ITCTCBDescribeEnvAccountCircleOutput>;
export declare const tcbDescribePackagesContract: Factory<ITCTCBDescribePackagesInput, ITCTCBDescribePackagesOutput>;
export declare const tcbInqueryPriceContract: Factory<ITCTCBInqueryPriceInput, ITCTCBInqueryPriceOutput>;
export declare const tcbCreateDealContract: Factory<ITCTCBCreateDealInput, ITCTCBCreateDealOutput>;
export declare const tcbDescribePayInfoContract: Factory<ITCTCBDescribePayInfoInput, ITCTCBDescribePayInfoOutput>;
export declare const tcbQueryDealsContract: Factory<ITCTCBQueryDealsInput, ITCTCBQueryDealsOutput>;
export declare const tcbCancelDealContract: Factory<ITCTCBCancelDealInput, ITCTCBCancelDealOutput>;
export declare const tcbDeleteDealContract: Factory<ITCTCBDeleteDealInput, ITCTCBDeleteDealOutput>;
export declare const tcbCheckEnvPackageModifyContract: Factory<ITCTCBCheckEnvPackageModifyInput, ITCTCBCheckEnvPackageModifyOutput>;
export declare const tcbDescribeBillingInfoContract: Factory<ITCTCBDescribeBillingInfoInput, ITCTCBDescribeBillingInfoOutput>;
export declare const tcbDescribeNextExpireTimeContract: Factory<ITCTCBDescribeNextExpireTimeInput, ITCTCBDescribeNextExpireTimeOutput>;
export declare const tcbDescribeInvoiceAmountContract: Factory<ITCTCBDescribeInvoiceAmountInput, ITCTCBDescribeInvoiceAmountOutput>;
export declare const tcbDescribeInvoiceSubjectContract: Factory<ITCTCBDescribeInvoiceSubjectInput, ITCTCBDescribeInvoiceSubjectOutput>;
export declare const tcbSetInvoiceSubjectContract: Factory<ITCTCBSetInvoiceSubjectInput, ITCTCBSetInvoiceSubjectOutput>;
export declare const tcbDescribeInvoicePostInfoContract: Factory<ITCTCBDescribeInvoicePostInfoInput, ITCTCBDescribeInvoicePostInfoOutput>;
export declare const tcbCreateInvoicePostInfoContract: Factory<ITCTCBCreateInvoicePostInfoInput, ITCTCBCreateInvoicePostInfoOutput>;
export declare const tcbModifyInvoicePostInfoContract: Factory<ITCTCBModifyInvoicePostInfoInput, ITCTCBModifyInvoicePostInfoOutput>;
export declare const tcbDeleteInvoicePostInfoContract: Factory<ITCTCBDeleteInvoicePostInfoInput, ITCTCBDeleteInvoicePostInfoOutput>;
export declare const tcbCreateInvoiceContract: Factory<ITCTCBCreateInvoiceInput, ITCTCBCreateInvoiceOutput>;
export declare const tcbDescribeInvoiceListContract: Factory<ITCTCBDescribeInvoiceListInput, ITCTCBDescribeInvoiceListOutput>;
export declare const tcbDescribeInvoiceDetailContract: Factory<ITCTCBDescribeInvoiceDetailInput, ITCTCBDescribeInvoiceDetailOutput>;
export declare const tcbRevokeInvoiceContract: Factory<ITCTCBRevokeInvoiceInput, ITCTCBRevokeInvoiceOutput>;
export declare const tcbDescribeAuthentificationContract: Factory<ITCTCBDescribeAuthentificationInput, ITCTCBDescribeAuthentificationOutput>;
export declare const tcbDescribeEnvResourceExceptionContract: Factory<ITCTCBDescribeEnvResourceExceptionInput, ITCTCBDescribeEnvResourceExceptionOutput>;
export declare const tcbResourceRecoverContract: Factory<ITCTCBResourceRecoverInput, ITCTCBResourceRecoverOutput>;
export declare const tcbDescribeResourceRecoverJobContract: Factory<ITCTCBDescribeResourceRecoverJobInput, ITCTCBDescribeResourceRecoverJobOutput>;
export declare const tcbDescribeVouchersInfoByDealContract: Factory<ITCTCBDescribeVouchersInfoByDealInput, ITCTCBDescribeVouchersInfoByDealOutput>;
export declare const tcbDescribeAmountAfterDeductionContract: Factory<ITCTCBDescribeAmountAfterDeductionInput, ITCTCBDescribeAmountAfterDeductionOutput>;
export declare const tcbDescribeVouchersInfoContract: Factory<ITCTCBDescribeVouchersInfoInput, ITCTCBDescribeVouchersInfoOutput>;
export declare const tcbDescribeVoucherPlanAvailableContract: Factory<ITCTCBDescribeVoucherPlanAvailableInput, ITCTCBDescribeVoucherPlanAvailableOutput>;
export declare const tcbApplyVoucherContract: Factory<ITCTCBApplyVoucherInput, ITCTCBApplyVoucherOutput>;
export declare const tcbDescribeVoucherApplicationContract: Factory<ITCTCBDescribeVoucherApplicationInput, ITCTCBDescribeVoucherApplicationOutput>;
export declare const tcbDeleteVoucherApplicationContract: Factory<ITCTCBDeleteVoucherApplicationInput, ITCTCBDeleteVoucherApplicationOutput>;
export declare const tcbDescribeMonitorResourceContract: Factory<ITCTCBDescribeMonitorResourceInput, ITCTCBDescribeMonitorResourceOutput>;
export declare const tcbDescribeMonitorPolicyContract: Factory<ITCTCBDescribeMonitorPolicyInput, ITCTCBDescribeMonitorPolicyOutput>;
export declare const tcbCreateMonitorPolicyContract: Factory<ITCTCBCreateMonitorPolicyInput, ITCTCBCreateMonitorPolicyOutput>;
export declare const tcbDeleteMonitorPolicyContract: Factory<ITCTCBDeleteMonitorPolicyInput, ITCTCBDeleteMonitorPolicyOutput>;
export declare const tcbModifyMonitorPolicyContract: Factory<ITCTCBModifyMonitorPolicyInput, ITCTCBModifyMonitorPolicyOutput>;
export declare const tcbDescribeMonitorConditionContract: Factory<ITCTCBDescribeMonitorConditionInput, ITCTCBDescribeMonitorConditionOutput>;
export declare const tcbCreateMonitorConditionContract: Factory<ITCTCBCreateMonitorConditionInput, ITCTCBCreateMonitorConditionOutput>;
export declare const tcbDeleteMonitorConditionContract: Factory<ITCTCBDeleteMonitorConditionInput, ITCTCBDeleteMonitorConditionOutput>;
export declare const tcbModifyMonitorConditionContract: Factory<ITCTCBModifyMonitorConditionInput, ITCTCBModifyMonitorConditionOutput>;
export declare const tcbDescribeDauDataContract: Factory<ITCTCBDescribeDauDataInput, ITCTCBDescribeDauDataOutput>;
export declare const tcbDescribeChangePayContract: Factory<ITCTCBDescribeChangePayInput, ITCTCBDescribeChangePayOutput>;
export declare const tcbDescribeRestoreHistoryContract: Factory<ITCTCBDescribeRestoreHistoryInput, ITCTCBDescribeRestoreHistoryOutput>;
export declare const tcbCommonServiceAPIContract: Factory<ITCTCBCommonServiceAPIInput, ITCTCBCommonServiceAPIOutput>;
export declare const tcbCreatePostpayPackageContract: Factory<ITCTCBCreatePostpayPackageInput, ITCTCBCreatePostpayPackageOutput>;
export declare const tcbInqueryPostpayPriceContract: Factory<ITCTCBInqueryPostpayPriceInput, ITCTCBInqueryPostpayPriceOutput>;
export declare const tcbDescribePostpayFreeQuotasContract: Factory<ITCTCBDescribePostpayFreeQuotasInput, ITCTCBDescribePostpayFreeQuotasOutput>;
export declare const tcbModifyStorageSafeRuleContract: Factory<ITCTCBModifyStorageSafeRuleInput, ITCTCBModifyStorageSafeRuleOutput>;
export declare const tcbDescribeStorageSafeRuleContract: Factory<ITCTCBDescribeStorageSafeRuleInput, ITCTCBDescribeStorageSafeRuleOutput>;
export declare const tcbDescribeCDNChainTaskContract: Factory<ITCTCBDescribeCDNChainTaskInput, ITCTCBDescribeCDNChainTaskOutput>;
export declare const tcbDescribeLoginConfigsContract: Factory<ITCTCBDescribeLoginConfigsInput, ITCTCBDescribeLoginConfigsOutput>;
export declare const tcbCreateLoginConfigContract: Factory<ITCTCBCreateLoginConfigInput, ITCTCBCreateLoginConfigOutput>;
export declare const tcbUpdateLoginConfigContract: Factory<ITCTCBUpdateLoginConfigInput, ITCTCBUpdateLoginConfigOutput>;
export declare const tcbDescribeSecurityRuleContract: Factory<ITCTCBDescribeSecurityRuleInput, ITCTCBDescribeSecurityRuleOutput>;
export declare const tcbModifySecurityRuleContract: Factory<ITCTCBModifySecurityRuleInput, ITCTCBModifySecurityRuleOutput>;
export declare const tcbCreateStaticStoreContract: Factory<ITCTCBCreateStaticStoreInput, ITCTCBCreateStaticStoreOutput>;
export declare const tcbDestroyStaticStoreContract: Factory<ITCTCBDestroyStaticStoreInput, ITCTCBDestroyStaticStoreOutput>;
export declare const tcbDescribeStaticStoreContract: Factory<ITCTCBDescribeStaticStoreInput, ITCTCBDescribeStaticStoreOutput>;
export declare const tcbDescribeEnvLimitContract: Factory<ITCTCBDescribeEnvLimitInput, ITCTCBDescribeEnvLimitOutput>;
export declare const tcbDescribeAccountInfoByPlatformIdContract: Factory<ITCTCBDescribeAccountInfoByPlatformIdInput, ITCTCBDescribeAccountInfoByPlatformIdOutput>;
export declare const tcbDescribeEnvFreeQuotaContract: Factory<ITCTCBDescribeEnvFreeQuotaInput, ITCTCBDescribeEnvFreeQuotaOutput>;
export declare const tcbDescribeHostingDomainContract: Factory<ITCTCBDescribeHostingDomainInput, ITCTCBDescribeHostingDomainOutput>;
export declare const tcbDescribeCloudBaseRunResourceContract: Factory<ITCTCBDescribeCloudBaseRunResourceInput, ITCTCBDescribeCloudBaseRunResourceOutput>;
export declare const tcbDescribeCloudBaseRunServersContract: Factory<ITCTCBDescribeCloudBaseRunServersInput, ITCTCBDescribeCloudBaseRunServersOutput>;
export declare const tcbCreateCloudBaseRunResourceContract: Factory<ITCTCBCreateCloudBaseRunResourceInput, ITCTCBCreateCloudBaseRunResourceOutput>;
export declare const tcbDescribeCloudBaseRunBuildServerContract: Factory<ITCTCBDescribeCloudBaseRunBuildServerInput, ITCTCBDescribeCloudBaseRunBuildServerOutput>;
export declare const tcbDescribeCloudBaseRunServerContract: Factory<ITCTCBDescribeCloudBaseRunServerInput, ITCTCBDescribeCloudBaseRunServerOutput>;
export declare const tcbDescribeCloudBaseRunContainerSpecContract: Factory<ITCTCBDescribeCloudBaseRunContainerSpecInput, ITCTCBDescribeCloudBaseRunContainerSpecOutput>;
export declare const tcbCreateCloudBaseRunServerVersionContract: Factory<ITCTCBCreateCloudBaseRunServerVersionInput, ITCTCBCreateCloudBaseRunServerVersionOutput>;
export declare const tcbDescribeCloudBaseRunServerVersionContract: Factory<ITCTCBDescribeCloudBaseRunServerVersionInput, ITCTCBDescribeCloudBaseRunServerVersionOutput>;
export declare const tcbEstablishCloudBaseRunServerContract: Factory<ITCTCBEstablishCloudBaseRunServerInput, ITCTCBEstablishCloudBaseRunServerOutput>;
export declare const tcbDeleteCloudBaseRunResourceContract: Factory<ITCTCBDeleteCloudBaseRunResourceInput, ITCTCBDeleteCloudBaseRunResourceOutput>;
export declare const tcbDescribeCloudBaseRunPodListContract: Factory<ITCTCBDescribeCloudBaseRunPodListInput, ITCTCBDescribeCloudBaseRunPodListOutput>;
export declare const tcbDescribeCloudBaseRunBuildLogContract: Factory<ITCTCBDescribeCloudBaseRunBuildLogInput, ITCTCBDescribeCloudBaseRunBuildLogOutput>;
export declare const tcbDescribeCloudBaseBuildServiceContract: Factory<ITCTCBDescribeCloudBaseBuildServiceInput, ITCTCBDescribeCloudBaseBuildServiceOutput>;
export declare const tcbDescribeCloudBaseRunVersionExceptionContract: Factory<ITCTCBDescribeCloudBaseRunVersionExceptionInput, ITCTCBDescribeCloudBaseRunVersionExceptionOutput>;
export declare const tcbModifyCloudBaseRunServerVersionContract: Factory<ITCTCBModifyCloudBaseRunServerVersionInput, ITCTCBModifyCloudBaseRunServerVersionOutput>;
export declare const tcbDescribeCloudBaseGWAPIContract: Factory<ITCTCBDescribeCloudBaseGWAPIInput, ITCTCBDescribeCloudBaseGWAPIOutput>;
export declare const tcbDescribeCloudBaseRunBuildStagesContract: Factory<ITCTCBDescribeCloudBaseRunBuildStagesInput, ITCTCBDescribeCloudBaseRunBuildStagesOutput>;
export declare const tcbDescribeCloudBaseRunBuildStepsContract: Factory<ITCTCBDescribeCloudBaseRunBuildStepsInput, ITCTCBDescribeCloudBaseRunBuildStepsOutput>;
export declare const tcbDescribeCloudBaseRunBuildStepLogContract: Factory<ITCTCBDescribeCloudBaseRunBuildStepLogInput, ITCTCBDescribeCloudBaseRunBuildStepLogOutput>;
export declare const tcbDeleteCloudBaseRunServerVersionContract: Factory<ITCTCBDeleteCloudBaseRunServerVersionInput, ITCTCBDeleteCloudBaseRunServerVersionOutput>;
export declare const tcbDeleteCloudBaseRunImageRepoContract: Factory<ITCTCBDeleteCloudBaseRunImageRepoInput, ITCTCBDeleteCloudBaseRunImageRepoOutput>;
export declare const tcbRollUpdateCloudBaseRunServerVersionContract: Factory<ITCTCBRollUpdateCloudBaseRunServerVersionInput, ITCTCBRollUpdateCloudBaseRunServerVersionOutput>;
export declare const tcbModifyCloudBaseRunServerFlowConfContract: Factory<ITCTCBModifyCloudBaseRunServerFlowConfInput, ITCTCBModifyCloudBaseRunServerFlowConfOutput>;
export declare const tcbDeleteCloudBaseRunServerContract: Factory<ITCTCBDeleteCloudBaseRunServerInput, ITCTCBDeleteCloudBaseRunServerOutput>;
export declare const tcbDescribeCloudBaseCodeReposContract: Factory<ITCTCBDescribeCloudBaseCodeReposInput, ITCTCBDescribeCloudBaseCodeReposOutput>;
export declare const tcbDescribeCloudBaseCodeBranchContract: Factory<ITCTCBDescribeCloudBaseCodeBranchInput, ITCTCBDescribeCloudBaseCodeBranchOutput>;
export declare const tcbCreateHostingDomainContract: Factory<ITCTCBCreateHostingDomainInput, ITCTCBCreateHostingDomainOutput>;
export declare const tcbDescribePostpayPackageListContract: Factory<ITCTCBDescribePostpayPackageListInput, ITCTCBDescribePostpayPackageListOutput>;
export declare const tcbDeleteHostingDomainContract: Factory<ITCTCBDeleteHostingDomainInput, ITCTCBDeleteHostingDomainOutput>;
export declare const tcbQueryActivityPriceContract: Factory<ITCTCBQueryActivityPriceInput, ITCTCBQueryActivityPriceOutput>;
export declare const tcbModifyHostingDomainContract: Factory<ITCTCBModifyHostingDomainInput, ITCTCBModifyHostingDomainOutput>;
export declare const tcbCheckQualificationContract: Factory<ITCTCBCheckQualificationInput, ITCTCBCheckQualificationOutput>;
export declare const tcbOnlineHostingDomainContract: Factory<ITCTCBOnlineHostingDomainInput, ITCTCBOnlineHostingDomainOutput>;
export declare const tcbCreateActivityDealContract: Factory<ITCTCBCreateActivityDealInput, ITCTCBCreateActivityDealOutput>;
export declare const tcbDescribeActivityGoodsContract: Factory<ITCTCBDescribeActivityGoodsInput, ITCTCBDescribeActivityGoodsOutput>;
export declare const tcbInqueryPackagePriceContract: Factory<ITCTCBInqueryPackagePriceInput, ITCTCBInqueryPackagePriceOutput>;
export declare const tcbDescribeEnvPostpayPackageContract: Factory<ITCTCBDescribeEnvPostpayPackageInput, ITCTCBDescribeEnvPostpayPackageOutput>;
export declare const tcbDescribePostpayQuotaLimitContract: Factory<ITCTCBDescribePostpayQuotaLimitInput, ITCTCBDescribePostpayQuotaLimitOutput>;
export declare const tcbUpdatePostpayQuotaLimitStatusContract: Factory<ITCTCBUpdatePostpayQuotaLimitStatusInput, ITCTCBUpdatePostpayQuotaLimitStatusOutput>;
export declare const tcbUpdatePostpayQuotaLimitContract: Factory<ITCTCBUpdatePostpayQuotaLimitInput, ITCTCBUpdatePostpayQuotaLimitOutput>;
export declare const tcbUpdateScfConfigContract: Factory<ITCTCBUpdateScfConfigInput, ITCTCBUpdateScfConfigOutput>;
export declare const tcbCreateInstallExtensionTaskContract: Factory<ITCTCBCreateInstallExtensionTaskInput, ITCTCBCreateInstallExtensionTaskOutput>;
export declare const tcbCreateUninstallExtensionTaskContract: Factory<ITCTCBCreateUninstallExtensionTaskInput, ITCTCBCreateUninstallExtensionTaskOutput>;
export declare const tcbDescribeExtensionInstalledContract: Factory<ITCTCBDescribeExtensionInstalledInput, ITCTCBDescribeExtensionInstalledOutput>;
export declare const tcbDescribeExtensionTaskStatusContract: Factory<ITCTCBDescribeExtensionTaskStatusInput, ITCTCBDescribeExtensionTaskStatusOutput>;
export declare const tcbDescribeExtensionTemplatesContract: Factory<ITCTCBDescribeExtensionTemplatesInput, ITCTCBDescribeExtensionTemplatesOutput>;
export declare const tcbDescribeExtensionUpgradeContract: Factory<ITCTCBDescribeExtensionUpgradeInput, ITCTCBDescribeExtensionUpgradeOutput>;
export declare const tcbDescribeExtensionsContract: Factory<ITCTCBDescribeExtensionsInput, ITCTCBDescribeExtensionsOutput>;
export declare const tcbCreateUpgradeExtensionTaskContract: Factory<ITCTCBCreateUpgradeExtensionTaskInput, ITCTCBCreateUpgradeExtensionTaskOutput>;
export declare const tcbDescribeCloudBaseRunOperationDetailsContract: Factory<ITCTCBDescribeCloudBaseRunOperationDetailsInput, ITCTCBDescribeCloudBaseRunOperationDetailsOutput>;
export declare const tcbDescribeCloudBaseRunVersionSnapshotContract: Factory<ITCTCBDescribeCloudBaseRunVersionSnapshotInput, ITCTCBDescribeCloudBaseRunVersionSnapshotOutput>;
export declare const tcbDescribeQcloudSceneContract: Factory<ITCTCBDescribeQcloudSceneInput, ITCTCBDescribeQcloudSceneOutput>;
export declare const tcbDescribeSmsQuotasContract: Factory<ITCTCBDescribeSmsQuotasInput, ITCTCBDescribeSmsQuotasOutput>;
export declare const tcbDescribeSmsAttrInfoContract: Factory<ITCTCBDescribeSmsAttrInfoInput, ITCTCBDescribeSmsAttrInfoOutput>;
export declare const tcbDescribeTcbBalanceContract: Factory<ITCTCBDescribeTcbBalanceInput, ITCTCBDescribeTcbBalanceOutput>;
export declare const tcbDescribeSmsRecordsContract: Factory<ITCTCBDescribeSmsRecordsInput, ITCTCBDescribeSmsRecordsOutput>;
export declare const tcbDescribeCloudBaseRunServiceDomainContract: Factory<ITCTCBDescribeCloudBaseRunServiceDomainInput, ITCTCBDescribeCloudBaseRunServiceDomainOutput>;
export declare const tcbModifyEnvContract: Factory<ITCTCBModifyEnvInput, ITCTCBModifyEnvOutput>;
export declare const tcbDescribeWxCloudBaseRunEnvsContract: Factory<ITCTCBDescribeWxCloudBaseRunEnvsInput, ITCTCBDescribeWxCloudBaseRunEnvsOutput>;
export declare const tcbDescribeWxCloudBaseRunSubNetsContract: Factory<ITCTCBDescribeWxCloudBaseRunSubNetsInput, ITCTCBDescribeWxCloudBaseRunSubNetsOutput>;
export declare const tcbRefundPostpaidPackageContract: Factory<ITCTCBRefundPostpaidPackageInput, ITCTCBRefundPostpaidPackageOutput>;
export declare const tcbQueryPostpaidPackageDealsContract: Factory<ITCTCBQueryPostpaidPackageDealsInput, ITCTCBQueryPostpaidPackageDealsOutput>;
export declare const tcbSearchClsLogContract: Factory<ITCTCBSearchClsLogInput, ITCTCBSearchClsLogOutput>;
export declare const tcbDescribeAuditRuleContract: Factory<ITCTCBDescribeAuditRuleInput, ITCTCBDescribeAuditRuleOutput>;
export declare const tcbDescribeCollectionsContract: Factory<ITCTCBDescribeCollectionsInput, ITCTCBDescribeCollectionsOutput>;
export declare const tcbCreateAuditRulesContract: Factory<ITCTCBCreateAuditRulesInput, ITCTCBCreateAuditRulesOutput>;
export declare const tcbDeleteAuditRuleContract: Factory<ITCTCBDeleteAuditRuleInput, ITCTCBDeleteAuditRuleOutput>;
export declare const tcbModifyAuditRuleContract: Factory<ITCTCBModifyAuditRuleInput, ITCTCBModifyAuditRuleOutput>;
export declare const tcbDescribeAuditResultsContract: Factory<ITCTCBDescribeAuditResultsInput, ITCTCBDescribeAuditResultsOutput>;
export declare const tcbUnfreezeSecurityAuditRecordContract: Factory<ITCTCBUnfreezeSecurityAuditRecordInput, ITCTCBUnfreezeSecurityAuditRecordOutput>;
export declare const tcbDescribeSecurityAuditConfigContract: Factory<ITCTCBDescribeSecurityAuditConfigInput, ITCTCBDescribeSecurityAuditConfigOutput>;
export declare const tcbDeleteSecurityAuditConfigContract: Factory<ITCTCBDeleteSecurityAuditConfigInput, ITCTCBDeleteSecurityAuditConfigOutput>;
export declare const tcbCreateSecurityAuditConfigContract: Factory<ITCTCBCreateSecurityAuditConfigInput, ITCTCBCreateSecurityAuditConfigOutput>;
export declare const tcbDescribeTriggerServiceParametersContract: Factory<ITCTCBDescribeTriggerServiceParametersInput, ITCTCBDescribeTriggerServiceParametersOutput>;
export declare const tcbCreateTriggerConfigsContract: Factory<ITCTCBCreateTriggerConfigsInput, ITCTCBCreateTriggerConfigsOutput>;
export declare const tcbDescribeTriggerConfigsContract: Factory<ITCTCBDescribeTriggerConfigsInput, ITCTCBDescribeTriggerConfigsOutput>;
export declare const tcbUpdateTriggerConfigContract: Factory<ITCTCBUpdateTriggerConfigInput, ITCTCBUpdateTriggerConfigOutput>;
export declare const tcbDeleteTriggerConfigsContract: Factory<ITCTCBDeleteTriggerConfigsInput, ITCTCBDeleteTriggerConfigsOutput>;
export declare const tcbCreateCopyEnvTaskContract: Factory<ITCTCBCreateCopyEnvTaskInput, ITCTCBCreateCopyEnvTaskOutput>;
export declare const tcbDescribeExtensionsInstalledContract: Factory<ITCTCBDescribeExtensionsInstalledInput, ITCTCBDescribeExtensionsInstalledOutput>;

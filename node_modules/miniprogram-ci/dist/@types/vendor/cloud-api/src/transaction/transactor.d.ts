import { TransactionContract } from './contracts/contracts';
import { ITransactionContractHTTPAgentIdentity } from './contracts/factory';
export declare enum TransactType {
    Mock = 1,
    HTTP = 2,
    IDEPlugin = 3,
    IDE = 4
}
export interface ITransactOptions {
    validOutput?: boolean;
    autoRecord?: boolean;
    isPoll?: boolean;
    request?: IRequestFunction;
    transactType?: TransactType;
}
export interface IRequestOptions {
    postdata: any;
    dataType?: string;
    headers?: Record<string, string>;
    method?: string;
    identity: ITransactionContractHTTPAgentIdentity;
    contract: TransactionContract<any, any>;
    _input: any;
}
export interface IRequestFunction {
    (options: IRequestOptions): Promise<any>;
}
export declare function setRequest(fn: (options: IRequestOptions) => Promise<any>): void;
export declare function setTransactType(type: TransactType): void;
export declare function setDefaultAppID(appid: string): void;
export declare function getDefaultAppID(): string;
export declare function transact<I, O>(contract: TransactionContract<I, O>, _input: I, options?: ITransactOptions): Promise<NonNullable<O>>;
export default transact;

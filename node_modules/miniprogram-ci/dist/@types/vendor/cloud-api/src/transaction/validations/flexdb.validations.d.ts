import * as v from '../../utils/validator';
export declare const flexdbPagerValidation: v.DeepImmutableJSONValidation;
export declare const flexdbTableInfoValidation: v.DeepImmutableJSONValidation;
export declare const flexdbIndexkeyValidation: v.DeepImmutableJSONValidation;
export declare const flexdbIndexAccessesValidation: v.DeepImmutableJSONValidation;
export declare const flexdbIndexInfoValidation: v.DeepImmutableJSONValidation;
export declare const flexdbListTablesOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbCreateTableOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbDeleteTableOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbQueryOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbPutItemOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbUpdateItemOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbDeleteItemOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbCountOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbDescribeTableOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbUpdateTableOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbRunCommandsOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbModifyTableNamesOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbDescribeRestoreTablesOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbDescribeRestoreTaskOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbRestoreTCBTablesOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbDescribeRestoreTimeOutputValidation: v.DeepImmutableJSONValidation;
export declare const flexdbModifyNameSpaceOutputValidation: v.DeepImmutableJSONValidation;

import * as v from '../../utils/validator';
export declare const scfFunctionTagValidation: {
    Key: string;
    Value: string;
};
export declare const scfFunctionValidation: v.DeepImmutableJSONValidation;
export declare const scfListFunctionsOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfCreateFunctionOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfUpdateFunctionOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfUpdateFunctionIncrementalCodeOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfUpdateFunctionInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfDeleteFunctionOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfGetFunctionInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfListFunctionTestModelsOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfGetFunctionTestModelOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfCreateFunctionTestModelOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfDeleteFunctionTestModelOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfUpdateFunctionTestModelOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfBatchCreateTriggerOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfGetFunctionAddressOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfInvokeFunctionOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfGetFunctionLogsOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfUpdateAliasOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfGetAliasOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfPublishVersionOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfListVersionByFunctionOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfGetProvisionedConcurrencyConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const scfPutProvisionedConcurrencyConfigOutputValidation: v.DeepImmutableJSONValidation;

import * as v from '../../utils/validator';
export declare const noValidation: v.DeepImmutableJSONValidation;
export declare const commonOutputValidation: v.DeepImmutableJSONValidation;
export declare const databaseInfoValidation: v.DeepImmutableJSONValidation;
export declare const storageInfoValidation: v.DeepImmutableJSONValidation;
export declare const functionInfoValidation: v.DeepImmutableJSONValidation;
export declare const logServiceInfoValidation: v.DeepImmutableJSONValidation;
export declare const envInfoValidation: v.DeepImmutableJSONValidation;
export declare const userInfoValidation: v.DeepImmutableJSONValidation;
export declare const limitInfoValidation: v.DeepImmutableJSONValidation;
export declare const storageExceptionValidation: v.DeepImmutableJSONValidation;
export declare const logServiceExceptionValidation: v.DeepImmutableJSONValidation;
export declare const recoverResultValidation: v.DeepImmutableJSONValidation;
export declare const recoverJobStatusValidation: v.DeepImmutableJSONValidation;
export declare const voucherValidation: v.DeepImmutableJSONValidation;
export declare const voucherApplication: v.DeepImmutableJSONValidation;
export declare const monitorConditionInfoValidation: v.DeepImmutableJSONValidation;
export declare const describePolicyRuleValidation: v.DeepImmutableJSONValidation;
export declare const monitorPolicyInfoValidation: v.DeepImmutableJSONValidation;
export declare const monitorResourceValidation: v.DeepImmutableJSONValidation;
export declare const dauStatDataValidation: v.DeepImmutableJSONValidation;
export declare const restoreHistoryItemValidation: v.DeepImmutableJSONValidation;
export declare const freequotaInfoValidation: v.DeepImmutableJSONValidation;
export declare const priceInfoValidation: v.DeepImmutableJSONValidation;
export declare const modifyTableNamesInfoValidation: v.DeepImmutableJSONValidation;
export declare const restoreTableTimeRangeValidation: v.DeepImmutableJSONValidation;
export declare const restoreTaskValidation: v.DeepImmutableJSONValidation;
export declare const tableRestoreTimeValidation: v.DeepImmutableJSONValidation;
export declare const loginConfigItemValidation: v.DeepImmutableJSONValidation;
export declare const functionVersionValidation: v.DeepImmutableJSONValidation;
export declare const versionWeightValidation: v.DeepImmutableJSONValidation;
export declare const versionMatchValidation: v.DeepImmutableJSONValidation;
export declare const routingConfigValidation: v.DeepImmutableJSONValidation;
export declare const staticStoreInfoValidation: v.DeepImmutableJSONValidation;
export declare const postpayEnvQuotaValidation: v.DeepImmutableJSONValidation;
export declare const tcbOriginValidation: v.DeepImmutableJSONValidation;
export declare const tcbAuthenticationValidation: v.DeepImmutableJSONValidation;
export declare const tcbCacheValidation: v.DeepImmutableJSONValidation;
export declare const tcbStaticValidation: v.DeepImmutableJSONValidation;
export declare const tcbCertInfoValidation: v.DeepImmutableJSONValidation;
export declare const tcbHttpsValidation: v.DeepImmutableJSONValidation;
export declare const tcbHeaderRuleValidation: v.DeepImmutableJSONValidation;
export declare const tcbHeaderValidation: v.DeepImmutableJSONValidation;
export declare const ipFilterValidation: v.DeepImmutableJSONValidation;
export declare const ipFreqLimitValidation: v.DeepImmutableJSONValidation;
export declare const forceRedirectValidation: v.DeepImmutableJSONValidation;
export declare const tcbRefererRuleValidation: v.DeepImmutableJSONValidation;
export declare const tcbRefererValidation: v.DeepImmutableJSONValidation;
export declare const hostingDomainValidation: v.DeepImmutableJSONValidation;
export declare const tcbDomainConfigValidation: v.DeepImmutableJSONValidation;
export declare const tcbDomainInfoValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunVpcSubnetValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunServerItemValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunContainerStandardValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunImageInfoValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseCodeRepoNameValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseCodeRepoDetailValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunImageSecretInfoValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunNfsVolumeSourceValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunVolumeMountValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunKvPriorityValidation: v.DeepImmutableJSONValidation;
export declare const objectKvValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunServerVersionItemValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunVersionPodValidation: v.DeepImmutableJSONValidation;
export declare const kvPairValidation: v.DeepImmutableJSONValidation;
export declare const cloudRunExceptionLinkInfoValidation: v.DeepImmutableJSONValidation;
export declare const cloudRunExceptionAdviceValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseGwapiValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunBuildStagesValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunBuildLogValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunVersionFlowItemValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseCodeBranchValidation: v.DeepImmutableJSONValidation;
export declare const certificateExtraValidation: v.DeepImmutableJSONValidation;
export declare const projectInfoValidation: v.DeepImmutableJSONValidation;
export declare const certificatesValidation: v.DeepImmutableJSONValidation;
export declare const postpayPackageInfoValidation: v.DeepImmutableJSONValidation;
export declare const dianshiProductValidation: v.DeepImmutableJSONValidation;
export declare const envPostpayPackageInfoValidation: v.DeepImmutableJSONValidation;
export declare const postpayQuotaLimitValidation: v.DeepImmutableJSONValidation;
export declare const upgradeResItemValidation: v.DeepImmutableJSONValidation;
export declare const extensionTaskStatusValidation: v.DeepImmutableJSONValidation;
export declare const userExtensionInfoValidation: v.DeepImmutableJSONValidation;
export declare const extensionInfoValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunServiceHistoryActionInfoValidation: v.DeepImmutableJSONValidation;
export declare const cloudBaseRunServiceHistoryActionValidation: v.DeepImmutableJSONValidation;
export declare const cloudRunServiceSimpleVersionSnapshotValidation: v.DeepImmutableJSONValidation;
export declare const monitorPeriodValidation: v.DeepImmutableJSONValidation;
export declare const smsFreeQuotaValidation: v.DeepImmutableJSONValidation;
export declare const recordValidation: v.DeepImmutableJSONValidation;
export declare const smsRecordValidation: v.DeepImmutableJSONValidation;
export declare const postpaidPackageDealInfoValidation: v.DeepImmutableJSONValidation;
export declare const logObjectValidation: v.DeepImmutableJSONValidation;
export declare const logResObjectValidation: v.DeepImmutableJSONValidation;
export declare const auditTypeInfoValidation: v.DeepImmutableJSONValidation;
export declare const auditRuleInfoValidation: v.DeepImmutableJSONValidation;
export declare const auditDetailInfoValidation: v.DeepImmutableJSONValidation;
export declare const triggerActionTypeParameterValidation: v.DeepImmutableJSONValidation;
export declare const triggerConditionTypeParameterValidation: v.DeepImmutableJSONValidation;
export declare const triggerTypeParameterValidation: v.DeepImmutableJSONValidation;
export declare const triggerConditionValidation: v.DeepImmutableJSONValidation;
export declare const triggerActionValidation: v.DeepImmutableJSONValidation;
export declare const triggerConfigValidation: v.DeepImmutableJSONValidation;
export declare const tagValidation: v.DeepImmutableJSONValidation;
export declare const envExtensionItemValidation: v.DeepImmutableJSONValidation;
export declare const versionProvisionedConcurrencyInfoValidation: v.DeepImmutableJSONValidation;
export declare const triggerActionSCFValidation: v.DeepImmutableJSONValidation;

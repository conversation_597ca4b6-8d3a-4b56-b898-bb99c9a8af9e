import * as v from '../../utils/validator';
export declare const tcbGetEnvironmentsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbGetUsersOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbGetResourceLimitOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeStatDataOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeQuotaDataOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeDbDistributionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeMonitorDataOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCurveDataOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeStorageACLOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyStorageACLOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeStorageACLTaskOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeDatabaseACLOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyDatabaseACLOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDatabaseMigrateImportOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDatabaseMigrateExportOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDatabaseMigrateQueryInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifySafeRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeSafeRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCheckEnvIdOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateEnvAndResourceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeEnvAccountCircleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribePackagesOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbInqueryPriceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateDealOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribePayInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbQueryDealsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCancelDealOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteDealOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCheckEnvPackageModifyOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbOrderInfoValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeBillingInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeNextExpireTimeOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeInvoiceAmountOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbInvoiceVATGeneralValidation: v.DeepImmutableJSONValidation;
export declare const tcbInvoiceVATSpecialValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeInvoiceSubjectOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbSetInvoiceSubjectOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbInvoicePostInfoValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeInvoicePostInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateInvoicePostInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyInvoicePostInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteInvoicePostInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateInvoiceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbInvoiceBasicInfo: v.DeepImmutableJSONValidation;
export declare const tcbDescribeInvoiceListOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeInvoiceDetailOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbRevokeInvoiceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeAuthentificationOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeEnvResourceExceptionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbResourceRecoverOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeResourceRecoverJobOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeVouchersInfoByDealOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeAmountAfterDeductionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeVouchersInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeVoucherPlanAvailableOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbApplyVoucherOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeVoucherApplicationOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteVoucherApplicationOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeMonitorResourceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeMonitorPolicyOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateMonitorPolicyOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteMonitorPolicyOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyMonitorPolicyOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeMonitorConditionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateMonitorConditionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteMonitorConditionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyMonitorConditionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeDauDataOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeChangePayOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeRestoreHistoryOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCommonServiceAPIOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreatePostpayPackageOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbInqueryPostpayPriceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribePostpayFreeQuotasOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyStorageSafeRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeStorageSafeRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCDNChainTaskOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeLoginConfigsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateLoginConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbUpdateLoginConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeSecurityRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifySecurityRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateStaticStoreOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDestroyStaticStoreOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeStaticStoreOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeEnvLimitOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeAccountInfoByPlatformIdOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeEnvFreeQuotaOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeHostingDomainOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunResourceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunServersOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateCloudBaseRunResourceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunBuildServerOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunServerOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunContainerSpecOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateCloudBaseRunServerVersionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunServerVersionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbEstablishCloudBaseRunServerOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteCloudBaseRunResourceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunPodListOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunBuildLogOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseBuildServiceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunVersionExceptionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyCloudBaseRunServerVersionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseGWAPIOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunBuildStagesOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunBuildStepsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunBuildStepLogOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteCloudBaseRunServerVersionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteCloudBaseRunImageRepoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbRollUpdateCloudBaseRunServerVersionOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyCloudBaseRunServerFlowConfOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteCloudBaseRunServerOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseCodeReposOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseCodeBranchOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateHostingDomainOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteHostingDomainOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyHostingDomainOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbOnlineHostingDomainOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribePostpayPackageListOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbQueryActivityPriceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCheckQualificationOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateActivityDealOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeActivityGoodsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbInqueryPackagePriceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeEnvPostpayPackageOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribePostpayQuotaLimitOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbUpdatePostpayQuotaLimitStatusOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbUpdatePostpayQuotaLimitOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbUpdateScfConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateInstallExtensionTaskOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateUninstallExtensionTaskOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeExtensionInstalledOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeExtensionTaskStatusOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeExtensionTemplatesOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeExtensionUpgradeOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeExtensionsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateUpgradeExtensionTaskOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunOperationDetailsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunVersionSnapshotOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeQcloudSceneOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeSmsQuotasOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeSmsAttrInfoOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeTcbBalanceOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeSmsRecordsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCloudBaseRunServiceDomainOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyEnvOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeWxCloudBaseRunEnvsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeWxCloudBaseRunSubNetsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbRefundPostpaidPackageOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbQueryPostpaidPackageDealsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbSearchClsLogOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeAuditRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeCollectionsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateAuditRulesOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteAuditRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbModifyAuditRuleOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeAuditResultsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbUnfreezeSecurityAuditRecordOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeSecurityAuditConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteSecurityAuditConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateSecurityAuditConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeTriggerServiceParametersOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateTriggerConfigsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeTriggerConfigsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbUpdateTriggerConfigOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDeleteTriggerConfigsOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbCreateCopyEnvTaskOutputValidation: v.DeepImmutableJSONValidation;
export declare const tcbDescribeExtensionsInstalledOutputValidation: v.DeepImmutableJSONValidation;

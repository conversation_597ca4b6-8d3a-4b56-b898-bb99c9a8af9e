import Factory from './factory';
export declare const flexdbListTablesContract: Factory<ITCFlexDBListTablesInput, ITCFlexDBListTablesOutput>;
export declare const flexdbCreateTableContract: Factory<ITCFlexDBCreateTableInput, ITCFlexDBCreateTableOutput>;
export declare const flexdbDeleteTableContract: Factory<ITCFlexDBDeleteTableInput, ITCFlexDBDeleteTableOutput>;
export declare const flexdbQueryContract: Factory<ITCFlexDBQueryInput, ITCFlexDBQueryOutput>;
export declare const flexdbPutItemContract: Factory<ITCFlexDBPutItemInput, ITCFlexDBPutItemOutput>;
export declare const flexdbUpdateItemContract: Factory<ITCFlexDBUpdateItemInput, ITCFlexDBUpdateItemOutput>;
export declare const flexdbDeleteItemContract: Factory<ITCFlexDBDeleteItemInput, ITCFlexDBDeleteItemOutput>;
export declare const flexdbCountContract: Factory<ITCFlexDBCountInput, ITCFlexDBCountOutput>;
export declare const flexdbDescribeTableContract: Factory<ITCFlexDBDescribeTableInput, ITCFlexDBDescribeTableOutput>;
export declare const flexdbUpdateTableContract: Factory<ITCFlexDBUpdateTableInput, ITCFlexDBUpdateTableOutput>;
export declare const flexdbRunCommandsContract: Factory<ITCFlexDBRunCommandsInput, ITCFlexDBRunCommandsOutput>;
export declare const flexdbModifyTableNamesContract: Factory<ITCFlexDBModifyTableNamesInput, ITCFlexDBModifyTableNamesOutput>;
export declare const flexdbDescribeRestoreTablesContract: Factory<ITCFLEXDBDescribeRestoreTablesInput, ITCFLEXDBDescribeRestoreTablesOutput>;
export declare const flexdbDescribeRestoreTaskContract: Factory<ITCFLEXDBDescribeRestoreTaskInput, ITCFLEXDBDescribeRestoreTaskOutput>;
export declare const flexdbRestoreTCBTablesContract: Factory<ITCFLEXDBRestoreTCBTablesInput, ITCFLEXDBRestoreTCBTablesOutput>;
export declare const flexdbDescribeRestoreTimeContract: Factory<ITCFLEXDBDescribeRestoreTimeInput, ITCFLEXDBDescribeRestoreTimeOutput>;
export declare const flexdbModifyNameSpaceContract: Factory<ITCFLEXDBModifyNameSpaceInput, ITCFLEXDBModifyNameSpaceOutput>;

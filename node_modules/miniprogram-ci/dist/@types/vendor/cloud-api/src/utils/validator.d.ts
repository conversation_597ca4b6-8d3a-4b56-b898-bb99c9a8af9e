declare interface IJSONObject {
    [x: string]: JSONLike;
}
declare interface IJSONArray extends Array<JSONLike> {
}
type PrimitiveJSONValues = boolean | number | null | string | undefined;
declare type JSONLike = IJSONObject | IJSONArray | PrimitiveJSONValues;
type Primitive = undefined | null | boolean | string | number | (typeof Function);
interface IDeepImmutableArray<T> extends ReadonlyArray<DeepImmutable<T>> {
}
interface IDeepImmutableMap<K, V> extends ReadonlyMap<DeepImmutable<K>, DeepImmutable<V>> {
}
type DeepImmutableObject<T> = {
    readonly [K in keyof T]: DeepImmutable<T[K]>;
};
type DeepImmutable<T> = T extends Primitive ? T : T extends Array<infer U> ? IDeepImmutableArray<U> : T extends Map<infer K, infer V> ? IDeepImmutableMap<K, V> : DeepImmutableObject<T>;
export type JSONValidationFn = (input: any, path: string, validator: typeof validJSON) => void;
export type JSONValidation = JSONValidationFn | Record<string, any> | JSONLike;
export type DeepImmutableJSONValidation = DeepImmutable<JSONValidation>;
import { EventEmitter } from 'eventemitter3';
export declare function getType(o: any): string;
export interface IValidator {
    on(type: 'fail', fn: (input: any, e: Error) => void): void;
    on(type: 'success', fn: (input: any) => void): void;
}
export declare class Validator extends EventEmitter implements IValidator {
    readonly typeName: string;
    readonly structure: DeepImmutable<JSONValidation>;
    constructor(typeName: string, structure: JSONValidation);
    validThrows(input: any): void;
}
declare class _S {
    readonly symbol: symbol;
    readonly o: any;
    constructor(symbol: symbol, o: any);
}
export declare function $optional(o: JSONValidation): _S;
export declare function $arrayOf(o: JSONValidation): _S;
export declare function $multiType(...types: JSONValidation[]): _S;
declare function validJSON(input: any, validation: DeepImmutableJSONValidation | _S, path?: string): void;
export {};

import { MiniProgramCI } from '../../types';
export declare class ChildProcessCrashedError extends Error {
    type: 'ERR_WORKER_OUT_OF_MEMORY';
    constructor(type: 'ERR_WORKER_OUT_OF_MEMORY', message: string);
}
declare class TaskManager {
    private _taskQueue;
    private _instance;
    runTask(name: string, data: any, onStatusUpdate?: MiniProgramCI.FN<void>): Promise<unknown>;
    private _run;
    private allocChildProcess;
    private onChildProcessExit;
    private onTaskDone;
}
export declare const childProcessManager: TaskManager;
export {};

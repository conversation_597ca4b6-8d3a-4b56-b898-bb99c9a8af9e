export declare enum ETaskStatus {
    waiting = 0,
    progress = 1,
    done = 2
}
export declare const TASK_NAME: {
    COMPILE_JS: string;
    COMPILE_WXSS: string;
    MINIFY_WXML: string;
    SUMMER_HOOK: string;
};
export declare const COMMAND: {
    RUN_TASK: string;
    TASK_DONE: string;
    CALL_FUNC: string;
    CALL_FUNC_RESULT: string;
    CHILD_PROCESS_READY: string;
    SEND_LOG: string;
};
export declare enum EWorkerStatus {
    free = 0,
    busy = 1,
    dying = 2
}
export declare enum EChildProcessStatus {
    free = 0,
    busy = 1,
    fullload = 2,
    dying = 3
}
export declare const SUICIDE_TIME: {
    devtools: number;
    'miniprogram-ci': number;
};

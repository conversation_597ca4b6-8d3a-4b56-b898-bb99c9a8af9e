/// <reference types="node" />
import { MiniProgramCI } from '../../../types';
interface ICompileData {
    projectPath: string;
    root: string;
    filePath: string;
    setting: MiniProgramCI.ICompileSettings;
    code: Buffer;
}
declare function compileWXSS(data: ICompileData): Promise<{
    error: null;
    code: string;
} | {
    error: {
        code: number;
        path: string;
        message: any;
    };
    code?: undefined;
}>;
export = compileWXSS;

declare function processTask(taskName: string, data: any): Promise<{
    error: {
        path: string;
        name: string;
        message: string;
        stack?: string | undefined;
    } | {
        path: string;
        message: string;
        code: number;
    };
    isLargeFile?: undefined;
    isBabelIgnore?: undefined;
    map?: undefined;
    code?: undefined;
    helpers?: undefined;
} | {
    error: null;
    code: string;
} | {
    error: {
        code: number;
        path: string;
        message: any;
    };
    code?: undefined;
} | import("../../../summer/worker").IRunSummerPluginHookResult | undefined>;
export = processTask;

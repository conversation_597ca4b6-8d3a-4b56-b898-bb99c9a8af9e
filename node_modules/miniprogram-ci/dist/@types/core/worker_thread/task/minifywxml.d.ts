/// <reference types="node" />
import { MiniProgramCI } from '../../../types';
interface ICompileData {
    projectPath: string;
    root: string;
    filePath: string;
    setting: MiniProgramCI.ICompileSettings;
    code: <PERSON><PERSON>er;
}
declare enum TokenType {
    ATTR = 0,
    STRING1 = 1,
    STRING2 = 2,
    OTHERS = 3
}
declare class Token {
    type: TokenType;
    value: string;
    constructor(type: TokenType, value: string);
}
declare class Tokenizer {
    constructor(src: string, path: string);
    generateTokens(output: Array<Token>): number;
    private m_pSrc;
    private path;
    private machine;
}
declare function generateWXMLFromTokens(tokens: Array<Token>): string;
declare function minifyWXML(data: ICompileData): Promise<{
    error: null;
    code: string;
} | {
    error: {
        code: number;
        path: string;
        message: any;
    };
    code?: undefined;
}>;
declare const _default: {
    minifyWXML: typeof minifyWXML;
    Tokenizer: typeof Tokenizer;
    generateWXMLFromTokens: typeof generateWXMLFromTokens;
    Token: typeof Token;
};
export = _default;

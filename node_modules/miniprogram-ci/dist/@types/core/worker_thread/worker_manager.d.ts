import { MiniProgramCI } from '../../types';
export declare const AbortEvent = "WorkerTaskAborted";
declare class WorkerManager {
    private _taskQueue;
    private _workerPool;
    private _max_pool_size;
    constructor(size: number, isDevtools?: boolean);
    runTask(name: string, data: any, onStatusUpdate?: MiniProgramCI.FN<void>): Promise<unknown>;
    abort(name: string): void;
    private _run;
    private allocWorker;
    private workerCount;
    private onWorkerExit;
    private _actualWorkerPoolSize;
    private _doneThreadSet;
    private onTaskDone;
}
export declare const getWorkerManager: (isDevtools?: boolean) => WorkerManager;
export {};

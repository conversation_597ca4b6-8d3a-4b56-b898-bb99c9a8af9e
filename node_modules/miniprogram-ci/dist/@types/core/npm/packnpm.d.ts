import { IProject, MiniProgramCI } from '../../types';
interface IPackNpmOptions {
    ignores?: string[];
    reporter?: MiniProgramCI.FN<void>;
}
export declare function packNpm(project: IProject, opts?: IPackNpmOptions): Promise<MiniProgramCI.IWarnItem[]>;
interface IPackNpmManuallyResult {
    miniProgramPackNum: number;
    otherNpmPackNum: number;
    warnList: Array<MiniProgramCI.IWarnItem>;
}
export declare function packNpmManually(options: {
    packageJsonPath: string;
    miniprogramNpmDistDir: string;
    ignores?: Array<string>;
}): Promise<IPackNpmManuallyResult>;
export {};

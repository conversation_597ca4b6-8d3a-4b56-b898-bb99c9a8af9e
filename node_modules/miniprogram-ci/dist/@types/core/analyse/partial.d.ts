import type { Analyzer, IModule, ModuleType } from '../../vendor/code-analyse';
export declare function findAllDescendant(mod: IModule | IModule[], type: ModuleType): IModule[];
export declare function partialGetSubPkgSortedJSFiles(analyzer: Analyzer, pages: string[], subPackage: string): {
    allFiles: string[];
    pageFiles: string[];
    componentFiles: string[];
};
export declare function partialGetMainPkgSortedJSFiles(analyzer: Analyzer, pages: string[]): {
    hasAppJS: boolean;
    allFiles: string[];
    pageFiles: string[];
    componentFiles: string[];
};
export declare function partialWholePkgGetSortedJSFiles(analyzer: Analyzer, pages: string[]): {
    hasAppJS: boolean;
    allFiles: string[];
    pageFiles: string[];
    componentFiles: string[];
};
export declare function partialGetWxmlAndWxsFiles(analyzer: Analyzer, pages: string[], subPackage?: string): {
    wxmlFiles: string[];
    wxsFiles: string[];
};
export declare function partialGetWxssFiles(analyzer: Analyzer, pages: string[], subPackage?: string): string[];
export declare function partialGetCodeFiles(analyzer: Analyzer, pages: string[]): string[];

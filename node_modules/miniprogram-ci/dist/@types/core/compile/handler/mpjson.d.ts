/// <reference types="node" />
import { IProject, MiniProgramCI } from '../../../types';
export declare function addSkylineRendererToComponents(pageJSONResults: {
    [key: string]: string;
}, potantialComponentResults: {
    [key: string]: string;
}): void;
export declare function compileJSON(project: IProject, options: MiniProgramCI.ICompileOptions): Promise<{
    [filePath: string]: Buffer | string;
}>;

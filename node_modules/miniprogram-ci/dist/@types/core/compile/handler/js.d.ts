import { RawSourceMap } from 'source-map';
import { IProject, MiniProgramCI } from '../../../types';
export declare function compileJS(project: IProject, filePath: string, options: {
    root?: string;
    sourceCode?: string;
    setting?: MiniProgramCI.ICompileSettings;
    babelRoot?: string;
    onProgressUpdate?: (status: MiniProgramCI.ITaskStatus) => void;
    devToolsCompileCache?: MiniProgramCI.IDevToolsCompileCache;
}): Promise<{
    filePath: string;
    code: string;
    helpers: string[];
    map: string | RawSourceMap;
}>;

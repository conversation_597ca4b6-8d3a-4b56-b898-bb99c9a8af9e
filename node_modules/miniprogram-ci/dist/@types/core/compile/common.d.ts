/// <reference types="node" />
import { IProject, MiniProgramCI, ProjectConfigJSON } from '../../types';
export declare function getBabelRoot(project: IProject): Promise<string>;
export declare function isNotIgnoredByProjectConfig(projectConfig: MiniProgramCI.IAnyObject, root: string, file: string): boolean;
export declare function compileOther(project: IProject, files: string[], options: MiniProgramCI.ICompileOptions): Promise<{
    [filePath: string]: Buffer;
}>;
export declare function compileJSFiles(project: IProject, files: string[], root: string, options: MiniProgramCI.ICompileOptions): Promise<{
    [filePath: string]: string;
}>;
export declare function compileWXSSFiles(project: IProject, files: string[], root: string, options: MiniProgramCI.ICompileOptions): Promise<{
    [filePath: string]: string;
}>;
export declare function compileWXMLFiles(project: IProject, files: string[], root: string, options: MiniProgramCI.ICompileOptions): Promise<{
    [filePath: string]: string;
}>;
export declare function getUploadProjectConfig(project: IProject, projectConfig: ProjectConfigJSON.IProjectConfigJSON): Promise<MiniProgramCI.IAnyObject>;

type ILoggerLevel = 'trace' | 'info' | 'log' | 'warn' | 'error';
type ILoggerOutput = (level: ILoggerLevel, args: any[]) => void;
declare class Logger {
    private output;
    private enableLevel;
    private ready;
    private logs;
    constructor(output: ILoggerOutput);
    setOutput(output: ILoggerOutput): void;
    setEnableLevel(level: number): void;
    setReady(): void;
    send(level: ILoggerLevel, args: any[]): void;
    private getPrintTime;
    private _receive;
    info(msg: string, ...args: any[]): void;
    log(msg: string, ...args: any[]): void;
    error(msg: string, ...args: any[]): void;
}
export declare const logger: Logger;
export {};

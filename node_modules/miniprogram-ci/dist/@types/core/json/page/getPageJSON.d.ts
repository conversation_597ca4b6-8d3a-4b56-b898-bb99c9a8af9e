import { ReactiveProject } from '../reactiveCache';
import { IProject, PageJSON } from '../../../types';
export declare function originGetPageJSON(project: ReactiveProject, options: {
    miniprogramRoot: string;
    pagePath: string;
}): PageJSON.IPageJSON;
export declare function getPageJSON(project: IProject, options: {
    miniprogramRoot: string;
    pagePath: string;
}): Promise<PageJSON.IPageJSON>;

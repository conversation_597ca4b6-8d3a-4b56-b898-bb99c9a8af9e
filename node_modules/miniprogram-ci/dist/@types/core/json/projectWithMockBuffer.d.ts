/// <reference types="node" />
import { Project } from '../../ci/project';
import { MiniProgramCI } from '../../types';
type IStat = MiniProgramCI.IStat;
export declare class ProjectWithMockBuffer extends Project {
    private mockBuffer;
    setMockFileCache(mockedProject: object): void;
    private __dirSet;
    private __fileSet;
    private _getTargetPath;
    getFile(prefix: string, filePath: string): Buffer;
    stat(prefix: string, filePath: string): IStat | undefined;
}
export {};

/// <reference types="node" />
import { CACHE_KEY } from '../../utils/cache';
import { MiniProgramCI, IProject, PageJSON } from '../../types';
type IDevtoolProject = IProject & {
    onFileChange?: any;
};
export declare class ReactiveProject implements MiniProgramCI.IProject {
    project: IProject;
    private fileBoxs;
    private statBoxs;
    private attrBox;
    private appidBox;
    private typeBox;
    private miniprogramRootBox;
    private pluginRootBox;
    private resetFileChangeListener;
    constructor(project: IDevtoolProject);
    release(): void;
    attr(): Promise<MiniProgramCI.IProjectAttr>;
    getFileList(prefix: string, extName: string): string[];
    getFilesAndDirs(): {
        files: string[];
        dirs: string[];
    };
    getExtAppid(): Promise<string | void>;
    updateFiles(): void;
    updateProject(): Promise<unknown>;
    private onFileChange;
    getFile(prefix: string, filePath: string): Buffer;
    stat(prefix: string, filePath: string): MiniProgramCI.IStat | undefined;
    attrSync(): MiniProgramCI.IProjectAttr;
    get appid(): string;
    get type(): MiniProgramCI.ProjectType;
    get nameMappingFromDevtools(): MiniProgramCI.IStringKeyMap<string> | undefined;
    get projectPath(): string;
    get privateKey(): string;
    get miniprogramRoot(): string;
    set miniprogramRoot(val: string);
    get pluginRoot(): string;
    set pluginRoot(val: string);
    private getTargetPath;
}
export declare function tryToGetReactiveProject(project: IDevtoolProject): ReactiveProject;
export declare class ReactiveJSONCompiler {
    static originGetPageJSON: any;
    static originCheckPageJSON: any;
    private pageComputeds;
    private jsonComputeds;
    private project;
    constructor(project: ReactiveProject);
    release(): void;
    registerOrGet<A extends any[], T>(cacheKey: CACHE_KEY, compileFunc: (project: ReactiveProject, ...args: A) => T, ...args: A): T;
    static setOriginGetPageJSON(originGetPageJSON: Function): void;
    static setOriginCheckPageJSON(originCheckPageJSON: Function): void;
    getPageJSON(type: 'checked' | 'compiled', options: {
        miniprogramRoot: string;
        pagePath: string;
    }): PageJSON.IPageJSON;
}
export declare function tryToGetReactiveJSONCompiler(project: ReactiveProject): ReactiveJSONCompiler;
export declare function wrapCompileJSONFunc<A extends any[], T>(cacheKey: CACHE_KEY, func: (project: ReactiveProject, ...args: A) => T): (project: IDevtoolProject | ReactiveProject, ...args: A) => T;
export declare function cleanReactiveCache(): void;
export {};

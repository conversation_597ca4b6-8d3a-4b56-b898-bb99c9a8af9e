import { ReactiveProject } from './reactiveCache';
import { AppJ<PERSON><PERSON>, IProject, PageJSON, ThemeJSON } from '../../types';
export declare const getThemeLocation: (project: (import("../../types").MiniProgramCI.IProject & {
    onFileChange?: any;
}) | ReactiveProject) => string | null;
export declare function getPluginThemeLocation(project: IProject): Promise<string | null>;
interface ITheme {
    themeLocation: string;
    isPlugin?: boolean;
}
export declare function checkThemeJSON(project: IProject, options: ITheme): ThemeJSON.IThemeJSON;
export declare function mergeThemeJSONToAppJSON(themeJson: Readonly<ThemeJSON.IThemeJSON>, appJson: Readonly<AppJSON.IAppJSON>): {
    appJSONLight: AppJSON.IAppJSON;
    appJSONDark: AppJSON.IAppJSON;
};
export declare function mergeThemeJSONToPageJSON(themeJson: ThemeJSON.IThemeJSON, pageJSON: PageJSON.IPageJSON, pageJSONFilePath: string): {
    pageJSONLight: PageJSON.IPageJSON;
    pageJSONDark: PageJSON.IPageJSON;
};
export {};

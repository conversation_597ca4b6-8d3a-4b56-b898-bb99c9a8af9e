import { ReactiveProject } from '../reactiveCache';
import { AppJSON } from '../../../types';
export interface IPageInfo {
    path: string;
    root: string;
    name?: string;
}
export interface IInnerAppJSONCheckOptions {
    project: ReactiveProject;
    miniprogramRoot: string;
    filePath: string;
    inputJSON: AppJSON.IAppJSON;
    mode?: 'light' | 'dark';
}
export declare function checkPageExist(options: IInnerAppJSONCheckOptions, page: string, tips: string): void;
export declare function checkWindow(options: IInnerAppJSONCheckOptions): void;
export declare function checkTabbar(options: IInnerAppJSONCheckOptions): void;
export declare function checkWorkers(options: IInnerAppJSONCheckOptions): void;
export declare function checkOpenLocationPagePath(options: IInnerAppJSONCheckOptions): void;
export declare const checkMainPkgPages: (options: IInnerAppJSONCheckOptions) => void;
export declare function checkSitemapLocation(options: IInnerAppJSONCheckOptions): void;
export declare function checkSubpackages(options: IInnerAppJSONCheckOptions): void;
export declare function checkPlugins(options: IInnerAppJSONCheckOptions): void;
export declare function checkNavigateToMiniProgramAppIdList(options: IInnerAppJSONCheckOptions): void;
export declare function checkFunctionalPages(options: IInnerAppJSONCheckOptions): void;
export declare function checkPreloadRule(options: IInnerAppJSONCheckOptions, allPages: IPageInfo[]): void;
export declare function checkEntryPagePath(options: IInnerAppJSONCheckOptions, allPages: IPageInfo[]): void;
export declare function checkTabbarPage(options: IInnerAppJSONCheckOptions): void;
export declare function checkMainPkgPageIsInSubpkg(options: IInnerAppJSONCheckOptions): void;
export declare function checkMainPkgPluginIsInSubPkg(options: IInnerAppJSONCheckOptions): void;
export declare function checkComponentPath(options: IInnerAppJSONCheckOptions): void;
export declare function checkEntranceDeclare(options: IInnerAppJSONCheckOptions): void;
interface IVariableDeclareProperty {
    property: string;
    value: string;
}
export declare function getAppJSONVariableDecalearProperty(appJSON: AppJSON.IAppJSON): Array<IVariableDeclareProperty>;
export declare function checkOpenDataContext(options: IInnerAppJSONCheckOptions, appJSON: AppJSON.IAppJSON): void;
export declare function checkRenderer(options: IInnerAppJSONCheckOptions): void;
export declare function checkResolveAlias(options: IInnerAppJSONCheckOptions): void;
export declare function checkRequiredPrivateInfos(options: IInnerAppJSONCheckOptions): void;
export {};

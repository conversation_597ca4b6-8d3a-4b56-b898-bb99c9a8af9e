import { AppJ<PERSON><PERSON>, MiniProgramCI } from '../../../types';
import { ReactiveProject } from '../reactiveCache';
import { IInnerAppJSONCheckOptions } from './checkAppFields';
export declare function checkAppJSON(options: IInnerAppJSONCheckOptions): AppJSON.IAppJSON;
export declare const getRawAppJSON: (project: (MiniProgramCI.IProject & {
    onFileChange?: any;
}) | ReactiveProject) => AppJSON.IAppJSON;
export declare const getAppJSON: (project: (MiniProgramCI.IProject & {
    onFileChange?: any;
}) | ReactiveProject) => AppJSON.IAppJSON;

import { ReactiveProject } from './reactiveCache';
import { AppJSON, IGameJSON, IProject, MiniProgramCI, PageJSON } from '../../types';
export declare function checkJSONFormat(code: string, filePath: string): MiniProgramCI.IAnyObject;
export declare function checkPagePathIsInSubPackage(appJSON: AppJSON.IAppJSON, pathName: string): undefined | AppJSON.ISubpackageItem;
export declare function checkPagePathIsInIndependentSubpackage(appJSON: AppJSON.IAppJSON, pathName: string): undefined | AppJSON.ISubpackageItem;
export declare function checkFilePathIsInIndependentSubpackage(appOrGameJSON: AppJSON.IAppJSON | IGameJSON, pathName: string): string | undefined;
export declare const getUseExtendLib: (project: IProject, pagePath: string) => Array<string>;
export declare const checkComponentPath: (options: {
    project: IProject | ReactiveProject;
    root: string;
    relativePath: string;
    inputJSON: MiniProgramCI.IAnyObject;
}) => void;
interface IVariableDeclareProperty {
    property: string;
    value: string;
}
export declare function getPageJSONVariableDecalearProperty(pageJSON: PageJSON.IPageJSON): Array<IVariableDeclareProperty>;
export declare function transValidateResult(validateResult: MiniProgramCI.IValidateResult): string;
export {};

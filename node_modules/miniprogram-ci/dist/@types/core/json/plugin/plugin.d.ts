import { ReactiveProject } from '../reactiveCache';
import { IProject, MiniProgramCI, PluginJSON } from '../../../types';
interface ICheckOptions {
    project: ReactiveProject;
    filePath: string;
    root: string;
}
export declare function checkComponentPath(options: ICheckOptions, inputJSON: PluginJSON.IPluginJSON): void;
export declare function checkWorkers(options: ICheckOptions, inputJSON: PluginJSON.IPluginJSON): void;
export declare const getDevPluginJSON: (project: (MiniProgramCI.IProject & {
    onFileChange?: any;
}) | ReactiveProject, localPath?: any) => PluginJSON.IPluginJSON;
export declare const getGameLocalPluginJSON: (project: IProject, localPath: string) => Promise<{}>;
export {};

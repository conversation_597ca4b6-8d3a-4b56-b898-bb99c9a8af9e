type IValidType = 'number' | 'string' | 'object' | 'array' | 'function' | 'boolean' | 'undefined' | 'null' | 'regexp' | 'ignore';
declare class T {
    value: any;
    private type;
    private required;
    private valueType;
    constructor(type: IValidType, required?: boolean, value?: any);
    static invalidKeys(validate: any, obj: any): undefined | string[];
    check(obj: any): void;
}
export = T;

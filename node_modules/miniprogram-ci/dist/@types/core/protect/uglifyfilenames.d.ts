/// <reference types="node" />
import { IGameJSON, IProject, MiniProgramCI } from '../../types';
interface INameMapping {
    [origin: string]: string;
}
export declare const getNameMapping: (project: IProject, root: string) => Promise<INameMapping>;
export declare const getGameNameMapping: (project: IProject, gameJSON: IGameJSON, fileList: string[]) => Promise<INameMapping>;
export declare function uglifyFileNames(project: IProject, prevResult: MiniProgramCI.IStringKeyMap<string | Buffer>, nameMapping?: INameMapping): Promise<MiniProgramCI.IAnyObject>;
export {};

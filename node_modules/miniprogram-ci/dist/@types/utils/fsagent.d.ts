/// <reference types="node" />
export interface IFSAgent {
    stat(path: string): Promise<{
        isFile(): boolean;
        isDirectory(): boolean;
        mode: number;
        size: number;
        atimeMs: number;
        mtimeMs: number;
        ctimeMs: number;
        birthtimeMs: number;
    }>;
    readFile(path: string): Promise<Buffer>;
    exists(path: string): Promise<boolean>;
    writeFile(path: string, data: any, options: string | null): Promise<void>;
    readdir(path: string): Promise<string[]>;
}
export declare class FSAgent implements IFSAgent {
    private _agent;
    constructor(agent?: IFSAgent);
    setAgent(agent: IFSAgent): void;
    stat(target: string): Promise<{
        isFile(): boolean;
        isDirectory(): boolean;
        mode: number;
        size: number;
        atimeMs: number;
        mtimeMs: number;
        ctimeMs: number;
        birthtimeMs: number;
    }>;
    exists(target: string): Promise<boolean>;
    readFile(target: string): Promise<Buffer>;
    writeFile(target: string, data: any, options?: string | null): Promise<void>;
    readdir(target: string): Promise<string[]>;
}

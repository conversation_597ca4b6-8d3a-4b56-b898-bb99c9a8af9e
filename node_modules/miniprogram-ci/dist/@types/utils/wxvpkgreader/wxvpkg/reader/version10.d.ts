/// <reference types="node" />
interface IFileInfo {
    name: string;
    encType: number;
    mode: number;
    offset: number;
    length: number;
}
interface IFileMap {
    [path: string]: IFileInfo;
}
declare class WxvpkgReader {
    fileMap: IFileMap;
    private dirMap;
    private fd;
    private cache;
    private _close;
    private _stat;
    private _path;
    constructor(path: string);
    private cacheDirName;
    private getFileMapByJSON;
    private getFileMap;
    readSync: (start: number, length: number) => Buffer;
    getFile(filePath: string): Buffer;
    exists(filePath: string): boolean;
    stat(filePath: string): {
        isFile: boolean;
        size: number;
        mode: number;
        atime: Date;
        birthtime: Date;
        mtime: Date;
        ctime: Date;
    };
    close(): void;
}
export = WxvpkgReader;

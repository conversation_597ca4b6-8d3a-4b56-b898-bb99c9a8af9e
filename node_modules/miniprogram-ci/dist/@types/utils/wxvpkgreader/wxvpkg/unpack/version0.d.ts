/// <reference types="node" />
interface IFileInfo {
    name: string;
    offset: number;
    length: number;
}
interface IFileCache {
    [path: string]: Buffer;
}
export declare function getFileInfoLength(buffer: Buffer): number;
export declare function getFileContentLength(buffer: Buffer): number;
export declare function getFileCount(buffer: Buffer): number;
export declare function getFileInfo(buffer: Buffer): IFileInfo[];
export default function unpack(buffer: Buffer, dist?: string): IFileCache;
export {};

/// <reference types="node" />
import { RawSourceMap } from 'source-map';
import { AppJSON } from '../types';
export declare function normalizePath(pathName?: string): string;
export declare function getType(object: any): string;
export declare const bufferToUtf8String: (buf: Buffer) => string | undefined;
export declare const formatJSONParseErr: (event: {
    filePath: string;
    data: string;
    error: string;
}) => string;
export declare const isHexColor: (hex: string) => boolean;
export declare function rmSync(filePath: string): void;
export declare function mkdirSync(dist: string): void;
export declare function escapeQuot(str: string, type?: string): string;
export declare function trailing(str: string, char: string, reversed?: boolean): string;
export declare function leading(str: string, char: string, reversed?: boolean): string;
export declare const isFileIgnored: (file: string, ignoreRule: any) => any;
export declare const isFileIncluded: (file: string, ignoreRule: any) => any;
export declare function formatSourceMap(map: string | RawSourceMap): string | undefined;
export declare function generateMD5(buffer: Buffer | string): string;
export declare const formatNumber: (n: number) => string;
export declare const formatTime: (date: Date) => string;
export declare const getWorkersPath: (workers: Exclude<AppJSON.IAppJSON['workers'], undefined>) => string;
export declare const pathRelative: (left: string, right: string) => string;

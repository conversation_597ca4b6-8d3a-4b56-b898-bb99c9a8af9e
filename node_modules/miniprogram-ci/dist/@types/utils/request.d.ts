import { InterruptiblePromise } from './interruptibletask';
import { MiniProgramCI } from '../types';
interface IRequestOptions {
    url: string;
    method: 'post' | 'get';
    formData?: MiniProgramCI.IAnyObject;
    body?: any;
    gzip?: boolean;
    project?: MiniProgramCI.IProject;
    needRandom?: number;
    needAppID?: number;
    needToken?: number;
    headers?: MiniProgramCI.IAnyObject;
}
export declare function request<T>(opt: IRequestOptions): InterruptiblePromise<T>;
export declare function setCiProxy(proxy: string): void;
export declare function getCiProxy(): string;
export declare function initGlobalProxy(): void;
export {};

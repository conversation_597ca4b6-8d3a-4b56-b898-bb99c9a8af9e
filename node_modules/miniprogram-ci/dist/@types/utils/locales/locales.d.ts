declare const _default: {
    readonly config: {
        GENERATE_LOCAL_SIGNATURE_FAIL: import("./fomatable_string").FormatableString;
        PARAM_ERROR: import("./fomatable_string").FormatableString;
        SHOULD_NOT_BE_EMPTY: import("./fomatable_string").FormatableString;
        JSON_CONTENT_SHOULD_BE: import("./fomatable_string").FormatableString;
        SHOULD_AT_LEAST_ONE_ITEM: import("./fomatable_string").FormatableString;
        SHOULD_MATCH: import("./fomatable_string").FormatableString;
        SHOULD_EQUAL: import("./fomatable_string").FormatableString;
        EXT_SHOULD_BE_ERROR: import("./fomatable_string").FormatableString;
        OR: import("./fomatable_string").FormatableString;
        CORRESPONDING_FILE_NOT_FOUND: import("./fomatable_string").FormatableString;
        JSON_SHOULD_NOT_START_WITH: import("./fomatable_string").FormatableString;
        JSON_SHOULD_NOT_CONTAIN: import("./fomatable_string").FormatableString;
        NOT_FOUND: import("./fomatable_string").FormatableString;
        NOT_FOUND_IN_ROOT_DIR: import("./fomatable_string").FormatableString;
        MINIPROGRAM_APP_JSON_NOT_FOUND: import("./fomatable_string").FormatableString;
        PLUGIN_JSON_NOT_FOUND: import("./fomatable_string").FormatableString;
        PLUGIN_PATH_SAME_WITH_MINIPROGRAM: import("./fomatable_string").FormatableString;
        FILE_NOT_FOUND: import("./fomatable_string").FormatableString;
        JSON_PARSE_ERROR: import("./fomatable_string").FormatableString;
        ENTRANCE_NOT_FOUND: import("./fomatable_string").FormatableString;
        JSON_PAGE_FILE_NOT_EXISTS: import("./fomatable_string").FormatableString;
        SHOULD_NOT_IN: import("./fomatable_string").FormatableString;
        JSON_CUSTOM_COMPILE_PATH_NOT_EXISTS_TITLE: import("./fomatable_string").FormatableString;
        JSON_CUSTOM_COMPILE_PATH_NOT_EXISTS: import("./fomatable_string").FormatableString;
        JSON_ENTRY_PAGE_PATH_NOT_FOUND: import("./fomatable_string").FormatableString;
        JSON_TABBAR_AT_LEAST: import("./fomatable_string").FormatableString;
        JSON_TABBAR_AT_MOST: import("./fomatable_string").FormatableString;
        JSON_TABBAR_PATH_EMPTY: import("./fomatable_string").FormatableString;
        JSON_TABBAR_PATH_SAME_WITH_OTHER: import("./fomatable_string").FormatableString;
        JSON_TABBAR_ICON_MAX_SIZE: import("./fomatable_string").FormatableString;
        JSON_TABBAR_ICON_EXT: import("./fomatable_string").FormatableString;
        JSON_CONTENT_SHOULD_NOT_BE: import("./fomatable_string").FormatableString;
        JSON_RESOLVE_ALIAS_ILLEGAL: import("./fomatable_string").FormatableString;
        JSON_RESOLVE_ALIAS_INCLUDE_STAR: import("./fomatable_string").FormatableString;
        JSON_RESOLVE_ALIAS_SHOULD_NOT_START_WITH: import("./fomatable_string").FormatableString;
        JSON_REQUIRED_PRIVATE_INFOS_MUTUALLY_EXCLUSIVE: import("./fomatable_string").FormatableString;
        APP_JSON_SHOULD_SET_LAZYCODELOADING: import("./fomatable_string").FormatableString;
        PAGE_JSON_SHOULD_SET_DISABLESCROLL_TRUE: import("./fomatable_string").FormatableString;
        PAGE_JSON_SHOULD_SET_NAVIGATIONSTYLE_CUSTOM: import("./fomatable_string").FormatableString;
        CONTENT_EXIST: import("./fomatable_string").FormatableString;
        JSON_CONTENT_EXISTED: import("./fomatable_string").FormatableString;
        JSON_CONTENT_NOT_FOUND: import("./fomatable_string").FormatableString;
        LACK_OF_FILE: import("./fomatable_string").FormatableString;
        JSON_PAGES_REPEAT: import("./fomatable_string").FormatableString;
        JSON_CONTENT_REPEAT: import("./fomatable_string").FormatableString;
        EXT_JSON_INVALID: import("./fomatable_string").FormatableString;
        GAME_EXT_JSON_INVALID: import("./fomatable_string").FormatableString;
        EXT_APPID_SHOULD_NOT_BE_EMPTY: import("./fomatable_string").FormatableString;
        FILE_NOT_UTF8: import("./fomatable_string").FormatableString;
        INVALID: import("./fomatable_string").FormatableString;
        DIRECTORY: import("./fomatable_string").FormatableString;
        EXCEED_LIMIT: import("./fomatable_string").FormatableString;
        PLEASE_CHOOSE_PLUGIN_MODE: import("./fomatable_string").FormatableString;
        TRIPLE_NUMBER_DOT: import("./fomatable_string").FormatableString;
        PAGE_PATH: import("./fomatable_string").FormatableString;
        PLUGINS_SAME_ALIAS: import("./fomatable_string").FormatableString;
        SAME_ITEM: import("./fomatable_string").FormatableString;
        ALREADY_EXISTS: import("./fomatable_string").FormatableString;
        SAME_KEY_PAGE_PUBLICCOMPONENTS: import("./fomatable_string").FormatableString;
        GAME_DEV_PLUGIN_SHOULD_NOT_USE_LOCAL_PATH: import("./fomatable_string").FormatableString;
        GAME_PLUGIN_SIGNATURE_MD5_NOT_MATCH_CONTENT: import("./fomatable_string").FormatableString;
        FILE: import("./fomatable_string").FormatableString;
        PROCESSING: import("./fomatable_string").FormatableString;
        DONE: import("./fomatable_string").FormatableString;
        UPLOAD: import("./fomatable_string").FormatableString;
        SUCCESS: import("./fomatable_string").FormatableString;
        PROJECT_TYPE_ERROR: import("./fomatable_string").FormatableString;
        MINI_PROGRAM: import("./fomatable_string").FormatableString;
        MINI_GAME: import("./fomatable_string").FormatableString;
        NOT_ALLOWED_REQUIRE_VAR: import("./fomatable_string").FormatableString;
        NOT_ALLOWED_REQUIRE_ASSIGN: import("./fomatable_string").FormatableString;
        NOT_FOUND_NPM_ENTRY: import("./fomatable_string").FormatableString;
        NOT_FOUND_NODE_MODULES: import("./fomatable_string").FormatableString;
        JSON_ENTRANCE_DECLARE_PATH_ERR: import("./fomatable_string").FormatableString;
        JSON_ENTRANCE_DECLARE_PATH_EMPTY: import("./fomatable_string").FormatableString;
        COULD_NOT_USE_CODE_PROTECT: import("./fomatable_string").FormatableString;
        SUMMER_COMPILING_MODULE: import("./fomatable_string").FormatableString;
        SUMMER_COMPILE_JSON: import("./fomatable_string").FormatableString;
        SUMMER_OPTIMIZE_CODE: import("./fomatable_string").FormatableString;
        SUMMER_PACK_FILES: import("./fomatable_string").FormatableString;
        SUMMER_COMPRESS_PACK: import("./fomatable_string").FormatableString;
        SUMMER_SEAL_PACK: import("./fomatable_string").FormatableString;
        SUMMER_APPEND_BABEL_HELPERS: import("./fomatable_string").FormatableString;
        SUMMER_COMPILE_PAGE_JSON: import("./fomatable_string").FormatableString;
        SUMMER_COMPILE_PLUGIN_PAGE_JSON: import("./fomatable_string").FormatableString;
        SUMMER_COMPILE: import("./fomatable_string").FormatableString;
        SUMMER_COMPILE_MINIPROGRAM: import("./fomatable_string").FormatableString;
        SUMMER_COMPILE_PLUGIN: import("./fomatable_string").FormatableString;
    };
    setLocale: (tag: string) => void;
    getLocale: () => string;
};
export default _default;

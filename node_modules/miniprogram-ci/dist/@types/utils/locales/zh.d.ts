import { FormatableString } from './fomatable_string';
declare const _default: {
    GENERATE_LOCAL_SIGNATURE_FAIL: FormatableString;
    PARAM_ERROR: FormatableString;
    SHOULD_NOT_BE_EMPTY: FormatableString;
    JSON_CONTENT_SHOULD_BE: FormatableString;
    SHOULD_AT_LEAST_ONE_ITEM: FormatableString;
    SHOULD_MATCH: FormatableString;
    SHOULD_EQUAL: FormatableString;
    EXT_SHOULD_BE_ERROR: FormatableString;
    OR: FormatableString;
    CORRESPONDING_FILE_NOT_FOUND: FormatableString;
    JSON_SHOULD_NOT_START_WITH: FormatableString;
    JSON_SHOULD_NOT_CONTAIN: FormatableString;
    NOT_FOUND: FormatableString;
    NOT_FOUND_IN_ROOT_DIR: FormatableString;
    MINIPROGRAM_APP_JSON_NOT_FOUND: FormatableString;
    PLUGIN_JSON_NOT_FOUND: FormatableString;
    PLUGIN_PATH_SAME_WITH_MINIPROGRAM: FormatableString;
    FILE_NOT_FOUND: FormatableString;
    JSON_PARSE_ERROR: FormatableString;
    ENTRANCE_NOT_FOUND: FormatableString;
    JSON_PAGE_FILE_NOT_EXISTS: FormatableString;
    SHOULD_NOT_IN: FormatableString;
    JSON_CUSTOM_COMPILE_PATH_NOT_EXISTS_TITLE: FormatableString;
    JSON_CUSTOM_COMPILE_PATH_NOT_EXISTS: FormatableString;
    JSON_ENTRY_PAGE_PATH_NOT_FOUND: FormatableString;
    JSON_TABBAR_AT_LEAST: FormatableString;
    JSON_TABBAR_AT_MOST: FormatableString;
    JSON_TABBAR_PATH_EMPTY: FormatableString;
    JSON_TABBAR_PATH_SAME_WITH_OTHER: FormatableString;
    JSON_TABBAR_ICON_MAX_SIZE: FormatableString;
    JSON_TABBAR_ICON_EXT: FormatableString;
    JSON_CONTENT_SHOULD_NOT_BE: FormatableString;
    JSON_RESOLVE_ALIAS_ILLEGAL: FormatableString;
    JSON_RESOLVE_ALIAS_INCLUDE_STAR: FormatableString;
    JSON_RESOLVE_ALIAS_SHOULD_NOT_START_WITH: FormatableString;
    JSON_REQUIRED_PRIVATE_INFOS_MUTUALLY_EXCLUSIVE: FormatableString;
    APP_JSON_SHOULD_SET_LAZYCODELOADING: FormatableString;
    PAGE_JSON_SHOULD_SET_DISABLESCROLL_TRUE: FormatableString;
    PAGE_JSON_SHOULD_SET_NAVIGATIONSTYLE_CUSTOM: FormatableString;
    CONTENT_EXIST: FormatableString;
    JSON_CONTENT_EXISTED: FormatableString;
    JSON_CONTENT_NOT_FOUND: FormatableString;
    LACK_OF_FILE: FormatableString;
    JSON_PAGES_REPEAT: FormatableString;
    JSON_CONTENT_REPEAT: FormatableString;
    EXT_JSON_INVALID: FormatableString;
    GAME_EXT_JSON_INVALID: FormatableString;
    EXT_APPID_SHOULD_NOT_BE_EMPTY: FormatableString;
    FILE_NOT_UTF8: FormatableString;
    INVALID: FormatableString;
    DIRECTORY: FormatableString;
    EXCEED_LIMIT: FormatableString;
    PLEASE_CHOOSE_PLUGIN_MODE: FormatableString;
    TRIPLE_NUMBER_DOT: FormatableString;
    PAGE_PATH: FormatableString;
    PLUGINS_SAME_ALIAS: FormatableString;
    SAME_ITEM: FormatableString;
    ALREADY_EXISTS: FormatableString;
    SAME_KEY_PAGE_PUBLICCOMPONENTS: FormatableString;
    GAME_DEV_PLUGIN_SHOULD_NOT_USE_LOCAL_PATH: FormatableString;
    GAME_PLUGIN_SIGNATURE_MD5_NOT_MATCH_CONTENT: FormatableString;
    FILE: FormatableString;
    PROCESSING: FormatableString;
    DONE: FormatableString;
    UPLOAD: FormatableString;
    SUCCESS: FormatableString;
    PROJECT_TYPE_ERROR: FormatableString;
    MINI_PROGRAM: FormatableString;
    MINI_GAME: FormatableString;
    NOT_ALLOWED_REQUIRE_VAR: FormatableString;
    NOT_ALLOWED_REQUIRE_ASSIGN: FormatableString;
    NOT_FOUND_NPM_ENTRY: FormatableString;
    NOT_FOUND_NODE_MODULES: FormatableString;
    JSON_ENTRANCE_DECLARE_PATH_ERR: FormatableString;
    JSON_ENTRANCE_DECLARE_PATH_EMPTY: FormatableString;
    COULD_NOT_USE_CODE_PROTECT: FormatableString;
    SUMMER_COMPILING_MODULE: FormatableString;
    SUMMER_COMPILE_JSON: FormatableString;
    SUMMER_OPTIMIZE_CODE: FormatableString;
    SUMMER_PACK_FILES: FormatableString;
    SUMMER_COMPRESS_PACK: FormatableString;
    SUMMER_SEAL_PACK: FormatableString;
    SUMMER_APPEND_BABEL_HELPERS: FormatableString;
    SUMMER_COMPILE_PAGE_JSON: FormatableString;
    SUMMER_COMPILE_PLUGIN_PAGE_JSON: FormatableString;
    SUMMER_COMPILE: FormatableString;
    SUMMER_COMPILE_MINIPROGRAM: FormatableString;
    SUMMER_COMPILE_PLUGIN: FormatableString;
};
export default _default;

import { MiniProgramCI } from '../types';
export type InterruptiblePromise<T> = Promise<T> & {
    abort: MiniProgramCI.FN<void>;
};
export declare const AbortEvent = "abort";
export declare abstract class InterruptibleTask {
    protected _aborted: boolean;
    protected _args: any[];
    protected _promise: Promise<any>;
    constructor(...args: any[]);
    then(onResolve: MiniProgramCI.FN<void>, onReject?: MiniProgramCI.FN<void>): Promise<void>;
    catch(onReject: MiniProgramCI.FN<void>): Promise<any>;
    abort(): void;
    abstract run(...args: any[]): Promise<any>;
}

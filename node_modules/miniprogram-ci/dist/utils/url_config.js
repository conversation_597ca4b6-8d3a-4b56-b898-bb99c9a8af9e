!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.tcbTencentCloudUrl=exports.dbTencentCloudUrl=exports.scfTencentCloudUrl=exports.cloudCosUploadURL=exports.get3rdCloudCodeSecret=exports.cloudAPIAgentURL=exports.GET_LATEST_VERSION=exports.UPLOAD_JS_SERVER=exports.GET_ASYNC_RESULT=exports.GET_UPLOAD_TOKEN=exports.GET_CLOUD_API_SIGNATURE=exports.TRANSLATE_FILENAME=exports.GET_DEV_SOURCE_MAP=exports.GET_RAND_STRING=exports.GET_ONLINE_SCHEMA=exports.GET_WHITE_EXT_LIST=exports.TEST_SOURCE_URL=exports.UPLOAD_URL=exports.GET_ATTR_URL=void 0;const Domain="https://servicewechat.com";exports.GET_ATTR_URL=Domain+"/wxa/ci/getattr",exports.UPLOAD_URL=Domain+"/wxa/ci/upload",exports.TEST_SOURCE_URL=Domain+"/wxa/ci/testSourceURL",exports.GET_WHITE_EXT_LIST=Domain+"/wxa/ci/getwhiteextlist",exports.GET_ONLINE_SCHEMA=Domain+"/wxa/ci/getonlineschema",exports.GET_RAND_STRING=Domain+"/wxa/ci/getrandstr",exports.GET_DEV_SOURCE_MAP=Domain+"/wxa/ci/get_dev_sourcemap",exports.TRANSLATE_FILENAME=Domain+"/wxa/ci/translate_filename",exports.GET_CLOUD_API_SIGNATURE=Domain+"/wxa/ci/getqcloudapisignature",exports.GET_UPLOAD_TOKEN=Domain+"/wxa/ci/getuploadtoken",exports.GET_ASYNC_RESULT=Domain+"/wxa/ci/getasyncresult",exports.UPLOAD_JS_SERVER=Domain+"/wxa/ci/uploadjsserver",exports.GET_LATEST_VERSION=Domain+"/wxa/ci/getlatestversion",exports.cloudAPIAgentURL=Domain+"/wxa/ci/cloudapihttpagent",exports.get3rdCloudCodeSecret=Domain+"/wxa/ci/getcloudcodesecret",exports.cloudCosUploadURL=Domain+"/wxa/ci/cloudcosupload",exports.scfTencentCloudUrl="https://scf.tencentcloudapi.com",exports.dbTencentCloudUrl="https://flexdb.tencentcloudapi.com",exports.tcbTencentCloudUrl="https://tcb.tencentcloudapi.com";
}(require("licia/lazyImport")(require), require)
"use strict";const WxvpkgReader=require("../wxvpkgreader/wxvpkgreader"),splitPath=e=>{if("[object String]"!==Object.prototype.toString.call(e))return[!1,"",""];if(".wxvpkg"===e.substr(-7))return[!0,e,""];const t=e.split(".wxvpkg");return 1===t.length?[!1,"",""]:[!0,t.slice(0,t.length-1).join(".wxvpkg")+".wxvpkg",t[t.length-1]]};let ReaderCache={};module.exports={splitPath:splitPath,getReader:e=>{let t=ReaderCache[e];return t||(t=ReaderCache[e]=new WxvpkgReader(e)),t},cleanCache:()=>{for(const e in ReaderCache){ReaderCache[e].close()}ReaderCache={}}};
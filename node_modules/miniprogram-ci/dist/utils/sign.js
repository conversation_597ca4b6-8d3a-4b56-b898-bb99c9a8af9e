!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getSignature=exports.getRandomString=void 0;const tslib_1=require("tslib"),config_1=require("../config"),crypto_1=tslib_1.__importDefault(require("crypto")),request_1=require("./request"),error_1=require("./error"),locales_1=tslib_1.__importDefault(require("./locales/locales")),url_config_1=require("./url_config"),jsonParse_1=require("./jsonParse");async function getRandomString(r){try{const{body:e}=await(0,request_1.request)({url:url_config_1.GET_RAND_STRING,method:"post",body:JSON.stringify({appid:r,clientRand:Math.floor(1e8*Math.random())}),headers:{"content-type":"application/json"}}),t=(0,jsonParse_1.jsonRespParse)(e,url_config_1.GET_RAND_STRING);if(0===t.errCode)return t.data.randomString;throw new Error(`errCode: ${t.errCode}; errMsg: ${t.errMsg}`)}catch(r){throw new error_1.CodeError(r.toString(),config_1.GET_SIGNATURE_RAND_STRING_ERR)}}async function getSignature(r,e){const t={appid:e,rand_str:await getRandomString(e)};try{return crypto_1.default.privateEncrypt({key:r,padding:crypto_1.default.constants.RSA_PKCS1_PADDING},Buffer.from(JSON.stringify(t))).toString("base64")}catch(r){throw new error_1.CodeError(locales_1.default.config.GENERATE_LOCAL_SIGNATURE_FAIL.format(r.toString()),config_1.GENERATE_LOCAL_SIGNATURE_ERR)}}exports.getRandomString=getRandomString,exports.getSignature=getSignature;
}(require("licia/lazyImport")(require), require)
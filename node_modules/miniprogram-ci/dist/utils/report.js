!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.wrapReport=void 0;const config_1=require("../config"),os=require("os"),request_1=require("./request");async function reportMMData(e){(0,request_1.request)({url:"https://servicewechat.com/wxa-dev-logic/clientreportv2",body:JSON.stringify({report_info_list:[e]}),method:"post"})}const compileTypeMap={miniProgram:1,miniProgramPlugin:2,miniGame:3,miniGamePlugin:4},actionMap={preview:1,upload:2,npm:3,cloud:4,getDevSourceMap:5};function parseVersionToInt(e){let t=e.split(".");return parseInt(t[0]+t[1]+t[2])}function getMinifyXStatus(e,t){return"boolean"==typeof t?t?1:0:e?1:0}function reportAction(e,t,o,i,r,n={}){const a=r.appid,p=r.type,s=parseVersionToInt(config_1.CI_VERSION),c="win32"===process.platform?1:"darwin"===process.platform?2:3,l=os.cpus().length,u=Math.floor(os.totalmem()/1024/1024),f=actionMap[e];o=o.replace(/\,/g,";").slice(0,512);reportMMData({log_id:22365,version:0,user_log_list:`0,${a},${s},${c},${l},${u},${compileTypeMap[p]},${f},${t},${o},${i},0,${n.es6?1:0},${n.es7?1:0},${getMinifyXStatus(n.minify,n.minifyJS)},${getMinifyXStatus(n.minify,n.minifyWXML)},${getMinifyXStatus(n.minify,n.minifyWXSS)},${n.codeProtect?1:0},${n.autoPrefixWXSS?1:0}`})}function wrapReport(e,t){return async function(o){let i=o.project,r={};"upload"!==e&&"preview"!==e||(r=o.setting||{});let n=null,a=Date.now();try{return await t.apply(null,arguments)}catch(e){throw n=e,e}finally{try{let t=Date.now()-a,o=n?n.code?n.code:-1:0,p=n&&n.message?n.message:"";reportAction(e,o,p,t,i,r)}catch(e){console.info(e)}}}}exports.wrapReport=wrapReport;
}(require("licia/lazyImport")(require), require)
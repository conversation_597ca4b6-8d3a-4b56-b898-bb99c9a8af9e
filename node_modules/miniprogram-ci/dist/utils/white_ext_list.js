!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getWhiteExtList=exports.GameWhiteList=exports.MiniProgramWhiteList=void 0;const tslib_1=require("tslib"),url_config_1=require("./url_config"),request_1=require("./request"),log_1=tslib_1.__importDefault(require("./log")),jsonParse_1=require("./jsonParse");let cacheResult;async function getWhiteExtList(){if(cacheResult)return cacheResult;try{const{body:t}=await(0,request_1.request)({url:url_config_1.GET_WHITE_EXT_LIST,method:"get"}),e=(0,jsonParse_1.jsonRespParse)(t,url_config_1.GET_WHITE_EXT_LIST);if(0===e.errCode&&e.data)return cacheResult={GameWhiteList:new Set(e.data.game||exports.GameWhiteList),MiniProgramWhiteList:new Set(e.data.miniProgram||exports.MiniProgramWhiteList)},cacheResult}catch(t){log_1.default.error("get white ext list fail "+t)}return{GameWhiteList:new Set(exports.GameWhiteList),MiniProgramWhiteList:new Set(exports.MiniProgramWhiteList)}}exports.MiniProgramWhiteList=[".wxml",".wxss",".wxs",".png",".jpg",".jpeg",".gif",".svg",".js",".json",".cer",".mp3",".aac",".m4a",".mp4",".wav",".ogg",".silk",".wasm",".br",".cur",".ico",".skel",".crt",".cert"],exports.GameWhiteList=[".png",".jpg",".jpeg",".gif",".svg",".js",".json",".cer",".obj",".dae",".fbx",".mtl",".stl",".3ds",".mp3",".pvr",".wav",".plist",".ttf",".fnt",".gz",".ccz",".m4a",".mp4",".bmp",".atlas",".swf",".ani",".part",".proto",".bin",".sk",".mipmaps",".txt",".zip",".ogg",".silk",".dbbin",".dbmv",".etc",".lmat",".lm",".ls",".lh",".lani",".lav",".lsani",".ltc",".csv",".scene",".prefab",".lml",".lmani",".ktx",".dds",".xml",".aac",".pkm",".skel",".cur",".ico",".wasm",".br",".gltf",".glb",".astc",".dat",".tt"],exports.getWhiteExtList=getWhiteExtList;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.initGlobalProxy=exports.getCiProxy=exports.setCiProxy=exports.request=void 0;const tslib_1=require("tslib"),log_1=tslib_1.__importDefault(require("./log")),interruptibletask_1=require("./interruptibletask"),req=require("request"),getGlobalProxySettings=require("get-proxy");class RequestTask extends interruptibletask_1.InterruptibleTask{constructor(e){super(e),this._alreadyRefresh=!1,this._promise.catch(e=>{log_1.default.error(`${this._opt.url} ${e}`)})}static async formateQuery(e){const t=Object.assign({},e),{needRandom:r}=t;delete t.needRandom;const s=(t.url||"").split("?"),o=s[0],i=[];return-1!==r&&i.push("_r="+Math.random()),s[1]&&i.push(s[1]),t.url=`${o}?${i.join("&")}`,t}abort(){this._aborted||(this._aborted=!0,this._realRequest&&"function"==typeof this._realRequest.abort&&this._realRequest.abort())}async request(){if(this._aborted)throw interruptibletask_1.AbortEvent;const e=await RequestTask.formateQuery(this._opt);return RequestTask.requestProxy&&(e.proxy=RequestTask.requestProxy),new Promise((t,r)=>{this._realRequest=req(e,(e,s,o)=>{e?r(e):t({resp:s,body:o})}),this._realRequest.on("abort",()=>{r(interruptibletask_1.AbortEvent)})})}async run(e){return this._opt=Object.assign({},e),await this.request()}}function request(e){return new RequestTask(e)}function setCiProxy(e){RequestTask.requestProxy=e,log_1.default.info("miniprogram-ci is using proxy: "+e)}function getCiProxy(){return RequestTask.requestProxy}function initGlobalProxy(){const e=getCiProxy(),t=`${process.env.no_proxy||""},${process.env.NO_PROXY||""}`.split(",").map(e=>e.trim());if(!e&&!t.includes("servicewechat.com")){const e=getGlobalProxySettings();e&&setCiProxy(e)}}RequestTask.requestProxy="",exports.request=request,exports.setCiProxy=setCiProxy,exports.getCiProxy=getCiProxy,exports.initGlobalProxy=initGlobalProxy;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const fomatable_string_1=require("./fomatable_string"),config={GENERATE_LOCAL_SIGNATURE_FAIL:"生成本地签名失败。通常是key文件编码或者内容有误。错误详情: %s",PARAM_ERROR:'方法："%s" 缺少参数："%s"',SHOULD_NOT_BE_EMPTY:"%s 不能为空",JSON_CONTENT_SHOULD_BE:"%s 字段需为 %s",SHOULD_AT_LEAST_ONE_ITEM:"%s 需至少存在一项",SHOULD_MATCH:"%s 需与 %s 匹配",SHOULD_EQUAL:"%s 需等于 %s",EXT_SHOULD_BE_ERROR:'%s 的拓展名需为 "%s"',OR:"或",CORRESPONDING_FILE_NOT_FOUND:"未找到 %s 对应的 %s 文件",JSON_SHOULD_NOT_START_WITH:"%s 不应该以 '%s' 开头",JSON_SHOULD_NOT_CONTAIN:"%s 不应该包含 %s",NOT_FOUND:"%s 未找到",NOT_FOUND_IN_ROOT_DIR:"在项目根目录未找到 %s ",MINIPROGRAM_APP_JSON_NOT_FOUND:"根据 project.config.json 中 miniprogramRoot 指定的小程序目录 %s，在该目录下未找到 %s。\n如果你不理解 miniprogramRoot 字段的含义，请在 project.config.json 中将 miniprogramRoot 设为空字符串。\n详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html",PLUGIN_JSON_NOT_FOUND:"根据 project.config.json 中 pluginRoot 指定的小程序本地开发插件目录 %s，%s 未找到",PLUGIN_PATH_SAME_WITH_MINIPROGRAM:"project.config.json 中 pluginRoot 指定的小程序本地开发插件目录 %s，与小程序目录 %s 相同，请修改为不同目录",FILE_NOT_FOUND:"未找到 %s 文件，或者文件读取失败",JSON_PARSE_ERROR:"%s 文件解析错误",ENTRANCE_NOT_FOUND:"未找到入口页面\napp.json 中定义的 pages : %s",JSON_PAGE_FILE_NOT_EXISTS:'未找到 %s 中的定义的 %s "%s" 对应的 %s 文件',SHOULD_NOT_IN:"%s 不应该在 %s 中",JSON_CUSTOM_COMPILE_PATH_NOT_EXISTS_TITLE:"app.json 或自定义编译条件错误",JSON_CUSTOM_COMPILE_PATH_NOT_EXISTS:"app.json 中未定义自定义编译中指定的启动页面 %s",JSON_ENTRY_PAGE_PATH_NOT_FOUND:"未在 %s 中找到 %s 定义的入口页面",JSON_TABBAR_AT_LEAST:'["tabBar"]["list"] 需至少包含 %s 项',JSON_TABBAR_AT_MOST:'["tabBar"]["list"] 不能超过 %s 项',JSON_TABBAR_PATH_EMPTY:'["tabBar"]["list"][%s]["pagePath"] 不能为空',JSON_TABBAR_PATH_SAME_WITH_OTHER:'["tabBar"]["list"][%s]["pagePath"] 和 ["tabBar"]["list"][%s]["pagePath"] 相同',JSON_TABBAR_ICON_MAX_SIZE:'["tabBar"]["list"][%s]["%s"] 大小超过 %skb',JSON_TABBAR_ICON_EXT:'["tabBar"]["list"][%s]["%s"] 文件格式错误，仅支持 %s 格式',JSON_CONTENT_SHOULD_NOT_BE:"%s 不能为 %s",JSON_RESOLVE_ALIAS_ILLEGAL:'resolveAlias 配置中 %s 或 %s 不合法，包含连续的 "//"',JSON_RESOLVE_ALIAS_INCLUDE_STAR:'resolveAlias 配置中 %s 或 %s 需要用 "/*" 结尾',JSON_RESOLVE_ALIAS_SHOULD_NOT_START_WITH:'resolveAlias 配置中 %s 不能以 "./" 开头',JSON_REQUIRED_PRIVATE_INFOS_MUTUALLY_EXCLUSIVE:"requiredPrivateInfos %s 与 %s 互斥",APP_JSON_SHOULD_SET_LAZYCODELOADING:'%s 中 "renderer" 设置为 "skyline"，需在 app.json 添加 "lazyCodeLoading": "requiredComponents"',PAGE_JSON_SHOULD_SET_DISABLESCROLL_TRUE:'根据页面或 app.json 的配置，%s 页面 "renderer" 为 "skyline"，需在页面配置中添加 "disableScroll": true',PAGE_JSON_SHOULD_SET_NAVIGATIONSTYLE_CUSTOM:'根据页面或 app.json 的配置，%s 页面 "renderer" 为 "skyline"，需在页面配置中添加 "navigationStyle": custom',CONTENT_EXIST:"%s 已经存在",JSON_CONTENT_EXISTED:"%s 已经存在",JSON_CONTENT_NOT_FOUND:"%s 不存在",LACK_OF_FILE:"缺少文件 %s",JSON_PAGES_REPEAT:"%s 在 %s 中重复",JSON_CONTENT_REPEAT:"%s 不能同时在 %s 中声明",EXT_JSON_INVALID:'%s 不是 3rdMiniProgramAppid, ext.json 无法生效；查看文档: "%s"',GAME_EXT_JSON_INVALID:'%s 不是 3rdMiniGameAppid, ext.json 无法生效；"%s"',EXT_APPID_SHOULD_NOT_BE_EMPTY:"extAppid 不能为空",FILE_NOT_UTF8:"%s 文件不是 UTF-8 格式",INVALID:"无效的 %s",DIRECTORY:"目录",EXCEED_LIMIT:"%s 超过限制 %s",PLEASE_CHOOSE_PLUGIN_MODE:"如果正在开发插件，请选择插件模式",TRIPLE_NUMBER_DOT:"数字.数字.数字",PAGE_PATH:"页面路径",PLUGINS_SAME_ALIAS:"%s 和 %s 的别名相同",SAME_ITEM:'%s 和 %s 的 "%s" 相同',ALREADY_EXISTS:"已存在",SAME_KEY_PAGE_PUBLICCOMPONENTS:'["pages"] 与 ["publicComponents"] 不能存在相同的 key: %s',GAME_DEV_PLUGIN_SHOULD_NOT_USE_LOCAL_PATH:"开发版插件 %s 不能使用 %s 指定本地路径",GAME_PLUGIN_SIGNATURE_MD5_NOT_MATCH_CONTENT:'插件文件 "%s" 的 MD5: "%s" 与其 signature.json 所给定的值: "%s" 不匹配, 因此编译过程已经中断。\n这表示此文件的内容可能已经被修改。\n恢复此文件的原始内容可能可以解决此问题并移除此警告。\n\n要了解更多，可以参考文档。\n',FILE:"文件",PROCESSING:"处理中: %s",DONE:"完成: %s",UPLOAD:"上传",SUCCESS:"成功",PROJECT_TYPE_ERROR:"project.type 是 %s, 但 appid(%s) 是 %s",MINI_PROGRAM:"小程序",MINI_GAME:"小游戏",NOT_ALLOWED_REQUIRE_VAR:"不允许require变量",NOT_ALLOWED_REQUIRE_ASSIGN:"不允许将require函数赋值给其他变量",NOT_FOUND_NPM_ENTRY:"未找到npm包入口文件",NOT_FOUND_NODE_MODULES:"没有找到可以构建的 NPM 包，请确认需要参与构建的 npm 都在 `miniprogramRoot` 目录内，或配置 project.config.json 的 packNpmManually 和 packNpmRelationList 进行构建",JSON_ENTRANCE_DECLARE_PATH_ERR:'["entranceDeclare"]["locationMessage"]["path"] "%s" 需在 pages 数组或分包 pages 数组中',JSON_ENTRANCE_DECLARE_PATH_EMPTY:'["entranceDeclare"]["locationMessage"]["path"] 不能为空',COULD_NOT_USE_CODE_PROTECT:"无法使用代码保护功能",SUMMER_COMPILING_MODULE:"编译 %s",SUMMER_COMPILE_JSON:"编译 JSON 文件",SUMMER_OPTIMIZE_CODE:"优化代码",SUMMER_PACK_FILES:"打包资源文件",SUMMER_COMPRESS_PACK:"压缩代码包",SUMMER_SEAL_PACK:"封装代码包",SUMMER_APPEND_BABEL_HELPERS:"追加 babel helper 文件",SUMMER_COMPILE_PAGE_JSON:"编译 %s 个页面json文件",SUMMER_COMPILE_PLUGIN_PAGE_JSON:"编译插件 %s 个页面json文件",SUMMER_COMPILE:"编译 %s",SUMMER_COMPILE_MINIPROGRAM:"编译打包小程序",SUMMER_COMPILE_PLUGIN:"编译打包插件"},formatConfig={};for(const[_,E]of Object.entries(config))formatConfig[_]=new fomatable_string_1.FormatableString(E);exports.default=formatConfig;
}(require("licia/lazyImport")(require), require)
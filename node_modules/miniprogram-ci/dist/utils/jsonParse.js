!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.jsonRespParse=exports.jsonParse=void 0;const tslib_1=require("tslib"),log_1=tslib_1.__importDefault(require("./log"));function jsonParse(e){try{return JSON.parse(e)}catch(r){throw log_1.default.info("jsonParse error, input string:"),log_1.default.info(e),r}}function jsonRespParse(e,r=""){try{return JSON.parse(e)}catch(s){throw log_1.default.info(`CGI[${r}] response parse error, response body: ${e}`),s}}exports.jsonParse=jsonParse,exports.jsonRespParse=jsonRespParse;
}(require("licia/lazyImport")(require), require)
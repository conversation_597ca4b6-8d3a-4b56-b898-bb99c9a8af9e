!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.pathRelative=exports.getWorkersPath=exports.formatTime=exports.formatNumber=exports.generateMD5=exports.formatSourceMap=exports.isFileIncluded=exports.isFileIgnored=exports.leading=exports.trailing=exports.escapeQuot=exports.mkdirSync=exports.rmSync=exports.isHexColor=exports.formatJSONParseErr=exports.bufferToUtf8String=exports.getType=exports.normalizePath=void 0;const tslib_1=require("tslib"),fs_1=tslib_1.__importDefault(require("fs")),path_1=tslib_1.__importDefault(require("path")),babel_code_frame_1=tslib_1.__importDefault(require("babel-code-frame")),minimatch_1=tslib_1.__importDefault(require("minimatch")),crypto_1=tslib_1.__importDefault(require("crypto")),jsonlint=require("./jsonlint");function normalizePath(e=""){const t=path_1.default.posix.normalize(e.replace(/\\/g,"/"));return!e.startsWith("//")&&!e.startsWith("\\\\")||t.startsWith("//")?t:"/"+t}function getType(e){return Object.prototype.toString.call(e).toLowerCase().split(" ")[1].replace("]","")}exports.normalizePath=normalizePath,exports.getType=getType;const bufferToUtf8String=e=>{const t=e.toString();if(0===Buffer.compare(Buffer.from(t,"utf8"),e))return t};function getErrLine(e,t,r,a){r=r>0?r:1;return`${a}\n${(0,babel_code_frame_1.default)(e,t,r)}`}exports.bufferToUtf8String=bufferToUtf8String;const formatJSONParseErr=e=>{const t=e.data||"";try{jsonlint.parser.parse(t)}catch(r){try{const a=`Expecting ${r.expected}, got ${r.token}`,i=getErrLine(t,r.line,r.loc.first_column,a);return`${e.filePath}\n${i}`}catch(e){}}return`${e.filePath}\n${e.error}`};exports.formatJSONParseErr=formatJSONParseErr;const isHexColor=e=>/^#[a-f\d]{3}$/i.test(e)||/^#[a-f\d]{4}$/i.test(e)||/^#[a-f\d]{6}$/i.test(e)||/^#[a-f\d]{8}$/i.test(e);function rmSync(e){try{if(e=path_1.default.resolve(e),!fs_1.default.existsSync(e))return;if(fs_1.default.lstatSync(e).isDirectory()){const t=fs_1.default.readdirSync(e);if(t.length>0)for(let r=0,a=t.length;r<a;r++)rmSync(path_1.default.posix.join(e,t[r]));fs_1.default.rmdirSync(e)}else fs_1.default.unlinkSync(e)}catch(e){}}function mkdirSync(e){if(e=path_1.default.resolve(e),fs_1.default.existsSync(e)){if(fs_1.default.lstatSync(e).isDirectory())return;fs_1.default.unlinkSync(e)}mkdirSync(path_1.default.dirname(e)),fs_1.default.mkdirSync(e)}function escapeQuot(e,t="`"){return e?"`"===t?e.replace(/\\/g,"\\\\").replace(/`/g,"\\`").replace(/\$/g,"\\$"):'"'===t?e.replace(/\\/g,"\\\\").replace(/\r\n/g,"\n").replace(/\n/g,"\\n").replace(/"/g,'\\"'):"'"===t?e.replace(/\\/g,"\\\\").replace(/\r\n/g,"\n").replace(/\n/g,"\\n").replace(/'/g,"\\'"):e:e}function trailing(e,t,r=!1){return r?e.endsWith(t)?e.slice(0,e.length-1):e:e.endsWith(t)?e:e+t}function leading(e,t,r=!1){return r?e.startsWith(t)?e.slice(1):e:e.startsWith(t)?e:t+e}exports.isHexColor=isHexColor,exports.rmSync=rmSync,exports.mkdirSync=mkdirSync,exports.escapeQuot=escapeQuot,exports.trailing=trailing,exports.leading=leading;const FFSPRGRulesFactory=function(e){let t=null,r=Object.create(null);return function(e,a){if(a.length<1)return!1;if(t===a){if(void 0!==r[e])return r[e]}else t=a,r=Object.create(null);const i=e.replace(/\\/g,"/").toLowerCase();if(!i)return!1;const o=i.slice(i.lastIndexOf("/")+1);let n=!1;for(const e of a){if(!e)continue;const t=e.value.toLowerCase();if("prefix"===e.type)n=o.startsWith(t);else if("suffix"===e.type)n=o.endsWith(t);else if("folder"===e.type)n=leading(i,"/").startsWith(trailing(leading(t,"/"),"/"));else if("file"===e.type)n=leading(i,"/")===leading(t,"/");else if("glob"===e.type)try{n=(0,minimatch_1.default)(i,t)||(0,minimatch_1.default)(leading(i,"/"),t)}catch(e){n=!1}else if("regexp"===e.type)try{n=new RegExp(t,"igm").test(i)||new RegExp(t,"igm").test(leading(i,"/"))}catch(e){n=!1}if(n)break}return r[e]=n,n}};function formatSourceMap(e){if(e){if("string"===getType(e))return e;try{return JSON.stringify(e)}catch(e){}}}function generateMD5(e){const t=crypto_1.default.createHash("md5");return t.update(e),t.digest("hex")}exports.isFileIgnored=FFSPRGRulesFactory(),exports.isFileIncluded=FFSPRGRulesFactory(),exports.formatSourceMap=formatSourceMap,exports.generateMD5=generateMD5;const formatNumber=e=>e>9?""+e:"0"+e;exports.formatNumber=formatNumber;const formatTime=e=>{const t=e.getFullYear(),r=e.getMonth()+1,a=e.getDate(),i=e.getHours(),o=e.getMinutes(),n=e.getSeconds();return`${[t,r,a].map(exports.formatNumber).join("/")} ${[i,o,n].map(exports.formatNumber).join(":")}`};exports.formatTime=formatTime;const getWorkersPath=e=>"string"==typeof e?e:e.path;exports.getWorkersPath=getWorkersPath;const pathRelative=(e,t)=>(e=path_1.default.posix.normalize(e.replace(/\\/g,"/")),t=path_1.default.posix.normalize(t.replace(/\\/g,"/")),path_1.default.posix.relative(e,t));exports.pathRelative=pathRelative;
}(require("licia/lazyImport")(require), require)
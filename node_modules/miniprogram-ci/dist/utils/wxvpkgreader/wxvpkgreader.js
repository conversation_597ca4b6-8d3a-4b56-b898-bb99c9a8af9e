"use strict";const fs=require("fs"),Reader0=require("./wxvpkg/reader/version0"),Reader10=require("./wxvpkg/reader/version10"),utils_1=require("./wxvpkg/utils"),VERSION_BEGIN=1;class WxvpkgReader{constructor(e){this.readSync=(e,s)=>this.instance.readSync(e,s);const s=fs.openSync(e,"r");if(this.version=(0,utils_1.getVersionByFD)(s),0===this.version&&(this.instance=new Reader0(e)),10===this.version&&(this.instance=new Reader10(e)),!this.instance)throw new Error(`${e} unrecognized version: ${this.version}`);fs.close(s,()=>{})}getFile(e){return this.instance.getFile(e)}exists(e){return this.instance.exists(e)}stat(e){return this.instance.stat(e)}close(){this.instance.close()}}module.exports=WxvpkgReader;
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getVersionByFD=exports.getVersion=void 0;const fs=require("fs"),VERSION_BEGIN=1,VERSION_END=5,getVersion=e=>{let r=e.slice(1,5).readInt32BE(0);if(1===r){const t=e.slice(13,14);14!==t[0]&&237!==t[0]||(r=0)}return r};exports.getVersion=getVersion;const getVersionByFD=e=>{const r=Buffer.alloc(4);fs.readSync(e,r,0,4,1);let t=r.readInt32BE(0);if(1===t){const r=Buffer.alloc(1);fs.readSync(e,r,0,1,13),14!==r[0]&&237!==r[0]||(t=0)}return t};exports.getVersionByFD=getVersionByFD;
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getFileInfo=exports.getFileCount=exports.getFileContentLength=exports.getFileInfoLength=void 0;const fs=require("fs"),path=require("path"),mkdir=require("mkdir-p"),SPECIAL_START_BEGIN=0,SPECIAL_START_END=1,VERSION_BEGIN=1,VERSION_END=5,INFO_LENGTH_BEGIN=5,INFO_LENGTH_END=9,FILE_LENGTH_BEGIN=9,FILE_LENGTH_END=13,SPECIAL_END_BEGIN=13,SPECIAL_END_END=14,FILE_COUNT_BEGIN=14,FILE_COUNT_END=18,FILF_INFO_BEGIN=18;function getFileInfoLength(e){return e.slice(5,9).readInt32BE(0)}function getFileContentLength(e){return e.slice(9,13).readInt32BE(0)}function getFileCount(e){return e.slice(14,18).readInt32BE(0)}function getFileInfo(e){const t=getFileInfoLength(e),n=getFileCount(e),o=(e.slice(18,18+t),[]);let r=18;for(let t=0;t<n;t++){const t=e.slice(r,r+4).readInt32BE(0);r+=4;const n=e.slice(r,r+t).toString();r+=t;const i=e.slice(r,r+4).readInt32BE(0);r+=4;const s=e.slice(r,r+4).readInt32BE(0);r+=4,o.push({name:n,offset:i,length:s})}return o}function unpack(e,t){try{const n=getFileInfo(e),o={};for(const r of n){const{name:n,offset:i,length:s}=r;if(o[n]=e.slice(i,i+s),t){const e=path.join(t,n);mkdir.sync(path.dirname(e)),fs.writeFileSync(e,o[n])}}return o}catch(e){throw new Error("unpack wxvpkg catch error "+e)}}exports.getFileInfoLength=getFileInfoLength,exports.getFileContentLength=getFileContentLength,exports.getFileCount=getFileCount,exports.getFileInfo=getFileInfo,exports.default=unpack;
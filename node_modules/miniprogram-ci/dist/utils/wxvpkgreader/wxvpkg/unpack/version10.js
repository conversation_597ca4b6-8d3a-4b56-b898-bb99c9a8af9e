"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getFileInfo=exports.getFileCount=exports.getFileContentLength=exports.getFileIndexLength=exports.getFileInfoLength=void 0;const fs=require("fs"),path=require("path"),mkdir=require("mkdir-p"),SPECIAL_START_BEGIN=0,SPECIAL_START_END=1,VERSION_BEGIN=1,VERSION_END=5,INFO_LENGTH_BEGIN=5,INFO_LENGTH_END=9,INDEX_LENGTH_BEGIN=9,INDEX_LENGTH_END=13,FILE_LENGTH_BEGIN=13,FILE_LENGTH_END=17,SPECIAL_END_BEGIN=17,SPECIAL_END_END=18,FILE_COUNT_BEGIN=18,FILE_COUNT_END=22,FILF_INFO_BEGIN=22;function getFileInfoLength(e){return e.slice(5,9).readInt32BE(0)}function getFileIndexLength(e){return e.slice(9,13).readInt32BE(0)}function getFileContentLength(e){return e.slice(13,17).readInt32BE(0)}function getFileCount(e){return e.slice(18,22).readInt32BE(0)}function getFileInfo(e){const t=getFileCount(e),n=[];let o=22;for(let r=0;r<t;r++){const t=e.slice(o,o+4).readInt32BE(0);o+=4;const r=e.slice(o,o+t).toString();o+=t;const i=e.slice(o,o+1).readIntBE(0,1,!1);o+=1;const I=e.slice(o,o+2).readIntBE(0,2,!1);o+=2;const s=e.slice(o,o+4).readInt32BE(0);o+=4;const E=e.slice(o,o+4).readInt32BE(0);o+=4,n.push({name:r,encType:i,mode:I,offset:s,length:E})}return n}function unpack(e,t){try{const n=getFileInfo(e),o={},r=18+getFileInfoLength(e)+getFileIndexLength(e);for(const i of n){const{name:n,offset:I,length:s,mode:E}=i;if(o[n]=e.slice(I+r,I+r+s),t){const e=path.join(t,n);mkdir.sync(path.dirname(e)),fs.writeFileSync(e,o[n],{mode:E.toString(8)})}}return o}catch(e){throw new Error("unpack wxvpkg catch error "+e)}}exports.getFileInfoLength=getFileInfoLength,exports.getFileIndexLength=getFileIndexLength,exports.getFileContentLength=getFileContentLength,exports.getFileCount=getFileCount,exports.getFileInfo=getFileInfo,exports.default=unpack;
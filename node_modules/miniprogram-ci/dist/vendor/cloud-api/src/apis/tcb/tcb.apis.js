!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.tcbDescribeVoucherApplication=exports.tcbApplyVoucher=exports.tcbDescribeVoucherPlanAvailable=exports.tcbDescribeAllVouchersInfo=exports.tcbDescribeVouchersInfo=exports.tcbDescribeAmountAfterDeduction=exports.tcbDescribeAllVouchersInfoByDeal=exports.tcbDescribeVouchersInfoByDeal=exports.tcbDescribeResourceRecoverJob=exports.tcbResourceRecover=exports.tcbDescribeEnvResourceException=exports.tcbDescribeAuthentification=exports.tcbRevokeInvoice=exports.tcbDescribeInvoiceDetail=exports.tcbDescribeInvoiceList=exports.tcbCreateInvoice=exports.tcbDeleteInvoicePostInfo=exports.tcbModifyInvoicePostInfo=exports.tcbCreateInvoicePostInfo=exports.tcbDescribeInvoicePostInfo=exports.tcbSetInvoiceSubject=exports.tcbDescribeInvoiceSubject=exports.tcbDescribeInvoiceAmount=exports.tcbDescribeNextExpireTime=exports.tcbDescribeBillingInfo=exports.tcbCheckEnvPackageModify=exports.tcbDeleteDeal=exports.tcbCancelDeal=exports.tcbQueryAllDeals=exports.tcbQueryDeals=exports.tcbDescribePayInfo=exports.tcbCreateDeal=exports.tcbInqueryPrice=exports.tcbDescribePackages=exports.tcbCreateEnvAndResource=exports.tcbCheckEnvId=exports.tcbDescribeSafeRule=exports.tcbModifySafeRule=exports.tcbDatabaseMigrateQueryInfo=exports.tcbDatabaseMigrateExport=exports.tcbDatabaseMigrateImport=exports.tcbModifyDatabaseACL=exports.tcbDescribeDatabaseACL=exports.tcbModifyStorageACL=exports.tcbGetStorageACLTask=exports.tcbDescribeCurveData=exports.tcbGetMonitorData=exports.tcbGetStorageACL=exports.tcbGetDbDistribution=exports.tcbGetUsers=void 0,exports.tcbDescribeCloudBaseRunBuildSteps=exports.tcbDescribeCloudBaseRunBuildStages=exports.tcbDescribeCloudBaseGWAPI=exports.tcbModifyCloudBaseRunServerVersion=exports.tcbDescribeCloudBaseRunVersionException=exports.tcbDescribeCloudBaseBuildService=exports.tcbDescribeCloudBaseRunBuildLog=exports.tcbDescribeCloudBaseRunPodList=exports.tcbDeleteCloudBaseRunResource=exports.tcbEstablishCloudBaseRunServer=exports.tcbDescribeCloudBaseRunServerVersion=exports.tcbCreateCloudBaseRunServerVersion=exports.tcbDescribeCloudBaseRunContainerSpec=exports.tcbDescribeCloudBaseRunServer=exports.tcbDescribeCloudBaseRunBuildServer=exports.tcbCreateCloudBaseRunResource=exports.tcbDescribeCloudBaseRunServers=exports.tcbDescribeCloudBaseRunResource=exports.tcbDescribeHostingDomain=exports.tcbDescribeEnvFreeQuota=exports.tcbDescribeAccountInfoByPlatformId=exports.tcbDescribeEnvLimit=exports.tcbDescribeStaticStore=exports.tcbDestroyStaticStore=exports.tcbCreateStaticStore=exports.tcbModifySecurityRule=exports.tcbDescribeSecurityRule=exports.tcbUpdateLoginConfig=exports.tcbCreateLoginConfig=exports.tcbDescribeLoginConfigs=exports.tcbDescribeCDNChainTask=exports.tcbDescribeStorageSafeRule=exports.tcbModifyStorageSafeRule=exports.tcbDescribePostpayFreeQuotas=exports.tcbInqueryPostpayPrice=exports.tcbCreatePostpayPackage=exports.tcbCommonServiceAPI=exports.tcbDescribeRestoreHistory=exports.tcbDescribeChangePay=exports.tcbDescribeDauData=exports.tcbModifyMonitorCondition=exports.tcbDeleteMonitorCondition=exports.tcbCreateMonitorCondition=exports.tcbDescribeMonitorCondition=exports.tcbModifyMonitorPolicy=exports.tcbDeleteMonitorPolicy=exports.tcbCreateMonitorPolicy=exports.tcbDescribeMonitorPolicy=exports.tcbDescribeMonitorResource=exports.tcbDeleteVoucherApplication=void 0,exports.tcbModifyAuditRule=exports.tcbDeleteAuditRule=exports.tcbCreateAuditRules=exports.tcbDescribeCollections=exports.tcbDescribeAuditRule=exports.tcbSearchClsLog=exports.tcbQueryPostpaidPackageDeals=exports.tcbRefundPostpaidPackage=exports.tcbDescribeWxCloudBaseRunSubNets=exports.tcbDescribeWxCloudBaseRunEnvs=exports.tcbModifyEnv=exports.tcbDescribeCloudBaseRunServiceDomain=exports.tcbDescribeSmsRecords=exports.tcbDescribeTcbBalance=exports.tcbDescribeSmsAttrInfo=exports.tcbDescribeSmsQuotas=exports.tcbDescribeQcloudScene=exports.tcbDescribeCloudBaseRunVersionSnapshot=exports.tcbDescribeCloudBaseRunOperationDetails=exports.tcbCreateUpgradeExtensionTask=exports.tcbDescribeExtensions=exports.tcbDescribeExtensionUpgrade=exports.tcbDescribeExtensionTemplates=exports.tcbDescribeExtensionTaskStatus=exports.tcbDescribeExtensionInstalled=exports.tcbCreateUninstallExtensionTask=exports.tcbCreateInstallExtensionTask=exports.tcbUpdateScfConfig=exports.tcbUpdatePostpayQuotaLimit=exports.tcbUpdatePostpayQuotaLimitStatus=exports.tcbDescribePostpayQuotaLimit=exports.tcbDescribeEnvPostpayPackage=exports.tcbOnlineHostingDomain=exports.tcbInqueryPackagePrice=exports.tcbDescribeActivityGoods=exports.tcbCreateActivityDeal=exports.tcbModifyHostingDomain=exports.tcbCheckQualification=exports.tcbDeleteHostingDomain=exports.tcbQueryActivityPrice=exports.tcbDescribePostpayPackageList=exports.tcbCreateHostingDomain=exports.tcbDescribeCloudBaseCodeBranch=exports.tcbDescribeCloudBaseCodeRepos=exports.tcbDeleteCloudBaseRunServer=exports.tcbModifyCloudBaseRunServerFlowConf=exports.tcbRollUpdateCloudBaseRunServerVersion=exports.tcbDeleteCloudBaseRunImageRepo=exports.tcbDeleteCloudBaseRunServerVersion=exports.tcbDescribeCloudBaseRunBuildStepLog=void 0,exports.tcbDescribeExtensionsInstalled=exports.tcbCreateCopyEnvTask=exports.tcbDeleteTriggerConfigs=exports.tcbUpdateTriggerConfig=exports.tcbDescribeTriggerConfigs=exports.tcbCreateTriggerConfigs=exports.tcbDescribeTriggerServiceParameters=exports.tcbCreateSecurityAuditConfig=exports.tcbDeleteSecurityAuditConfig=exports.tcbDescribeSecurityAuditConfig=exports.tcbUnfreezeSecurityAuditRecord=exports.tcbDescribeAuditResults=void 0;const tslib_1=require("tslib"),transactor_1=tslib_1.__importStar(require("../../transaction/transactor")),tcbContracts=tslib_1.__importStar(require("../../transaction/contracts/contracts")),common_1=require("../../utils/common");function tcbGenderToCCUserGender(e){switch(e){case 2:case 1:case 0:return e;default:return-1}}function formatAvatarUrl(e){return(e=e.trim()).startsWith("http://")&&(e=e.replace("http://","https://")),e&&!/\/\d+$/.test(e)&&(e+="/0"),e}async function tcbGetUsers(e){const t={Action:"DescribeUsers",Version:"2018-06-08",EnvId:e.envId,Region:e.region,Keyword:e.keyword,Offset:e.offset,Limit:e.limit,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};try{const e=await(0,transactor_1.default)(tcbContracts.tcbGetUsersContract,t);return{total:e.Total,users:e.Users.map(e=>({openId:e.OpenId,grantUserInfo:e.GrantUserInfo,nickName:e.NickName,country:e.Country,province:e.Province,city:e.City,gender:tcbGenderToCCUserGender(e.Gender),language:e.Language,avatarUrl:formatAvatarUrl(e.AvatarUrl),createTime:(0,common_1.strToDate)(e.CreateTime),updateTime:(0,common_1.strToDate)(e.UpdateTime)})),requestId:e.RequestId}}catch(e){throw e}}async function tcbGetDbDistribution(e){const t={Action:"DescribeDbDistribution",Version:"2018-06-08",EnvId:e.envId,Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};return{collections:((await(0,transactor_1.default)(tcbContracts.tcbDescribeDbDistributionContract,t)).Collections||[]).map(e=>({collectionName:e.CollectionName,docCount:e.DocCount}))}}async function tcbGetStorageACL(e){const t={Action:"DescribeStorageACL",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),Bucket:e.bucket,EnvId:e.envId};return{aclTag:(await(0,transactor_1.default)(tcbContracts.tcbDescribeStorageACLContract,t)).AclTag}}async function tcbGetMonitorData(e){const t={Action:"DescribeMonitorData",Version:"2018-06-08",Region:e.region,MetricName:e.metricName,StartTime:(0,common_1.dateToStr)(e.startTime),EndTime:(0,common_1.dateToStr)(e.endTime),EnvId:e.envId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),ResourceID:e.resourceID,SubresourceID:e.subresourceID},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeMonitorDataContract,t);return{startTime:(0,common_1.strToDate)(o.StartTime),endTime:(0,common_1.strToDate)(o.EndTime),metricName:o.MetricName,period:o.Period,values:o.Values}}async function tcbDescribeCurveData(e){const t={Action:"DescribeCurveData",Version:"2018-06-08",Region:e.region,MetricName:e.metricName,StartTime:(0,common_1.dateToStr)(e.startTime),EndTime:(0,common_1.dateToStr)(e.endTime),EnvId:e.envId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),ResourceID:e.resourceID},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCurveDataContract,t);return{startTime:(0,common_1.strToDate)(o.StartTime),endTime:(0,common_1.strToDate)(o.EndTime),metricName:o.MetricName,period:o.Period,values:o.Values,time:o.Time}}async function tcbGetStorageACLTask(e,t){const o={Action:"DescribeStorageACLTask",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),Bucket:e.bucket,EnvId:e.envId};return{status:(await(0,transactor_1.default)(tcbContracts.tcbDescribeStorageACLTaskContract,o,{isPoll:t})).Status}}async function tcbModifyStorageACL(e){const t={Action:"ModifyStorageACL",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),Bucket:e.bucket,EnvId:e.envId,AclTag:e.aclTag};return await(0,transactor_1.default)(tcbContracts.tcbModifyStorageACLContract,t),{}}async function tcbDescribeDatabaseACL(e){const t={Action:"DescribeDatabaseACL",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),CollectionName:e.collectionName,EnvId:e.envId};return{aclTag:(await(0,transactor_1.default)(tcbContracts.tcbDescribeDatabaseACLContract,t)).AclTag}}async function tcbModifyDatabaseACL(e){const t={Action:"ModifyDatabaseACL",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),CollectionName:e.collectionName,EnvId:e.envId,AclTag:e.aclTag};return await(0,transactor_1.default)(tcbContracts.tcbModifyDatabaseACLContract,t),{}}async function tcbDatabaseMigrateImport(e){const t={Action:"DatabaseMigrateImport",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,CollectionName:e.collectionName,FileType:e.fileType,FilePath:e.filePath,StopOnError:e.stopOnError,ConflictMode:e.conflictMode};return{jobId:(await(0,transactor_1.default)(tcbContracts.tcbDatabaseMigrateImportContract,t)).JobId}}async function tcbDatabaseMigrateExport(e){const t={Action:"DatabaseMigrateExport",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,CollectionName:e.collectionName,FileType:e.fileType,FilePath:e.filePath,Query:e.query,Fields:e.fields,Skip:e.skip,Limit:e.limit,Sort:e.sort};return{jobId:(await(0,transactor_1.default)(tcbContracts.tcbDatabaseMigrateExportContract,t)).JobId}}async function tcbDatabaseMigrateQueryInfo(e,t){const o={Action:"DatabaseMigrateQueryInfo",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,JobId:e.jobId},r=await(0,transactor_1.default)(tcbContracts.tcbDatabaseMigrateQueryInfoContract,o,{isPoll:t});return{status:r.Status,recordSuccess:r.RecordSuccess,recordFail:r.RecordFail,errorMsg:r.ErrorMsg,fileUrl:r.FileUrl}}async function tcbModifySafeRule(e){const t={Action:"ModifySafeRule",Version:"2018-06-08",Region:e.region,EnvId:e.envId,CollectionName:e.collectionName,AclTag:e.aclTag,Rule:e.rule};return await(0,transactor_1.default)(tcbContracts.tcbModifySafeRuleContract,t),{}}async function tcbDescribeSafeRule(e){const t={Action:"DescribeSafeRule",Version:"2018-06-08",Region:e.region,EnvId:e.envId,CollectionName:e.collectionName},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeSafeRuleContract,t);return{rule:o.Rule,aclTag:o.AclTag}}async function tcbCheckEnvId(e){const t={Action:"CheckEnvId",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId},o=await(0,transactor_1.default)(tcbContracts.tcbCheckEnvIdContract,t);return{exist:!0===o.Exist||"string"==typeof o.Exist&&"true"===o.Exist.toLowerCase()}}async function tcbCreateEnvAndResource(e){const t={Action:"CreateEnvAndResource",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,Alias:e.alias,Source:e.source||"miniapp",Channel:e.channel||"ide"};return await(0,transactor_1.default)(tcbContracts.tcbCreateEnvAndResourceContract,t),{}}async function tcbDescribePackages(e){const t={Action:"DescribePackages",Version:"2018-06-08",Region:e.region,PackageId:e.packageId,EnvId:e.envId,Source:e.source,TargetAction:e.targetAction};return{packages:((await(0,transactor_1.default)(tcbContracts.tcbDescribePackagesContract,t)).Packages||[]).map(e=>{let t;try{t=JSON.parse(e.Detail)||{}}catch(e){console.error(e),t={}}return{packageId:e.PackageId||"",name:e.Name||"",desc:e.Desc||"",detail:t,pid:e.Pid,packageType:e.PackageType,isFreePackage:e.IsFreePackage}})}}async function tcbInqueryPrice(e){const t={Action:"InqueryPrice",Version:"2018-06-08",Region:e.region,QueryType:e.queryType,UserClientIp:e.userClientIp,PackageId:e.packageId,EnvId:e.envId,TimeSpan:e.timeSpan,TimeUnit:e.timeUnit,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),Source:e.source},o=await(0,transactor_1.default)(tcbContracts.tcbInqueryPriceContract,t);return{price:isNaN(o.Price)?-1:o.Price}}async function tcbCreateDeal(e){const t={Action:"CreateDeal",Version:"2018-06-08",Region:e.region,DealType:e.dealType,EnvId:e.alias?void 0:e.envId,UserClientIp:e.userClientIp,PackageId:e.packageId,TimeSpan:e.timeSpan,TimeUnit:e.timeUnit,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),ForceToModify:e.forceToModify,Source:e.source,IsAutoRenew:e.isAutoRenew,ChangePay:e.changePay,EnvSource:e.alias?"miniapp":void 0,Alias:e.alias?e.alias:void 0,Channel:e.alias?"ide":void 0},o=await(0,transactor_1.default)(tcbContracts.tcbCreateDealContract,t);return{tranId:o.TranId,envId:o.EnvId,modifyFailReason:(o.ModifyFailReason||[]).map(e=>({resourceName:e.ResourceName||"",indexName:e.IndexName||"",indexShowName:e.IndexShowName||"",indexUseAmount:isNaN(e.IndexUseAmount)?-1:e.IndexUseAmount,indexUseUnit:e.IndexUseUnit||""}))}}async function tcbDescribePayInfo(e){const t={Action:"DescribePayInfo",Version:"2018-06-08",Region:e.region,TranId:e.tranId,UserClientIp:e.userClientIp,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};e.voucherId&&(t.VoucherId=e.voucherId);const o=await(0,transactor_1.default)(tcbContracts.tcbDescribePayInfoContract,t);return{payCode:o.PayCode,appId:o.AppId,isFreeOrRefundDeal:o.IsFreeOrRefundDeal}}async function tcbQueryDeals(e){const t={Action:"QueryDeals",Version:"2018-06-08",Region:e.region,UserClientIp:e.userClientIp,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),TranId:e.tranId,DealStatus:e.dealStatus,Limit:e.limit||100,Offset:e.offset,ReturnPostpayDeal:e.returnPostpayDeal},o=await(0,transactor_1.default)(tcbContracts.tcbQueryDealsContract,t);return{total:o.Total||0,deals:(o.Deals||[]).map(e=>({tranId:e.TranId,dealOwner:e.DealOwner,createTime:(0,common_1.strToDate)(e.CreateTime),packageId:e.PackageId,dealStatus:e.DealStatus,dealCost:e.DealCost,envId:e.EnvId,payTime:(0,common_1.strToDate)(e.PayTime),timeSpan:e.TimeSpan,price:e.Price,payMode:e.PayMode,productName:e.ProductName,timeUnit:e.TimeUnit,refundAmount:e.RefundAmount,dealStatusDes:e.DealStatusDes||"",voucherDecline:e.VoucherDecline||null,hasReturnPayCode:e.HasReturnPayCode||null,source:e.Source||null}))}}async function tcbQueryAllDeals(e,t=[],o=0){const r=e.limit||50,{total:a,deals:n}=await tcbQueryDeals({region:e.region,userClientIp:e.userClientIp,appId:e.appId,tranId:e.tranId,dealStatus:e.dealStatus,returnPostpayDeal:e.returnPostpayDeal,limit:r,offset:o});return n.length&&n.length>=r?tcbQueryAllDeals(e,[...t,...n],o+r):{total:a,deals:[...t,...n]}}async function tcbCancelDeal(e){const t={Action:"CancelDeal",Version:"2018-06-08",Region:e.region,UserClientIp:e.userClientIp,DealOwner:e.dealOwner,TranId:e.tranId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};await(0,transactor_1.default)(tcbContracts.tcbCancelDealContract,t);return{}}async function tcbDeleteDeal(e){const t={Action:"DeleteDeal",Version:"2018-06-08",Region:e.region,UserClientIp:e.userClientIp,DealOwner:e.dealOwner,TranId:e.tranId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};await(0,transactor_1.default)(tcbContracts.tcbDeleteDealContract,t);return{}}async function tcbCheckEnvPackageModify(e){const t={Action:"CheckEnvPackageModify",Version:"2018-06-08",Region:e.region,PackageId:e.packageId,EnvId:e.envId},o=await(0,transactor_1.default)(tcbContracts.tcbCheckEnvPackageModifyContract,t);return{allowToModify:Boolean(o.AllowToModify),forceToModify:Boolean(o.ForceToModify),quotaOverlimitList:(o.QuotaOverlimitList||[]).map(e=>({resourceName:e.ResourceName||"",quotaName:e.QuotaName||"",quotaChName:e.QuotaChName||"",quotaUsaged:isNaN(e.QuotaUsaged)?-1:e.QuotaUsaged,unit:e.Unit||"",comments:e.Comments||""})),invoiceOverlimit:o.InvoiceOverlimit?{invoiceAmount:o.InvoiceOverlimit.InvoiceAmount,isAmountOverlimit:o.InvoiceOverlimit.IsAmountOverlimit,refundAmount:o.InvoiceOverlimit.RefundAmount}:void 0}}async function tcbDescribeBillingInfo(e){const t={Action:"DescribeBillingInfo",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,NeedOrderInfo:e.needOrderInfo};return{envBillingInfoList:((await(0,transactor_1.default)(tcbContracts.tcbDescribeBillingInfoContract,t)).EnvBillingInfoList||[]).map(e=>({envId:e.EnvId,packageId:e.PackageId,isAutoRenew:e.IsAutoRenew,status:e.Status,payMode:e.PayMode,isolatedTime:(0,common_1.strToDate)(e.IsolatedTime),expireTime:(0,common_1.strToDate)(e.ExpireTime),createTime:(0,common_1.strToDate)(e.CreateTime),updateTime:(0,common_1.strToDate)(e.UpdateTime),isAlwaysFree:Boolean(e.IsAlwaysFree),paymentChannel:e.PaymentChannel,orderInfo:e.OrderInfo?{tranId:e.OrderInfo.TranId,packageId:e.OrderInfo.PackageId,tranType:e.OrderInfo.TranType,tranStatus:e.OrderInfo.TranStatus,updateTime:e.OrderInfo.UpdateTime,createTime:e.OrderInfo.CreateTime,payMode:e.OrderInfo.PayMode}:null,freeQuota:e.FreeQuota}))}}async function tcbDescribeNextExpireTime(e){const t={Action:"DescribeNextExpireTime",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,TimeSpan:e.timeSpan,ForceNewBuy:e.forceNewBuy},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeNextExpireTimeContract,t);return{expireTime:(0,common_1.strToDate)(o.ExpireTime)}}async function tcbDescribeInvoiceAmount(e){const t={Action:"DescribeInvoiceAmount",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeInvoiceAmountContract,t);return{invoicedAmount:isNaN(o.InvoicedAmount)?-1:o.InvoicedAmount,availableAmount:isNaN(o.AvailableAmount)?-1:o.AvailableAmount}}async function tcbDescribeInvoiceSubject(e){const t={Action:"DescribeInvoiceSubject",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeInvoiceSubjectContract,t);return{userType:o.UserType,invoiceHead:o.InvoiceHead||"",VATGeneral:o.VATGeneral?{taxPayerType:o.VATGeneral.TaxPayerType,taxPayerNumber:o.VATGeneral.TaxPayerNumber,bankDeposit:o.VATGeneral.BankDeposit,bankAccount:o.VATGeneral.BankAccount,registerAddress:o.VATGeneral.RegisterAddress,registerPhone:o.VATGeneral.RegisterPhone}:null,VATSpecial:o.VATSpecial?{taxPayerNumber:o.VATSpecial.TaxPayerNumber,bankDeposit:o.VATSpecial.BankDeposit,bankAccount:o.VATSpecial.BankAccount,registerAddress:o.VATSpecial.RegisterAddress,registerPhone:o.VATSpecial.RegisterPhone}:null,status:o.Status,statusMessage:o.StatusMessage||""}}async function tcbSetInvoiceSubject(e){const t={Action:"SetInvoiceSubject",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),UserType:e.userType,InvoiceHead:e.invoiceHead,VATGeneral:e.VATGeneral?{TaxPayerType:e.VATGeneral.taxPayerType,TaxPayerNumber:e.VATGeneral.taxPayerNumber,BankDeposit:e.VATGeneral.bankDeposit,BankAccount:e.VATGeneral.bankAccount,RegisterAddress:e.VATGeneral.registerAddress,RegisterPhone:e.VATGeneral.registerPhone}:void 0,VATSpecial:e.VATSpecial?{TaxPayerNumber:e.VATSpecial.taxPayerNumber,BankDeposit:e.VATSpecial.bankDeposit,BankAccount:e.VATSpecial.bankAccount,RegisterAddress:e.VATSpecial.registerAddress,RegisterPhone:e.VATSpecial.registerPhone}:void 0};await(0,transactor_1.default)(tcbContracts.tcbSetInvoiceSubjectContract,t);return{}}async function tcbDescribeInvoicePostInfo(e){const t={Action:"DescribeInvoicePostInfo",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};return{postInfoList:((await(0,transactor_1.default)(tcbContracts.tcbDescribeInvoicePostInfoContract,t)).PostInfoList||[]).map(e=>({postId:e.PostId||"",contact:e.Contact||"",province:e.Province||"",city:e.City||"",address:e.Address||"",postalCode:e.PostalCode||"",cellphone:e.Cellphone||""}))}}async function tcbCreateInvoicePostInfo(e){const t={Action:"CreateInvoicePostInfo",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),Contact:e.contact,Province:e.province,City:e.city,Address:e.address,PostalCode:e.postalCode,Cellphone:e.cellphone};return{postId:(await(0,transactor_1.default)(tcbContracts.tcbCreateInvoicePostInfoContract,t)).PostId||""}}async function tcbModifyInvoicePostInfo(e){const t={Action:"ModifyInvoicePostInfo",Version:"2018-06-08",Region:e.region,PostId:e.postId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),Contact:e.contact,Province:e.province,City:e.city,Address:e.address,PostalCode:e.postalCode,Cellphone:e.cellphone};await(0,transactor_1.default)(tcbContracts.tcbModifyInvoicePostInfoContract,t);return{}}async function tcbDeleteInvoicePostInfo(e){const t={Action:"DeleteInvoicePostInfo",Version:"2018-06-08",Region:e.region,PostId:e.postId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};await(0,transactor_1.default)(tcbContracts.tcbDeleteInvoicePostInfoContract,t);return{}}async function tcbCreateInvoice(e){const t={Action:"CreateInvoice",Version:"2018-06-08",Region:e.region,PostId:e.postId,Amount:e.amount,Remark:e.remark,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};await(0,transactor_1.default)(tcbContracts.tcbCreateInvoiceContract,t);return{}}async function tcbDescribeInvoiceList(e){const t={Action:"DescribeInvoiceList",Version:"2018-06-08",Region:e.region,StartTime:(0,common_1.dateToStr)(e.startTime),EndTime:(0,common_1.dateToStr)(e.endTime),Limit:e.limit,Offset:e.offset,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeInvoiceListContract,t);return{total:o.Total,invoiceList:(o.InvoiceList||[]).map(e=>({invoiceId:e.InvoiceId||"",userType:e.UserType||"",amount:isNaN(e.Amount)?-1:e.Amount,status:e.Status,invoiceTime:(0,common_1.strToDate)(e.InvoiceTime)}))}}async function tcbDescribeInvoiceDetail(e){const t={Action:"DescribeInvoiceDetail",Version:"2018-06-08",Region:e.region,InvoiceId:e.invoiceId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeInvoiceDetailContract,t);return{amount:isNaN(o.Amount)?-1:o.Amount,invoiceNumber:o.InvoiceNumber,userType:o.UserType,invoiceHead:o.InvoiceHead,VATGeneral:o.VATGeneral?{taxPayerType:o.VATGeneral.TaxPayerType,taxPayerNumber:o.VATGeneral.TaxPayerNumber,bankDeposit:o.VATGeneral.BankDeposit,bankAccount:o.VATGeneral.BankAccount,registerAddress:o.VATGeneral.RegisterAddress,registerPhone:o.VATGeneral.RegisterPhone}:null,VATSpecial:o.VATSpecial?{taxPayerNumber:o.VATSpecial.TaxPayerNumber,bankDeposit:o.VATSpecial.BankDeposit,bankAccount:o.VATSpecial.BankAccount,registerAddress:o.VATSpecial.RegisterAddress,registerPhone:o.VATSpecial.RegisterPhone}:null,postInfo:{postId:o.PostInfo.PostId,contact:o.PostInfo.Contact,province:o.PostInfo.Province,city:o.PostInfo.City,address:o.PostInfo.Address,postalCode:o.PostInfo.PostalCode,cellphone:o.PostInfo.Cellphone},status:o.Status,postCompany:o.PostCompany||"",postNumber:o.PostNumber||"",invoiceTime:(0,common_1.strToDate)(o.InvoiceTime),remark:o.Remark||""}}async function tcbRevokeInvoice(e){const t={Action:"RevokeInvoice",Version:"2018-06-08",Region:e.region,InvoiceId:e.invoiceId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};await(0,transactor_1.default)(tcbContracts.tcbRevokeInvoiceContract,t);return{}}async function tcbDescribeAuthentification(e){const t={Action:"DescribeAuthentification",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeAuthentificationContract,t);return{authentificationName:o.AuthentificationName||"",authentificationType:o.AuthentificationType}}async function tcbDescribeEnvResourceException(e){const t={Action:"DescribeEnvResourceException",Version:"2018-06-08",Region:e.region,EnvId:e.envId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeEnvResourceExceptionContract,t);return{storage:o.Storage?o.Storage.map(e=>({bucket:e.Bucket,COSStatus:e.COSStatus,COSRecoverJobId:e.COSRecoverJobId})):null,logService:o.LogService?o.LogService.map(e=>({logsetName:e.LogsetName,status:e.Status,functionUpdateJobId:e.FunctionUpdateJobId})):null}}async function tcbResourceRecover(e){const t={Action:"ResourceRecover",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceTypes:e.resourceTypes};return{results:(await(0,transactor_1.default)(tcbContracts.tcbResourceRecoverContract,t)).Results.map(e=>({result:e.Result,errorMessage:e.ErrorMessage,recoverJobId:e.RecoverJobId}))}}async function tcbDescribeResourceRecoverJob(e){const t={Action:"DescribeResourceRecoverJob",Version:"2018-06-08",Region:e.region,EnvId:e.envId,JobIds:e.jobIds,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeResourceRecoverJobContract,t);return{jobStatusSet:o.JobStatusSet?o.JobStatusSet.map(e=>({jobId:e.JobId,status:e.Status,errorMessage:e.ErrorMessage})):null}}async function tcbDescribeVouchersInfoByDeal(e){const t={Action:"DescribeVouchersInfoByDeal",Version:"2018-06-08",Region:e.region,TranId:e.tranId,Page:e.page,Size:e.size,Source:e.source||"miniapp"};return{vouchers:(await(0,transactor_1.default)(tcbContracts.tcbDescribeVouchersInfoByDealContract,t)).Vouchers.map(e=>({voucherId:e.VoucherId,ownerUin:e.OwnerUin,amount:e.Amount,leftAmount:e.LeftAmount,useDeadLine:e.UseDeadLine,status:e.Status||null,baseAmount:e.BaseAmount,reuse:e.Reuse}))}}async function tcbDescribeAllVouchersInfoByDeal(e,t=[],o=1){const r=e.size||100,{vouchers:a}=await tcbDescribeVouchersInfoByDeal({page:o,size:r,tranId:e.tranId,source:e.source});return a.length&&a.length>=r?tcbDescribeAllVouchersInfoByDeal(e,[...t,...a],o+1):{vouchers:[...t,...a]}}async function tcbDescribeAmountAfterDeduction(e){const t={Action:"DescribeAmountAfterDeduction",Version:"2018-06-08",Region:e.region,VoucherId:e.voucherId,TranId:e.tranId,Source:e.source||"miniapp"};return{amountAfterDeduction:(await(0,transactor_1.default)(tcbContracts.tcbDescribeAmountAfterDeductionContract,t)).AmountAfterDeduction}}async function tcbDescribeVouchersInfo(e){const t={Action:"DescribeVouchersInfo",Version:"2018-06-08",Region:e.region,Page:e.page,Size:e.size,Source:e.source||"miniapp"};return{vouchers:(await(0,transactor_1.default)(tcbContracts.tcbDescribeVouchersInfoContract,t)).Vouchers.map(e=>({voucherId:e.VoucherId,ownerUin:e.OwnerUin,amount:e.Amount,leftAmount:e.LeftAmount,useDeadLine:e.UseDeadLine,status:e.Status||null,baseAmount:e.BaseAmount,reuse:e.Reuse}))}}async function tcbDescribeAllVouchersInfo(e,t=[],o=1){const r=e.size||100,{vouchers:a}=await tcbDescribeVouchersInfo({page:o,size:r,source:e.source});return a.length&&a.length>=r?tcbDescribeAllVouchersInfo(e,[...t,...a],o+1):{vouchers:[...t,...a]}}async function tcbDescribeVoucherPlanAvailable(e){const t={Action:"DescribeVoucherPlanAvailable",Version:"2018-06-08",Region:e.region,ProductName:e.productName,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeVoucherPlanAvailableContract,t);return{voucherPlans:o.VoucherPlans||[],qualified:o.Qualified}}async function tcbApplyVoucher(e){const t={Action:"ApplyVoucher",Version:"2018-06-08",Region:e.region,ProductName:e.productName,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),VoucherPlan:e.voucherPlan};return{applicationId:(await(0,transactor_1.default)(tcbContracts.tcbApplyVoucherContract,t)).ApplicationId}}async function tcbDescribeVoucherApplication(e){const t={Action:"DescribeVoucherApplication",Version:"2018-06-08",Region:e.region,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)(),ProductName:e.productName,ApplicationId:e.applicationId};return{applications:((await(0,transactor_1.default)(tcbContracts.tcbDescribeVoucherApplicationContract,t)).Applications||[]).map(e=>({applicationId:e.ApplicationId,voucherPlan:e.VoucherPlan,status:e.Status,productName:e.ProductName}))}}async function tcbDeleteVoucherApplication(e){const t={Action:"DeleteVoucherApplication",Version:"2018-06-08",Region:e.region,ApplicationId:e.applicationId,WxAppId:e.appId||(0,transactor_1.getDefaultAppID)()};await(0,transactor_1.default)(tcbContracts.tcbDeleteVoucherApplicationContract,t);return{}}async function tcbDescribeMonitorResource(e){const t={Action:"DescribeMonitorResource",Version:"2018-06-08",Region:e.region};return{data:(await(0,transactor_1.default)(tcbContracts.tcbDescribeMonitorResourceContract,t)).Data.map(e=>({name:e.Name,index:e.Index,period:e.Period,enIndex:e.EnIndex,enName:e.EnName,indexUnit:e.IndexUnit,convergence:e.Convergence,convergenceName:e.ConvergenceName,periodNum:e.PeriodNum,periodInfo:[]}))}}async function tcbDescribeMonitorPolicy(e){const t={Action:"DescribeMonitorPolicy",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Source:e.source,DesRule:e.desRule?{ResType:e.desRule.resType,RecGroup:e.desRule.recGroup,RecUser:e.desRule.recUser,Object:e.desRule.object,Name:e.desRule.name,PolicyId:e.desRule.policyId}:e.desRule};return{data:(await(0,transactor_1.default)(tcbContracts.tcbDescribeMonitorPolicyContract,t)).Data.map(e=>({name:e.Name,note:e.Note,convergence:e.Convergence,policyId:e.PolicyId,resType:e.ResType,resName:e.ResName,objects:e.Objects||[],source:e.Source,activeStartTime:e.ActiveStartTime,activeEndTime:e.ActiveEndTime,alertChannels:e.AlertChannels||[],recGroups:e.RecGroups||[],recUsers:e.RecUsers||[],isActive:e.IsActive,isEdit:e.IsEdit}))}}async function tcbCreateMonitorPolicy(e){const t={Action:"CreateMonitorPolicy",Version:"2018-06-08",Region:e.region,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,Name:e.name,Convergence:e.convergence,Note:e.note,ResType:e.resType,ResName:e.resName,Objects:e.objects,Source:e.source,ActiveStartTime:e.activeStartTime,ActiveEndTime:e.activeEndTime,AlertChannels:e.alertChannels,RecGroups:e.recGroups,RecUsers:e.recUsers,IsActive:e.isActive};return{id:(await(0,transactor_1.default)(tcbContracts.tcbCreateMonitorPolicyContract,t)).Id}}async function tcbDeleteMonitorPolicy(e){const t={Action:"DeleteMonitorPolicy",Version:"2018-06-08",Region:e.region,EnvId:e.envId,PolicyId:e.policyId,Source:e.source};await(0,transactor_1.default)(tcbContracts.tcbDeleteMonitorPolicyContract,t);return{}}async function tcbModifyMonitorPolicy(e){const t={Action:"ModifyMonitorPolicy",Version:"2018-06-08",Region:e.region,PolicyId:e.policyId,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),EnvId:e.envId,Name:e.name,Convergence:e.convergence,Note:e.note,ResType:e.resType,ResName:e.resName,Objects:e.objects,Source:e.source,ActiveStartTime:e.activeStartTime,ActiveEndTime:e.activeEndTime,AlertChannels:e.alertChannels,RecGroups:e.recGroups,RecUsers:e.recUsers,IsActive:e.isActive};await(0,transactor_1.default)(tcbContracts.tcbModifyMonitorPolicyContract,t);return{}}async function tcbDescribeMonitorCondition(e){const t={Action:"DescribeMonitorCondition",Version:"2018-06-08",Region:e.region,EnvId:e.envId,PolicyId:e.policyId,Source:e.source};return{data:(await(0,transactor_1.default)(tcbContracts.tcbDescribeMonitorConditionContract,t)).Data.map(e=>({envId:e.EnvId,policyId:e.PolicyId,conditionId:e.ConditionId,metrics:e.Metrics,cmp:e.Cmp,threshold:e.Threshold,period:e.Period,periodNum:e.PeriodNum,convergence:e.Convergence,source:e.Source,isEdit:e.IsEdit}))}}async function tcbCreateMonitorCondition(e){const t={Action:"CreateMonitorCondition",Version:"2018-06-08",Region:e.region,EnvId:e.envId,PolicyId:e.policyId,Metrics:e.metrics,Cmp:e.cmp,Threshold:e.threshold,Period:e.period,PeriodNum:e.periodNum,Source:e.source,Convergence:e.convergence};return{id:(await(0,transactor_1.default)(tcbContracts.tcbCreateMonitorConditionContract,t)).Id}}async function tcbDeleteMonitorCondition(e){const t={Action:"DeleteMonitorCondition",Version:"2018-06-08",Region:e.region,EnvId:e.envId,PolicyId:e.policyId,ConditionId:e.conditionId,Source:e.source};await(0,transactor_1.default)(tcbContracts.tcbDeleteMonitorConditionContract,t);return{}}async function tcbModifyMonitorCondition(e){const t={Action:"ModifyMonitorCondition",Version:"2018-06-08",Region:e.region,ConditionId:e.conditionId,EnvId:e.envId,PolicyId:e.policyId,Metrics:e.metrics,Cmp:e.cmp,Threshold:e.threshold,Period:e.period,PeriodNum:e.periodNum,Source:e.source,Convergence:e.convergence};await(0,transactor_1.default)(tcbContracts.tcbModifyMonitorConditionContract,t);return{}}async function tcbDescribeDauData(e){const t={Action:"DescribeDauData",Version:"2018-06-08",Region:e.region,EnvId:e.envId,StartDate:e.startDate,EndDate:e.endDate,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)()};return{dauDataList:(await(0,transactor_1.default)(tcbContracts.tcbDescribeDauDataContract,t)).DauDataList.map(e=>({statDate:e.StatDate,amount:e.Amount}))}}async function tcbDescribeChangePay(e){const t={Action:"DescribeChangePay",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ChangeWay:e.changeWay},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeChangePayContract,t);return{changeable:o.Changeable,failReason:o.FailReason,refundAmount:o.RefundAmount,refundType:o.RefundType}}async function tcbDescribeRestoreHistory(e){const t={Action:"DescribeRestoreHistory",Version:"2018-06-08",Region:e.region,InstanceId:e.instanceId,Count:e.count,Page:e.page},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeRestoreHistoryContract,t);return{total:o.Total,data:o.Data.map(e=>({operateTime:e.OperateTime,restoreTime:e.RestoreTime,restorTables:e.RestorTables}))}}async function tcbCommonServiceAPI(e){const t={Action:"CommonServiceAPI",Version:"2018-06-08",Region:e.region,Service:e.service,JSONData:e.jsonData};return{jsonResp:(await(0,transactor_1.default)(tcbContracts.tcbCommonServiceAPIContract,t)).JSONResp}}async function tcbCreatePostpayPackage(e){const t={Action:"CreatePostpayPackage",Version:"2018-06-08",Region:e.region,EnvId:e.envId,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),Source:e.source,FreeQuota:e.freeQuota,ChangePay:e.changePay,Alias:e.alias,Channel:e.alias?"ide":void 0,EnvSource:e.alias?"miniapp":void 0,CopyFromEnv:e.copyFromEnv},o=await(0,transactor_1.default)(tcbContracts.tcbCreatePostpayPackageContract,t);return{tranId:o.TranId,envId:o.EnvId}}async function tcbInqueryPostpayPrice(e){const t={Action:"InqueryPostpayPrice",Version:"2018-06-08",Region:e.region};return{priceInfoList:(await(0,transactor_1.default)(tcbContracts.tcbInqueryPostpayPriceContract,t)).PriceInfoList.map(e=>({resourceType:e.ResourceType,resourceMetric:e.ResourceMetric,unitPrice:e.UnitPrice,metricUnit:e.MetricUnit}))}}async function tcbDescribePostpayFreeQuotas(e){const t={Action:"DescribePostpayFreeQuotas",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{freequotaInfoList:((await(0,transactor_1.default)(tcbContracts.tcbDescribePostpayFreeQuotasContract,t)).FreequotaInfoList||[]).map(e=>({resourceType:e.ResourceType,resourceMetric:e.ResourceMetric,freeQuota:e.FreeQuota,metricUnit:e.MetricUnit,deductType:e.DeductType,freeQuotaType:e.FreeQuotaType}))}}async function tcbModifyStorageSafeRule(e){const t={Action:"ModifyStorageSafeRule",Version:"2018-06-08",Region:e.region,Bucket:e.bucket,EnvId:e.envId,AclTag:e.aclTag,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),Rule:e.rule};await(0,transactor_1.default)(tcbContracts.tcbModifyStorageSafeRuleContract,t);return{}}async function tcbDescribeStorageSafeRule(e){const t={Action:"DescribeStorageSafeRule",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Bucket:e.bucket,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeStorageSafeRuleContract,t);return{aclTag:o.AclTag,rule:o.Rule}}async function tcbDescribeCDNChainTask(e){const t={Action:"DescribeCDNChainTask",Version:"2018-06-08",Region:e.region,Bucket:e.bucket,EnvId:e.envId};return{status:(await(0,transactor_1.default)(tcbContracts.tcbDescribeCDNChainTaskContract,t)).Status}}async function tcbDescribeLoginConfigs(e){const t={Action:"DescribeLoginConfigs",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{configList:(await(0,transactor_1.default)(tcbContracts.tcbDescribeLoginConfigsContract,t)).ConfigList.map(e=>({platform:e.Platform,platformId:e.PlatformId,createTime:e.CreateTime,updateTime:e.UpdateTime,status:e.Status,id:e.Id}))}}async function tcbCreateLoginConfig(e){const t={Action:"CreateLoginConfig",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Platform:e.platform,PlatformId:e.platformId,PlatformSecret:e.platformSecret,Status:e.status};await(0,transactor_1.default)(tcbContracts.tcbCreateLoginConfigContract,t);return{}}async function tcbUpdateLoginConfig(e){const t={Action:"UpdateLoginConfig",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ConfigId:e.configId,PlatformId:e.platformId,PlatformSecret:e.platformSecret,Status:e.status};await(0,transactor_1.default)(tcbContracts.tcbUpdateLoginConfigContract,t);return{}}async function tcbDescribeSecurityRule(e){const t={Action:"DescribeSecurityRule",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceType:e.resourceType,ResourceName:e.resourceName,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),OnlyTag:e.onlyTag},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeSecurityRuleContract,t);return{aclTag:o.AclTag,rule:o.Rule}}async function tcbModifySecurityRule(e){const t={Action:"ModifySecurityRule",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceType:e.resourceType,AclTag:e.aclTag,ResourceName:e.resourceName,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),Rule:e.rule};await(0,transactor_1.default)(tcbContracts.tcbModifySecurityRuleContract,t);return{}}async function tcbCreateStaticStore(e){const t={Action:"CreateStaticStore",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{result:(await(0,transactor_1.default)(tcbContracts.tcbCreateStaticStoreContract,t)).Result}}async function tcbDestroyStaticStore(e){const t={Action:"DestroyStaticStore",Version:"2018-06-08",Region:e.region,EnvId:e.envId,CdnDomain:e.cdnDomain};return{result:(await(0,transactor_1.default)(tcbContracts.tcbDestroyStaticStoreContract,t)).Result}}async function tcbDescribeStaticStore(e){const t={Action:"DescribeStaticStore",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{data:((await(0,transactor_1.default)(tcbContracts.tcbDescribeStaticStoreContract,t)).Data||[]).map(e=>({envId:e.EnvId,cdnDomain:e.CdnDomain,bucket:e.Bucket,regoin:e.Regoin,status:e.Status}))}}async function tcbDescribeEnvLimit(e){const t={Action:"DescribeEnvLimit",Version:"2018-06-08",Region:e.region,Source:e.source,EnvType:e.envType},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeEnvLimitContract,t);return{maxEnvNum:o.MaxEnvNum,currentEnvNum:o.CurrentEnvNum,maxFreeEnvNum:o.MaxFreeEnvNum,currentFreeEnvNum:o.CurrentFreeEnvNum,maxDeleteTotal:o.MaxDeleteTotal,currentDeleteTotal:o.CurrentDeleteTotal,maxDeleteMonthly:o.MaxDeleteMonthly,currentDeleteMonthly:o.CurrentDeleteMonthly}}async function tcbDescribeAccountInfoByPlatformId(e){const t={Action:"DescribeAccountInfoByPlatformId",Version:"2018-06-08",Region:e.region,Platform:e.platform,PlatformId:e.platformId},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeAccountInfoByPlatformIdContract,t);return{uin:o.Uin,nickName:o.NickName,isAuth:o.IsAuth}}async function tcbDescribeEnvFreeQuota(e){const t={Action:"DescribeEnvFreeQuota",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceTypes:e.resourceTypes};return{quotaItems:((await(0,transactor_1.default)(tcbContracts.tcbDescribeEnvFreeQuotaContract,t)).QuotaItems||[]).map(e=>({resourceType:e.ResourceType,metricName:e.MetricName,value:e.Value,startTime:e.StartTime,endTime:e.EndTime}))}}async function tcbDescribeHostingDomain(e){const t={Action:"DescribeHostingDomain",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Offset:e.offset,Limit:e.limit},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeHostingDomainContract,t);return{totalCount:o.TotalCount,domainSet:(o.DomainSet||[]).map(e=>({domain:e.Domain,certId:e.CertId,status:e.Status,cName:e.CName,updateTime:e.UpdateTime,createTime:e.CreateTime,dnsStatus:e.DNSStatus,statusMessage:e.StatusMessage}))}}async function tcbDescribeCloudBaseRunResource(e){const t={Action:"DescribeCloudBaseRunResource",Version:"2018-06-08",Region:e.region,EnvId:e.envId,NeedSubnet:e.needSubnet},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunResourceContract,t);return{clusterStatus:o.ClusterStatus,virtualClusterId:o.VirtualClusterId,vpcId:o.VpcId,region:o.Region,subnetIds:(o.SubnetIds||[]).map(e=>({id:e.Id,cidr:e.Cidr,zone:e.Zone,type:e.Type,target:e.Target,region:e.Region,name:e.Name}))}}async function tcbDescribeCloudBaseRunServers(e){const t={Action:"DescribeCloudBaseRunServers",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Offset:e.offset,Limit:e.limit,ServerName:e.serverName},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunServersContract,t);return{totalCount:o.TotalCount,cloudBaseRunServerSet:(o.CloudBaseRunServerSet||[]).map(e=>({serverName:e.ServerName,createdTime:e.CreatedTime,updatedTime:e.UpdatedTime,isPublic:e.IsPublic,imageRepo:e.ImageRepo,vpcId:e.VpcId,serviceRemark:e.ServiceRemark,trafficType:e.TrafficType,allowDeleteImageRepo:e.AllowDeleteImageRepo,natIp:e.NatIp||[]}))}}async function tcbCreateCloudBaseRunResource(e){const t={Action:"CreateCloudBaseRunResource",Version:"2018-06-08",Region:e.region,EnvId:e.envId,VpcId:e.vpcId,SubnetIds:e.subnetIds};return{result:(await(0,transactor_1.default)(tcbContracts.tcbCreateCloudBaseRunResourceContract,t)).Result}}async function tcbDescribeCloudBaseRunBuildServer(e){const t={Action:"DescribeCloudBaseRunBuildServer",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Business:e.business},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunBuildServerContract,t);return{packageRepositoryId:o.PackageRepositoryId,packageRepositoryName:o.PackageRepositoryName,imageNamespace:o.ImageNamespace,projectGlobalKey:o.ProjectGlobalKey,projectToken:o.ProjectToken,projectName:o.ProjectName,teamGlobalKey:o.TeamGlobalKey,projectId:o.ProjectId}}async function tcbDescribeCloudBaseRunServer(e){const t={Action:"DescribeCloudBaseRunServer",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,Offset:e.offset,Limit:e.limit,VersionName:e.versionName},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunServerContract,t);return{totalCount:o.TotalCount,versionItems:(o.VersionItems||[]).map(e=>({versionName:e.VersionName,status:e.Status,flowRatio:e.FlowRatio,createdTime:e.CreatedTime,updatedTime:e.UpdatedTime,buildId:e.BuildId,uploadType:e.UploadType,remark:e.Remark,urlParam:e.UrlParam?{key:e.UrlParam.Key,value:e.UrlParam.Value}:e.UrlParam,priority:e.Priority,isDefaultPriority:e.IsDefaultPriority,flowParams:(e.FlowParams||[]).map(e=>({key:e.Key,value:e.Value,priority:e.Priority}))})),serverName:o.ServerName,isPublic:o.IsPublic,imageRepo:o.ImageRepo,trafficType:o.TrafficType}}async function tcbDescribeCloudBaseRunContainerSpec(e){const t={Action:"DescribeCloudBaseRunContainerSpec",Version:"2018-06-08",Region:e.region};return{containerStandards:(await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunContainerSpecContract,t)).ContainerStandards.map(e=>({cpus:e.Cpus,mems:e.Mems}))}}async function tcbCreateCloudBaseRunServerVersion(e){var t;const o={Action:"CreateCloudBaseRunServerVersion",Version:"2018-06-08",Region:e.region,EnvId:e.envId,UploadType:e.uploadType,FlowRatio:e.flowRatio,Cpu:e.cpu,Mem:e.mem,MinNum:e.minNum,MaxNum:e.maxNum,PolicyType:e.policyType,PolicyThreshold:e.policyThreshold,ContainerPort:e.containerPort,ServerName:e.serverName,RepositoryType:e.repositoryType,DockerfilePath:e.dockerfilePath,BuildDir:e.buildDir,EnvParams:e.envParams,Repository:e.repository,Branch:e.branch,VersionRemark:e.versionRemark,PackageName:e.packageName,PackageVersion:e.packageVersion,ImageInfo:e.imageInfo?{RepositoryName:e.imageInfo.repositoryName,IsPublic:e.imageInfo.isPublic,TagName:e.imageInfo.tagName,ServerAddr:e.imageInfo.serverAddr,ImageUrl:e.imageInfo.imageUrl}:e.imageInfo,CodeDetail:e.codeDetail?{Name:e.codeDetail.name?{Name:e.codeDetail.name.name,FullName:e.codeDetail.name.fullName}:e.codeDetail.name,Url:e.codeDetail.url}:e.codeDetail,ImageSecretInfo:e.imageSecretInfo?{RegistryServer:e.imageSecretInfo.registryServer,UserName:e.imageSecretInfo.userName,Password:e.imageSecretInfo.password,Email:e.imageSecretInfo.email}:e.imageSecretInfo,ImagePullSecret:e.imagePullSecret,CustomLogs:e.customLogs,InitialDelaySeconds:e.initialDelaySeconds,MountVolumeInfo:null===(t=e.mountVolumeInfo)||void 0===t?void 0:t.map(e=>{var t;return{Name:e.name,MountPath:e.mountPath,ReadOnly:e.readOnly,NfsVolumes:null===(t=e.nfsVolumes)||void 0===t?void 0:t.map(e=>({Server:e.server,Path:e.path,ReadOnly:e.readOnly}))}}),AddIntranetDns:e.addIntranetDns,AccessType:e.useHttpRoute?void 0:4,MountWxToken:e.mountWxToken},r=await(0,transactor_1.default)(tcbContracts.tcbCreateCloudBaseRunServerVersionContract,o);return{result:r.Result,versionName:r.VersionName}}async function tcbDescribeCloudBaseRunServerVersion(e){const t={Action:"DescribeCloudBaseRunServerVersion",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,VersionName:e.versionName},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunServerVersionContract,t);return{versionName:o.VersionName,remark:o.Remark,dockerfilePath:o.DockerfilePath,buildDir:o.BuildDir,cpu:o.CpuSize,mem:o.MemSize,minNum:o.MinNum,maxNum:o.MaxNum,policyType:o.PolicyType,policyThreshold:o.PolicyThreshold,envParams:o.EnvParams,createdTime:o.CreatedTime,updatedTime:o.UpdatedTime,versionIp:o.VersionIP,versionPort:o.VersionPort,status:o.Status,packageName:o.PackageName,packageVersion:o.PackageVersion,uploadType:o.UploadType,repoType:o.RepoType,repo:o.Repo,branch:o.Branch,serverName:o.ServerName,isPublic:o.IsPublic,vpcId:o.VpcId,subnetIds:o.SubnetIds||[],customLogs:o.CustomLogs,containerPort:o.ContainerPort,initialDelaySeconds:o.InitialDelaySeconds,imageUrl:o.ImageUrl,mountWxToken:o.MountWxToken}}async function tcbEstablishCloudBaseRunServer(e){const t={Action:"EstablishCloudBaseRunServer",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServiceName:e.serviceName,IsPublic:e.isPublic,ImageRepo:e.imageRepo,Remark:e.remark};await(0,transactor_1.default)(tcbContracts.tcbEstablishCloudBaseRunServerContract,t);return{}}async function tcbDeleteCloudBaseRunResource(e){const t={Action:"DeleteCloudBaseRunResource",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{result:(await(0,transactor_1.default)(tcbContracts.tcbDeleteCloudBaseRunResourceContract,t)).Result}}async function tcbDescribeCloudBaseRunPodList(e){const t={Action:"DescribeCloudBaseRunPodList",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,VersionName:e.versionName,Limit:e.limit,Offset:e.offset,Status:e.status,PodName:e.podName},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunPodListContract,t);return{offset:o.Offset,limit:o.Limit,totalCount:o.TotalCount,podList:(o.PodList||[]).map(e=>({webshell:e.Webshell,podId:e.PodId,podIp:e.PodIp,status:e.Status,createTime:e.CreateTime}))}}async function tcbDescribeCloudBaseRunBuildLog(e){const t={Action:"DescribeCloudBaseRunBuildLog",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServiceName:e.serviceName,ServiceVersion:e.serviceVersion,BuildId:e.buildId,Start:e.start},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunBuildLogContract,t);return{log:o.Log?{total:o.Log.Total,delivered:o.Log.Delivered,text:o.Log.Text,more:o.Log.More}:o.Log}}async function tcbDescribeCloudBaseBuildService(e){var t,o;const r={Action:"DescribeCloudBaseBuildService",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServiceName:e.serviceName,CiBusiness:e.ciBusiness,ServiceVersion:e.serviceVersion},a=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseBuildServiceContract,r);return{uploadUrl:a.UploadUrl,uploadHeaders:null===(t=a.UploadHeaders)||void 0===t?void 0:t.map(e=>({key:e.Key,value:e.Value})),packageName:a.PackageName,packageVersion:a.PackageVersion,downloadHeaders:null===(o=a.DownloadHeaders)||void 0===o?void 0:o.map(e=>({key:e.Key,value:e.Value})),downloadUrl:a.DownloadUrl}}async function tcbDescribeCloudBaseRunVersionException(e){const t={Action:"DescribeCloudBaseRunVersionException",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,VersionName:e.versionName},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunVersionExceptionContract,t);return{status:o.Status,exceptionInfo:o.ExceptionInfo,advice:o.Advice?{guideline:o.Advice.Guideline||[],linkInfos:(o.Advice.LinkInfos||[]).map(e=>({index:e.Index,type:e.Type,matchWords:e.MatchWords,link:e.Link}))}:o.Advice,errorType:o.ErrorType}}async function tcbModifyCloudBaseRunServerVersion(e){const t={Action:"ModifyCloudBaseRunServerVersion",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,VersionName:e.versionName,EnvParams:e.envParams,MinNum:e.minNum,MaxNum:e.maxNum,ContainerPort:e.containerPort,Remark:e.remark,CustomLogs:e.customLogs,IsResetRemark:e.isResetRemark,BasicModify:e.basicModify};return{result:(await(0,transactor_1.default)(tcbContracts.tcbModifyCloudBaseRunServerVersionContract,t)).Result}}async function tcbDescribeCloudBaseGWAPI(e){const t={Action:"DescribeCloudBaseGWAPI",Version:"2018-06-08",Region:e.region,ServiceId:e.serviceId,Domain:e.domain,Path:e.path,ApiId:e.apiId,Type:e.type,Name:e.name,Offset:e.offset,Limit:e.limit},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseGWAPIContract,t);return{apiSet:(o.APISet||[]).map(e=>({serviceId:e.ServiceId,apiId:e.ApiId,path:e.Path,type:e.Type,name:e.Name,createTime:e.CreateTime,custom:e.Custom,enableAuth:e.EnableAuth,envId:e.EnvId,accessType:e.AccessType})),enableService:o.EnableService,total:o.Total,offset:o.Offset,limit:o.Limit}}async function tcbDescribeCloudBaseRunBuildStages(e){const t={Action:"DescribeCloudBaseRunBuildStages",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServiceName:e.serviceName,ServiceVersion:e.serviceVersion,BuildId:e.buildId},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunBuildStagesContract,t);return{stages:o.Stages?{jsonData:o.Stages.JsonData}:o.Stages}}async function tcbDescribeCloudBaseRunBuildSteps(e){const t={Action:"DescribeCloudBaseRunBuildSteps",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServiceName:e.serviceName,ServiceVersion:e.serviceVersion,BuildId:e.buildId,StageId:e.stageId},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunBuildStepsContract,t);return{steps:o.Steps?{jsonData:o.Steps.JsonData}:o.Steps}}async function tcbDescribeCloudBaseRunBuildStepLog(e){const t={Action:"DescribeCloudBaseRunBuildStepLog",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServiceName:e.serviceName,ServiceVersion:e.serviceVersion,BuildId:e.buildId,StageId:e.stageId,StepId:e.stepId,Start:e.start},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunBuildStepLogContract,t);return{log:o.Log?{total:o.Log.Total,delivered:o.Log.Delivered,text:o.Log.Text,more:o.Log.More}:o.Log}}async function tcbDeleteCloudBaseRunServerVersion(e){const t={Action:"DeleteCloudBaseRunServerVersion",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,VersionName:e.versionName,IsDeleteServer:e.isDeleteServer,IsDeleteImage:e.isDeleteImage};return{result:(await(0,transactor_1.default)(tcbContracts.tcbDeleteCloudBaseRunServerVersionContract,t)).Result}}async function tcbDeleteCloudBaseRunImageRepo(e){const t={Action:"DeleteCloudBaseRunImageRepo",Version:"2018-06-08",Region:e.region,ImageRepo:e.imageRepo};await(0,transactor_1.default)(tcbContracts.tcbDeleteCloudBaseRunImageRepoContract,t);return{}}async function tcbRollUpdateCloudBaseRunServerVersion(e){var t;const o={Action:"RollUpdateCloudBaseRunServerVersion",Version:"2018-06-08",Region:e.region,EnvId:e.envId,VersionName:e.versionName,UploadType:e.uploadType,RepositoryType:e.repositoryType,FlowRatio:e.flowRatio,DockerfilePath:e.dockerfilePath,BuildDir:e.buildDir,Cpu:String(e.cpu),Mem:String(e.mem),MinNum:String(e.minNum),MaxNum:String(e.maxNum),PolicyType:e.policyType,PolicyThreshold:String(e.policyThreshold),EnvParams:e.envParams,ContainerPort:e.containerPort,ServerName:e.serverName,Repository:e.repository,Branch:e.branch,VersionRemark:e.versionRemark,PackageName:e.packageName,PackageVersion:e.packageVersion,ImageInfo:e.imageInfo?{RepositoryName:e.imageInfo.repositoryName,IsPublic:e.imageInfo.isPublic,TagName:e.imageInfo.tagName,ServerAddr:e.imageInfo.serverAddr,ImageUrl:e.imageInfo.imageUrl}:e.imageInfo,CodeDetail:e.codeDetail?{Name:e.codeDetail.name?{Name:e.codeDetail.name.name,FullName:e.codeDetail.name.fullName}:e.codeDetail.name,Url:e.codeDetail.url}:e.codeDetail,IsRebuild:e.isRebuild,InitialDelaySeconds:e.initialDelaySeconds,MountVolumeInfo:null===(t=e.mountVolumeInfo)||void 0===t?void 0:t.map(e=>{var t;return{Name:e.name,MountPath:e.mountPath,ReadOnly:e.readOnly,NfsVolumes:null===(t=e.nfsVolumes)||void 0===t?void 0:t.map(e=>({Server:e.server,Path:e.path,ReadOnly:e.readOnly}))}}),Rollback:e.rollback,SnapshotName:e.snapshotName,CustomLogs:e.customLogs},r=await(0,transactor_1.default)(tcbContracts.tcbRollUpdateCloudBaseRunServerVersionContract,o);return{result:r.Result,versionName:r.VersionName}}async function tcbModifyCloudBaseRunServerFlowConf(e){var t;const o={Action:"ModifyCloudBaseRunServerFlowConf",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,VersionFlowItems:null===(t=e.versionFlowItems)||void 0===t?void 0:t.map(e=>({VersionName:e.versionName,FlowRatio:e.flowRatio,UrlParam:e.urlParam?{Key:e.urlParam.key,Value:e.urlParam.value}:e.urlParam,Priority:e.priority,IsDefaultPriority:e.isDefaultPriority})),TrafficType:e.trafficType};return{result:(await(0,transactor_1.default)(tcbContracts.tcbModifyCloudBaseRunServerFlowConfContract,o)).Result}}async function tcbDeleteCloudBaseRunServer(e){const t={Action:"DeleteCloudBaseRunServer",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName};return{result:(await(0,transactor_1.default)(tcbContracts.tcbDeleteCloudBaseRunServerContract,t)).Result}}async function tcbDescribeCloudBaseCodeRepos(e){const t={Action:"DescribeCloudBaseCodeRepos",Version:"2018-06-08",Region:e.region,Channel:e.channel,PageNumber:e.pageNumber,PageSize:e.pageSize},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseCodeReposContract,t);return{repoList:(o.RepoList||[]).map(e=>({name:e.Name,fullName:e.FullName})),pageNumber:o.PageNumber,pageSize:o.PageSize,isFinished:o.IsFinished}}async function tcbDescribeCloudBaseCodeBranch(e){const t={Action:"DescribeCloudBaseCodeBranch",Version:"2018-06-08",Region:e.region,Channel:e.channel,RepoName:{Name:e.repoName.name,FullName:e.repoName.fullName},PageNumber:e.pageNumber,PageSize:e.pageSize},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseCodeBranchContract,t);return{branchList:(o.BranchList||[]).map(e=>({name:e.Name,isProtected:e.IsProtected})),pageNumber:o.PageNumber,pageSize:o.PageSize,isFinished:o.IsFinished}}async function tcbCreateHostingDomain(e){const t={Action:"CreateHostingDomain",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Domain:e.domain,CertId:e.certId};await(0,transactor_1.default)(tcbContracts.tcbCreateHostingDomainContract,t);return{}}async function tcbDescribePostpayPackageList(e){return{postpayPackageInfoList:(await(0,transactor_1.default)(tcbContracts.tcbDescribePostpayPackageListContract,{Action:"DescribePostpayPackageList",Version:"2018-06-08"})).PostpayPackageInfoList.map(e=>({postpayPackageId:e.PostpayPackageId,resourceType:e.ResourceType,pid:e.Pid,name:e.Name,desc:e.Desc,detail:e.Detail,categoryid:e.Categoryid,tag:e.Tag}))}}async function tcbQueryActivityPrice(e){const t={Action:"QueryActivityPrice",Version:"2018-06-08",Region:e.region,ActivityId:e.activityId,ProductId:e.productId,PostpayPackageId:e.postpayPackageId,TimeSpan:e.timeSpan};return{price:(await(0,transactor_1.default)(tcbContracts.tcbQueryActivityPriceContract,t)).Price}}async function tcbDeleteHostingDomain(e){const t={Action:"DeleteHostingDomain",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Domain:e.domain};await(0,transactor_1.default)(tcbContracts.tcbDeleteHostingDomainContract,t);return{}}async function tcbCheckQualification(e){const t={Action:"CheckQualification",Version:"2018-06-08",Region:e.region,ActivityId:e.activityId,ProductId:e.productId};return{available:(await(0,transactor_1.default)(tcbContracts.tcbCheckQualificationContract,t)).Available}}async function tcbModifyHostingDomain(e){const t={Action:"ModifyHostingDomain",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Domain:e.domain,CertId:e.certId};await(0,transactor_1.default)(tcbContracts.tcbModifyHostingDomainContract,t);return{}}async function tcbCreateActivityDeal(e){const t={Action:"CreateActivityDeal",Version:"2018-06-08",Region:e.region,ActivityId:e.activityId,ProductId:e.productId,PostpayPackageId:e.postpayPackageId,TimeSpan:e.timeSpan,EnvId:e.envId};return{tranId:(await(0,transactor_1.default)(tcbContracts.tcbCreateActivityDealContract,t)).TranId}}async function tcbDescribeActivityGoods(e){const t={Action:"DescribeActivityGoods",Version:"2018-06-08",Region:e.region,ActivityId:e.activityId};return{products:(await(0,transactor_1.default)(tcbContracts.tcbDescribeActivityGoodsContract,t)).Products.map(e=>({productId:e.ProductId,productName:e.ProductName,postpayPackageId:e.PostpayPackageId,timeSpan:e.TimeSpan}))}}async function tcbInqueryPackagePrice(e){const t={Action:"InqueryPackagePrice",Version:"2018-06-08",Region:e.region,PostpayPackageId:e.postpayPackageId};return{price:(await(0,transactor_1.default)(tcbContracts.tcbInqueryPackagePriceContract,t)).Price}}async function tcbOnlineHostingDomain(e){const t={Action:"OnlineHostingDomain",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Domain:e.domain};await(0,transactor_1.default)(tcbContracts.tcbOnlineHostingDomainContract,t);return{}}async function tcbDescribeEnvPostpayPackage(e){const t={Action:"DescribeEnvPostpayPackage",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{envPostpayPackageInfoList:(await(0,transactor_1.default)(tcbContracts.tcbDescribeEnvPostpayPackageContract,t)).EnvPostpayPackageInfoList.map(e=>({postpayPackageId:e.PostpayPackageId,envId:e.EnvId,createTime:e.CreateTime,expireTime:e.ExpireTime,status:e.Status,detail:e.Detail,resoureceId:e.ResourceId,overrun:e.Overrun,canRefund:e.CanRefund}))}}async function tcbDescribePostpayQuotaLimit(e){const t={Action:"DescribePostpayQuotaLimit",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{quotaLimitSet:((await(0,transactor_1.default)(tcbContracts.tcbDescribePostpayQuotaLimitContract,t)).QuotaLimitSet||[]).map(e=>({resourceType:e.ResourceType,metric:e.Metric,quota:e.Quota,interval:e.Interval,warningRates:e.WarningRates,createTime:e.CreateTime,updateTime:e.UpdateTime,status:e.Status}))}}async function tcbUpdatePostpayQuotaLimitStatus(e){const t={Action:"UpdatePostpayQuotaLimitStatus",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceType:e.resourceType,Metric:e.metric,Status:e.status};await(0,transactor_1.default)(tcbContracts.tcbUpdatePostpayQuotaLimitStatusContract,t);return{}}async function tcbUpdatePostpayQuotaLimit(e){const t={Action:"UpdatePostpayQuotaLimit",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceType:e.resourceType,Metric:e.metric,Quota:e.quota,Interval:e.interval,WarningRates:e.warningRates};await(0,transactor_1.default)(tcbContracts.tcbUpdatePostpayQuotaLimitContract,t);return{}}async function tcbUpdateScfConfig(e){const t={Action:"UpdateScfConfig",Version:"2018-06-08",Region:e.region,EnvId:e.envId,FunctionName:e.functionName,EipStatus:e.eipStatus,PubNetStatus:e.pubNetStatus};await(0,transactor_1.default)(tcbContracts.tcbUpdateScfConfigContract,t);return{}}async function tcbCreateInstallExtensionTask(e){const t={Action:"CreateInstallExtensionTask",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ExtensionId:e.extensionId,Template:e.template,Flag:e.flag};await(0,transactor_1.default)(tcbContracts.tcbCreateInstallExtensionTaskContract,t);return{}}async function tcbCreateUninstallExtensionTask(e){const t={Action:"CreateUninstallExtensionTask",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ExtensionId:e.extensionId,Option:e.option};await(0,transactor_1.default)(tcbContracts.tcbCreateUninstallExtensionTaskContract,t);return{}}async function tcbDescribeExtensionInstalled(e){const t={Action:"DescribeExtensionInstalled",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Offset:e.offset,Limit:e.limit,ExtensionNames:e.extensionNames,ExtensionIds:e.extensionIds,Sort:e.sort},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeExtensionInstalledContract,t);return{extensions:o.Extensions.map(e=>({id:e.Id,status:e.Status,updateTime:e.UpdateTime,createTime:e.CreateTime,taskInfo:e.TaskInfo})),totalCount:o.TotalCount}}async function tcbDescribeExtensionTaskStatus(e){const t={Action:"DescribeExtensionTaskStatus",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ExtensionIds:e.extensionIds};return{extensionTaskInfo:(await(0,transactor_1.default)(tcbContracts.tcbDescribeExtensionTaskStatusContract,t)).ExtensionTaskInfo.map(e=>({extensionId:e.ExtensionId,status:e.Status,detail:e.Detail,percent:e.Percent}))}}async function tcbDescribeExtensionTemplates(e){const t={Action:"DescribeExtensionTemplates",Version:"2018-06-08",Region:e.region,ExtensionId:e.extensionId,TemplateTypes:e.templateTypes,EnvId:e.envId};return{templates:(await(0,transactor_1.default)(tcbContracts.tcbDescribeExtensionTemplatesContract,t)).Templates.map(e=>({key:e.Key,value:e.Value}))}}async function tcbDescribeExtensionUpgrade(e){const t={Action:"DescribeExtensionUpgrade",Version:"2018-06-08",Region:e.region,ExtensionIds:e.extensionIds};return{extensions:(await(0,transactor_1.default)(tcbContracts.tcbDescribeExtensionUpgradeContract,t)).Extensions.map(e=>({currentExtensionId:e.CurrentExtensionId,nextExtensionId:e.NextExtensionId,version:e.Version,changeLog:e.ChangeLog}))}}async function tcbDescribeExtensions(e){const t={Action:"DescribeExtensions",Version:"2018-06-08",Region:e.region,Offset:e.offset,Limit:e.limit,Sort:e.sort,ExtensionIds:e.extensionIds,ExtensionNames:e.extensionNames},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeExtensionsContract,t);return{extensions:o.Extensions.map(e=>({id:e.Id,name:e.Name,version:e.Version,tags:e.Tags,description:e.Description,createTime:e.CreateTime,iconUrl:e.IconUrl,docUrl:e.DocUrl,status:e.Status,updateTime:e.UpdateTime,templateId:e.TemplateId})),totalCount:o.TotalCount}}async function tcbCreateUpgradeExtensionTask(e){const t={Action:"CreateUpgradeExtensionTask",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ExtensionId:e.extensionId};await(0,transactor_1.default)(tcbContracts.tcbCreateUpgradeExtensionTaskContract,t);return{}}async function tcbDescribeCloudBaseRunOperationDetails(e){const t={Action:"DescribeCloudBaseRunOperationDetails",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServerName:e.serverName,ActionType:e.actionType,Offset:e.offset,Limit:e.limit,StartTime:e.startTime,EndTime:e.endTime},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunOperationDetailsContract,t);return{totalCount:o.TotalCount,actionDetails:(o.ActionDetails||[]).map(e=>({actionDetail:e.ActionDetail?{serverName:e.ActionDetail.ServerName||[],versionName:e.ActionDetail.VersionName||[],runId:e.ActionDetail.RunId,actionType:e.ActionDetail.ActionType,status:e.ActionDetail.Status,actionData:e.ActionDetail.ActionData,createdTime:e.ActionDetail.CreatedTime,operatorRemark:e.ActionDetail.OperatorRemark,operatorType:e.ActionDetail.OperatorType,oldData:e.ActionDetail.OldData,freshData:e.ActionDetail.FreshData}:e.ActionDetail,stepDetails:(e.StepDetails||[]).map(e=>({serverName:e.ServerName||[],versionName:e.VersionName||[],runId:e.RunId,actionType:e.ActionType,status:e.Status,actionData:e.ActionData,createdTime:e.CreatedTime,operatorRemark:e.OperatorRemark,operatorType:e.OperatorType,oldData:e.OldData,freshData:e.FreshData}))}))}}async function tcbDescribeCloudBaseRunVersionSnapshot(e){const t={Action:"DescribeCloudBaseRunVersionSnapshot",Version:"2018-06-08",Region:e.region,ServerName:e.serverName,VersionName:e.versionName,EnvId:e.envId,SnapshotName:e.snapshotName,Offset:e.offset,Limit:e.limit};return{snapshots:((await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunVersionSnapshotContract,t)).Snapshots||[]).map(e=>({versionName:e.VersionName,remark:e.Remark,cpu:e.Cpu,mem:e.Mem,minNum:e.MinNum,maxNum:e.MaxNum,imageUrl:e.ImageUrl,policyType:e.PolicyType,policyThreshold:e.PolicyThreshold,envParams:e.EnvParams,containerPort:e.ContainerPort,createTime:e.CreateTime,updateTime:e.UpdateTime,uploadType:e.UploadType,dockerfilePath:e.DockerfilePath,buildDir:e.BuildDir,repoType:e.RepoType,repo:e.Repo,branch:e.Branch,envId:e.EnvId,serverName:e.ServerName,packageName:e.PackageName,packageVersion:e.PackageVersion,customLogs:e.CustomLogs,initialDelaySeconds:e.InitialDelaySeconds,snapshotName:e.SnapshotName,imageInfo:e.ImageInfo?{repositoryName:e.ImageInfo.RepositoryName,isPublic:e.ImageInfo.IsPublic,tagName:e.ImageInfo.TagName,serverAddr:e.ImageInfo.ServerAddr,imageUrl:e.ImageInfo.ImageUrl}:e.ImageInfo,codeDetail:e.CodeDetail?{name:e.CodeDetail.Name?{name:e.CodeDetail.Name.Name,fullName:e.CodeDetail.Name.FullName}:e.CodeDetail.Name,url:e.CodeDetail.Url}:e.CodeDetail,status:e.Status}))}}async function tcbDescribeQcloudScene(e){const t={Action:"DescribeQcloudScene",Version:"2018-06-08",Region:e.region,Source:e.source};return{data:((await(0,transactor_1.default)(tcbContracts.tcbDescribeQcloudSceneContract,t)).Data||[]).map(e=>({name:e.Name,index:e.Index,period:e.Period,enIndex:e.EnIndex,enName:e.EnName,indexUnit:e.IndexUnit,convergence:e.Convergence,convergenceName:e.ConvergenceName,periodNum:e.PeriodNum,periodInfo:(e.PeriodInfo||[]).map(e=>({indexName:e.IndexName,period:e.Period||[]}))}))}}async function tcbDescribeSmsQuotas(e){const t={Action:"DescribeSmsQuotas",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{smsFreeQuotaList:((await(0,transactor_1.default)(tcbContracts.tcbDescribeSmsQuotasContract,t)).SmsFreeQuotaList||[]).map(e=>({freeQuota:e.FreeQuota,totalUsedQuota:e.TotalUsedQuota,cycleStart:e.CycleStart,cycleEnd:e.CycleEnd,todayUsedQuota:e.TodayUsedQuota}))}}async function tcbDescribeSmsAttrInfo(e){var t;const o={Action:"DescribeSmsAttrInfo",Version:"2018-06-08",Region:e.region,EnvId:e.envId,StartTime:e.startTime,EndTime:e.endTime},r=await(0,transactor_1.default)(tcbContracts.tcbDescribeSmsAttrInfoContract,o);return{period:r.Period,record:null===(t=r.Record)||void 0===t?void 0:t.map(e=>({stateTime:e.StateTime,reqCnt:e.ReqCnt,feeCnt:e.FeeCnt,succCnt:e.SuccCnt})),total:r.Total}}async function tcbDescribeTcbBalance(e){const t={Action:"DescribeTcbBalance",Version:"2018-06-08",Region:e.region};return{balance:(await(0,transactor_1.default)(tcbContracts.tcbDescribeTcbBalanceContract,t)).Balance}}async function tcbDescribeSmsRecords(e){const t={Action:"DescribeSmsRecords",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Mobile:e.mobile,QueryId:e.queryId,StartDate:e.startDate,EndDate:e.endDate,PageNumber:e.pageNumber,PageSize:e.pageSize},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeSmsRecordsContract,t);return{smsRecords:(o.SmsRecords||[]).map(e=>({mobile:e.Mobile,content:e.Content,contentSize:e.ContentSize,fee:e.Fee,createTime:e.CreateTime,receivedTime:e.ReceivedTime,status:e.Status,remarks:e.Remarks})),totalCount:o.TotalCount}}async function tcbDescribeCloudBaseRunServiceDomain(e){const t={Action:"DescribeCloudBaseRunServiceDomain",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ServiceName:e.serviceName},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeCloudBaseRunServiceDomainContract,t);return{defaultPublicDomain:o.DefaultPublicDomain,defaultInternalDomain:o.DefaultInternalDomain,accessTypes:o.AccessTypes}}async function tcbModifyEnv(e){const t={Action:"ModifyEnv",Version:"2018-06-08",Region:e.region,EnvId:e.envId,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),Alias:e.alias,IsDefault:e.isDefault,IsAutoDegrade:e.isAutoDegrade,Source:e.source,Channel:e.channel};await(0,transactor_1.default)(tcbContracts.tcbModifyEnvContract,t);return{}}async function tcbDescribeWxCloudBaseRunEnvs(e){const t={Action:"DescribeWxCloudBaseRunEnvs",Version:"2018-06-08",Region:e.region,EnvId:e.envId,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)()},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeWxCloudBaseRunEnvsContract,t);return{requestId:o.RequestId,envList:o.EnvList.map(e=>({isAutoDegrade:e.IsAutoDegrade,envChannel:e.EnvChannel,payMode:e.PayMode,isDefault:e.IsDefault,region:e.Region,envId:e.EnvId,alias:e.Alias,createTime:(0,common_1.strToDate)(e.CreateTime),updateTime:(0,common_1.strToDate)(e.UpdateTime),status:e.Status,source:e.Source,storages:(e.Storages||[]).map(e=>({region:(e=e||{}).Region,bucket:e.Bucket,cdnDomain:e.CdnDomain,tcAppId:e.AppId})),functions:(e.Functions||[]).map(e=>({namespace:(e=e||{}).Namespace,region:e.Region})),databases:(e.Databases||[]).map(e=>({instanceId:(e=e||{}).InstanceId,status:e.Status,region:e.Region})),packageId:e.PackageId||"",packageName:e.PackageName||"",logServices:(e.LogServices||[]).map(e=>({logsetName:(e=e||{}).LogsetName,logsetId:e.LogsetId,topicName:e.TopicName,topicId:e.TopicId,region:e.Region})),staticStorages:(e.StaticStorages||[]).map(e=>({region:(e=e||{}).Region,bucket:e.Bucket,staticDomain:e.StaticDomain,status:e.Status,defaultDirName:e.DefaultDirName}))}))}}async function tcbDescribeWxCloudBaseRunSubNets(e){const t={Action:"DescribeWxCloudBaseRunSubNets",Version:"2018-06-08",Region:e.region,VpcId:e.vpcId,Limit:e.limit};return{subNetIds:(await(0,transactor_1.default)(tcbContracts.tcbDescribeWxCloudBaseRunSubNetsContract,t)).SubNetIds}}async function tcbRefundPostpaidPackage(e){const t={Action:"RefundPostpaidPackage",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceId:e.resourceId,PostpayPackageId:e.postpayPackageId,PaymentChannel:e.paymentChannel,ResourceIdList:e.resourceIdList};await(0,transactor_1.default)(tcbContracts.tcbRefundPostpaidPackageContract,t);return{}}async function tcbQueryPostpaidPackageDeals(e){const t={Action:"QueryPostpaidPackageDeals",Version:"2018-06-08",Region:e.region,UserClientIp:e.userClientIp,WxAppId:e.wxAppId||(0,transactor_1.getDefaultAppID)(),Limit:e.limit,Offset:e.offset},o=await(0,transactor_1.default)(tcbContracts.tcbQueryPostpaidPackageDealsContract,t);return{total:o.Total,deals:o.Deals.map(e=>({tranId:e.TranId,dealOwner:e.DealOwner,createTime:e.CreateTime,dealStatus:e.DealStatus,dealCost:e.DealCost,envId:e.EnvId,payTime:e.PayTime,timeSpan:e.TimeSpan,price:e.Price,timeUnit:e.TimeUnit,refundAmount:e.RefundAmount,voucherDecline:e.VoucherDecline,action:e.Action,originTotalCost:e.OriginTotalCost,postpaidPackageId:e.PostpaidPackageId,productCode:e.ProductCode,subProductCode:e.SubProductCode,detail:e.Detail,resourceIds:e.ResourceIds||[],canRefund:e.CanRefund}))}}async function tcbSearchClsLog(e){var t,o,r,a,n,i,c;const s={Action:"SearchClsLog",Version:"2018-06-08",Region:e.region,EnvId:e.envId,StartTime:e.startTime,EndTime:e.endTime,QueryString:e.queryString,Limit:e.limit,Context:e.context,Sort:e.sort,UseLucene:e.useLucene},u=await(0,transactor_1.default)(tcbContracts.tcbSearchClsLogContract,s);return{logResults:{context:null!==(o=null===(t=u.LogResults)||void 0===t?void 0:t.Context)&&void 0!==o?o:"",listOver:null===(a=null===(r=u.LogResults)||void 0===r?void 0:r.ListOver)||void 0===a||a,results:null!==(c=null===(i=null===(n=u.LogResults)||void 0===n?void 0:n.Results)||void 0===i?void 0:i.map(e=>({topicId:e.TopicId,topicName:e.TopicName,timestamp:e.Timestamp,content:e.Content,fileName:e.FileName,source:e.Source})))&&void 0!==c?c:[]}}}async function tcbDescribeAuditRule(e){const t={Action:"DescribeAuditRule",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResourceType:e.resourceType,AuditKey:e.auditKey,AuditSubKey:e.auditSubKey,StartTime:e.startTime,EndTime:e.endTime,PageSize:e.pageSize,PageNum:e.pageNum},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeAuditRuleContract,t);return{auditRules:o.AuditRules.map(e=>({resourceType:e.ResourceType,auditKey:e.AuditKey,auditSubKey:e.AuditSubKey,rewriteComment:e.RewriteComment,rewriteSwitch:e.RewriteSwitch,envId:e.EnvId,uin:e.Uin,id:e.Id,auditType:(e.AuditType||[]).map(e=>({auditType:e.AuditType,shieldedLine:e.ShieldedLine})),updateRewriteSwitch:e.UpdateRewriteSwitch,resourceName:e.ResourceName,region:e.Region})),totalCount:o.TotalCount}}async function tcbDescribeCollections(e){const t={Action:"DescribeCollections",Version:"2018-06-08",Region:e.region,EnvKey:e.envKey,DbName:e.dbName};return{collections:(await(0,transactor_1.default)(tcbContracts.tcbDescribeCollectionsContract,t)).Collections}}async function tcbCreateAuditRules(e){const t={Action:"CreateAuditRules",Version:"2018-06-08",Region:e.region,Rules:e.rules.map(e=>({ResourceType:e.resourceType,AuditKey:e.auditKey,AuditSubKey:e.auditSubKey,RewriteComment:e.rewriteComment,RewriteSwitch:e.rewriteSwitch,EnvId:e.envId,Uin:e.uin,Id:e.id,AuditType:(e.auditType||[]).map(e=>({AuditType:e.auditType,ShieldedLine:e.shieldedLine})),UpdateRewriteSwitch:e.updateRewriteSwitch,ResourceName:e.resourceName,Region:e.region})),EnvId:e.envId};await(0,transactor_1.default)(tcbContracts.tcbCreateAuditRulesContract,t);return{}}async function tcbDeleteAuditRule(e){const t={Action:"DeleteAuditRule",Version:"2018-06-08",Region:e.region,AuditId:e.auditId,EnvId:e.envId};await(0,transactor_1.default)(tcbContracts.tcbDeleteAuditRuleContract,t);return{}}async function tcbModifyAuditRule(e){const t={Action:"ModifyAuditRule",Version:"2018-06-08",Region:e.region,Rule:{ResourceType:e.rule.resourceType,AuditKey:e.rule.auditKey,AuditSubKey:e.rule.auditSubKey,RewriteComment:e.rule.rewriteComment,RewriteSwitch:e.rule.rewriteSwitch,EnvId:e.rule.envId,Id:e.rule.id,AuditType:(e.rule.auditType||[]).map(e=>({AuditType:e.auditType,ShieldedLine:e.shieldedLine})),UpdateRewriteSwitch:e.rule.updateRewriteSwitch,ResourceName:e.rule.resourceName,Region:e.rule.region,Uin:e.rule.uin},EnvId:e.envId};await(0,transactor_1.default)(tcbContracts.tcbModifyAuditRuleContract,t);return{}}async function tcbDescribeAuditResults(e){const t={Action:"DescribeAuditResults",Version:"2018-06-08",Region:e.region,EnvId:e.envId,StartTime:e.startTime,EndTime:e.endTime,BeginScore:e.beginScore,EndScore:e.endScore,ResourceType:e.resourceType,AuditKey:e.auditKey,AuditSubKey:e.auditSubKey,AuditType:e.auditType,ShieldStatus:e.shieldStatus,PageSize:e.pageSize,PageNum:e.pageNum},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeAuditResultsContract,t);return{result:o.Result.map(e=>({auditKey:e.AuditKey,auditSubKey:e.AuditSubKey,auditType:e.AuditType||[],auditTime:e.AuditTime,auditResult:e.AuditResult,auditScore:e.AuditScore,auditComment:e.AuditComment,triggerEvent:e.TriggerEvent,envId:e.EnvId,resultId:e.ResultId,auditDesc:e.AuditDesc,docId:e.DocId})),totalCount:o.TotalCount}}async function tcbUnfreezeSecurityAuditRecord(e){const t={Action:"UnfreezeSecurityAuditRecord",Version:"2018-06-08",Region:e.region,EnvId:e.envId,ResultId:e.resultId};await(0,transactor_1.default)(tcbContracts.tcbUnfreezeSecurityAuditRecordContract,t);return{}}async function tcbDescribeSecurityAuditConfig(e){const t={Action:"DescribeSecurityAuditConfig",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{switchOn:(await(0,transactor_1.default)(tcbContracts.tcbDescribeSecurityAuditConfigContract,t)).SwitchOn}}async function tcbDeleteSecurityAuditConfig(e){const t={Action:"DeleteSecurityAuditConfig",Version:"2018-06-08",Region:e.region,EnvId:e.envId};await(0,transactor_1.default)(tcbContracts.tcbDeleteSecurityAuditConfigContract,t);return{}}async function tcbCreateSecurityAuditConfig(e){const t={Action:"CreateSecurityAuditConfig",Version:"2018-06-08",Region:e.region,EnvId:e.envId};await(0,transactor_1.default)(tcbContracts.tcbCreateSecurityAuditConfigContract,t);return{}}async function tcbDescribeTriggerServiceParameters(e){const t={Action:"DescribeTriggerServiceParameters",Version:"2018-06-08",Region:e.region,EnvId:e.envId};return{triggerTypes:(await(0,transactor_1.default)(tcbContracts.tcbDescribeTriggerServiceParametersContract,t)).TriggerTypes.map(e=>({conditionTypes:(e.ConditionTypes||[]).map(e=>({conditionOperator:e.ConditionOperator,conditionTypes:e.ConditionTypes||[]})),triggerType:e.TriggerType,triggerTypeZh:e.TriggerTypeZh,actionTypes:(e.ActionTypes||[]).map(e=>({type:e.Type,text:e.Text})),rateLimit:(e.RateLimit||[]).map(e=>({type:e.Type,text:e.Text})),event:e.Event||[]}))}}async function tcbCreateTriggerConfigs(e){const t={Action:"CreateTriggerConfigs",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Triggers:e.triggers.map(e=>({Id:e.id,Uin:e.uin,EnvId:e.envId,TriggerType:e.triggerType,TriggerKey:e.triggerKey,TriggerEvent:e.triggerEvent,TriggerMode:e.triggerMode,Status:e.status,CreateTime:e.createTime,UpdateTime:e.updateTime,Conditions:(e.conditions||[]).map(e=>({ConditionKey:e.conditionKey,ConditionOperator:e.conditionOperator,ConditionType:e.conditionType,ConditionValue:e.conditionValue,TriggerId:e.triggerId,Id:e.id})),Actions:(e.actions||[]).map(e=>({ActionType:e.actionType,ActionKey:e.actionKey,TriggerId:e.triggerId,Template:e.template,RateLimit:e.rateLimit,RateInterval:e.rateInterval,Id:e.id})),TriggerName:e.triggerName,TriggerDesc:e.triggerDesc,ResourceName:e.resourceName,Region:e.region}))};await(0,transactor_1.default)(tcbContracts.tcbCreateTriggerConfigsContract,t);return{}}async function tcbDescribeTriggerConfigs(e){const t={Action:"DescribeTriggerConfigs",Version:"2018-06-08",Region:e.region,PageNum:e.pageNum,PageSize:e.pageSize,EnvId:e.envId,TriggerName:e.triggerName,StartTime:e.startTime,EndTime:e.endTime},o=await(0,transactor_1.default)(tcbContracts.tcbDescribeTriggerConfigsContract,t);return{triggers:o.Triggers.map(e=>({id:e.Id,uin:e.Uin,envId:e.EnvId,triggerType:e.TriggerType,triggerKey:e.TriggerKey,triggerEvent:e.TriggerEvent,triggerMode:e.TriggerMode,status:e.Status,createTime:e.CreateTime,updateTime:e.UpdateTime,conditions:(e.Conditions||[]).map(e=>({conditionKey:e.ConditionKey,conditionOperator:e.ConditionOperator,conditionType:e.ConditionType,conditionValue:e.ConditionValue,triggerId:e.TriggerId,id:e.Id})),actions:(e.Actions||[]).map(e=>({actionType:e.ActionType,actionKey:e.ActionKey,triggerId:e.TriggerId,template:e.Template,rateLimit:e.RateLimit,rateInterval:e.RateInterval,id:e.Id})),triggerName:e.TriggerName,triggerDesc:e.TriggerDesc,resourceName:e.ResourceName,region:e.Region})),totalCount:o.TotalCount}}async function tcbUpdateTriggerConfig(e){const t={Action:"UpdateTriggerConfig",Version:"2018-06-08",Region:e.region,EnvId:e.envId,Trigger:{Id:e.trigger.id,Uin:e.trigger.uin,EnvId:e.trigger.envId,TriggerType:e.trigger.triggerType,TriggerKey:e.trigger.triggerKey,TriggerEvent:e.trigger.triggerEvent,TriggerMode:e.trigger.triggerMode,Status:e.trigger.status,CreateTime:e.trigger.createTime,UpdateTime:e.trigger.updateTime,Conditions:(e.trigger.conditions||[]).map(e=>({ConditionKey:e.conditionKey,ConditionOperator:e.conditionOperator,ConditionType:e.conditionType,ConditionValue:e.conditionValue,TriggerId:e.triggerId,Id:e.id})),Actions:(e.trigger.actions||[]).map(e=>({ActionType:e.actionType,ActionKey:e.actionKey,TriggerId:e.triggerId,Template:e.template,RateLimit:e.rateLimit,RateInterval:e.rateInterval,Id:e.id})),TriggerName:e.trigger.triggerName,TriggerDesc:e.trigger.triggerDesc,ResourceName:e.trigger.resourceName,Region:e.trigger.region}};await(0,transactor_1.default)(tcbContracts.tcbUpdateTriggerConfigContract,t);return{}}async function tcbDeleteTriggerConfigs(e){const t={Action:"DeleteTriggerConfigs",Version:"2018-06-08",Region:e.region,EnvId:e.envId,TriggerId:e.triggerId};await(0,transactor_1.default)(tcbContracts.tcbDeleteTriggerConfigsContract,t);return{}}async function tcbCreateCopyEnvTask(e){const t={Action:"CreateCopyEnvTask",Version:"2018-06-08",Region:e.region,FromEnv:e.fromEnv,ToEnv:e.toEnv};await(0,transactor_1.default)(tcbContracts.tcbCreateCopyEnvTaskContract,t);return{}}async function tcbDescribeExtensionsInstalled(e){const t={Action:"DescribeExtensionsInstalled",Version:"2018-06-08",Region:e.region,EnvIds:e.envIds,Flags:e.flags};return{envExtensions:(await(0,transactor_1.default)(tcbContracts.tcbDescribeExtensionsInstalledContract,t)).EnvExtensions.map(e=>({envId:e.EnvId,extensionId:e.ExtensionId,status:e.Status,taskInfo:e.TaskInfo,createTime:e.CreateTime}))}}exports.tcbGetUsers=tcbGetUsers,exports.tcbGetDbDistribution=tcbGetDbDistribution,exports.tcbGetStorageACL=tcbGetStorageACL,exports.tcbGetMonitorData=tcbGetMonitorData,exports.tcbDescribeCurveData=tcbDescribeCurveData,exports.tcbGetStorageACLTask=tcbGetStorageACLTask,exports.tcbModifyStorageACL=tcbModifyStorageACL,exports.tcbDescribeDatabaseACL=tcbDescribeDatabaseACL,exports.tcbModifyDatabaseACL=tcbModifyDatabaseACL,exports.tcbDatabaseMigrateImport=tcbDatabaseMigrateImport,exports.tcbDatabaseMigrateExport=tcbDatabaseMigrateExport,exports.tcbDatabaseMigrateQueryInfo=tcbDatabaseMigrateQueryInfo,exports.tcbModifySafeRule=tcbModifySafeRule,exports.tcbDescribeSafeRule=tcbDescribeSafeRule,exports.tcbCheckEnvId=tcbCheckEnvId,exports.tcbCreateEnvAndResource=tcbCreateEnvAndResource,exports.tcbDescribePackages=tcbDescribePackages,exports.tcbInqueryPrice=tcbInqueryPrice,exports.tcbCreateDeal=tcbCreateDeal,exports.tcbDescribePayInfo=tcbDescribePayInfo,exports.tcbQueryDeals=tcbQueryDeals,exports.tcbQueryAllDeals=tcbQueryAllDeals,exports.tcbCancelDeal=tcbCancelDeal,exports.tcbDeleteDeal=tcbDeleteDeal,exports.tcbCheckEnvPackageModify=tcbCheckEnvPackageModify,exports.tcbDescribeBillingInfo=tcbDescribeBillingInfo,exports.tcbDescribeNextExpireTime=tcbDescribeNextExpireTime,exports.tcbDescribeInvoiceAmount=tcbDescribeInvoiceAmount,exports.tcbDescribeInvoiceSubject=tcbDescribeInvoiceSubject,exports.tcbSetInvoiceSubject=tcbSetInvoiceSubject,exports.tcbDescribeInvoicePostInfo=tcbDescribeInvoicePostInfo,exports.tcbCreateInvoicePostInfo=tcbCreateInvoicePostInfo,exports.tcbModifyInvoicePostInfo=tcbModifyInvoicePostInfo,exports.tcbDeleteInvoicePostInfo=tcbDeleteInvoicePostInfo,exports.tcbCreateInvoice=tcbCreateInvoice,exports.tcbDescribeInvoiceList=tcbDescribeInvoiceList,exports.tcbDescribeInvoiceDetail=tcbDescribeInvoiceDetail,exports.tcbRevokeInvoice=tcbRevokeInvoice,exports.tcbDescribeAuthentification=tcbDescribeAuthentification,exports.tcbDescribeEnvResourceException=tcbDescribeEnvResourceException,exports.tcbResourceRecover=tcbResourceRecover,exports.tcbDescribeResourceRecoverJob=tcbDescribeResourceRecoverJob,exports.tcbDescribeVouchersInfoByDeal=tcbDescribeVouchersInfoByDeal,exports.tcbDescribeAllVouchersInfoByDeal=tcbDescribeAllVouchersInfoByDeal,exports.tcbDescribeAmountAfterDeduction=tcbDescribeAmountAfterDeduction,exports.tcbDescribeVouchersInfo=tcbDescribeVouchersInfo,exports.tcbDescribeAllVouchersInfo=tcbDescribeAllVouchersInfo,exports.tcbDescribeVoucherPlanAvailable=tcbDescribeVoucherPlanAvailable,exports.tcbApplyVoucher=tcbApplyVoucher,exports.tcbDescribeVoucherApplication=tcbDescribeVoucherApplication,exports.tcbDeleteVoucherApplication=tcbDeleteVoucherApplication,exports.tcbDescribeMonitorResource=tcbDescribeMonitorResource,exports.tcbDescribeMonitorPolicy=tcbDescribeMonitorPolicy,exports.tcbCreateMonitorPolicy=tcbCreateMonitorPolicy,exports.tcbDeleteMonitorPolicy=tcbDeleteMonitorPolicy,exports.tcbModifyMonitorPolicy=tcbModifyMonitorPolicy,exports.tcbDescribeMonitorCondition=tcbDescribeMonitorCondition,exports.tcbCreateMonitorCondition=tcbCreateMonitorCondition,exports.tcbDeleteMonitorCondition=tcbDeleteMonitorCondition,exports.tcbModifyMonitorCondition=tcbModifyMonitorCondition,exports.tcbDescribeDauData=tcbDescribeDauData,exports.tcbDescribeChangePay=tcbDescribeChangePay,exports.tcbDescribeRestoreHistory=tcbDescribeRestoreHistory,exports.tcbCommonServiceAPI=tcbCommonServiceAPI,exports.tcbCreatePostpayPackage=tcbCreatePostpayPackage,exports.tcbInqueryPostpayPrice=tcbInqueryPostpayPrice,exports.tcbDescribePostpayFreeQuotas=tcbDescribePostpayFreeQuotas,exports.tcbModifyStorageSafeRule=tcbModifyStorageSafeRule,exports.tcbDescribeStorageSafeRule=tcbDescribeStorageSafeRule,exports.tcbDescribeCDNChainTask=tcbDescribeCDNChainTask,exports.tcbDescribeLoginConfigs=tcbDescribeLoginConfigs,exports.tcbCreateLoginConfig=tcbCreateLoginConfig,exports.tcbUpdateLoginConfig=tcbUpdateLoginConfig,exports.tcbDescribeSecurityRule=tcbDescribeSecurityRule,exports.tcbModifySecurityRule=tcbModifySecurityRule,exports.tcbCreateStaticStore=tcbCreateStaticStore,exports.tcbDestroyStaticStore=tcbDestroyStaticStore,exports.tcbDescribeStaticStore=tcbDescribeStaticStore,exports.tcbDescribeEnvLimit=tcbDescribeEnvLimit,exports.tcbDescribeAccountInfoByPlatformId=tcbDescribeAccountInfoByPlatformId,exports.tcbDescribeEnvFreeQuota=tcbDescribeEnvFreeQuota,exports.tcbDescribeHostingDomain=tcbDescribeHostingDomain,exports.tcbDescribeCloudBaseRunResource=tcbDescribeCloudBaseRunResource,exports.tcbDescribeCloudBaseRunServers=tcbDescribeCloudBaseRunServers,exports.tcbCreateCloudBaseRunResource=tcbCreateCloudBaseRunResource,exports.tcbDescribeCloudBaseRunBuildServer=tcbDescribeCloudBaseRunBuildServer,exports.tcbDescribeCloudBaseRunServer=tcbDescribeCloudBaseRunServer,exports.tcbDescribeCloudBaseRunContainerSpec=tcbDescribeCloudBaseRunContainerSpec,exports.tcbCreateCloudBaseRunServerVersion=tcbCreateCloudBaseRunServerVersion,exports.tcbDescribeCloudBaseRunServerVersion=tcbDescribeCloudBaseRunServerVersion,exports.tcbEstablishCloudBaseRunServer=tcbEstablishCloudBaseRunServer,exports.tcbDeleteCloudBaseRunResource=tcbDeleteCloudBaseRunResource,exports.tcbDescribeCloudBaseRunPodList=tcbDescribeCloudBaseRunPodList,exports.tcbDescribeCloudBaseRunBuildLog=tcbDescribeCloudBaseRunBuildLog,exports.tcbDescribeCloudBaseBuildService=tcbDescribeCloudBaseBuildService,exports.tcbDescribeCloudBaseRunVersionException=tcbDescribeCloudBaseRunVersionException,exports.tcbModifyCloudBaseRunServerVersion=tcbModifyCloudBaseRunServerVersion,exports.tcbDescribeCloudBaseGWAPI=tcbDescribeCloudBaseGWAPI,exports.tcbDescribeCloudBaseRunBuildStages=tcbDescribeCloudBaseRunBuildStages,exports.tcbDescribeCloudBaseRunBuildSteps=tcbDescribeCloudBaseRunBuildSteps,exports.tcbDescribeCloudBaseRunBuildStepLog=tcbDescribeCloudBaseRunBuildStepLog,exports.tcbDeleteCloudBaseRunServerVersion=tcbDeleteCloudBaseRunServerVersion,exports.tcbDeleteCloudBaseRunImageRepo=tcbDeleteCloudBaseRunImageRepo,exports.tcbRollUpdateCloudBaseRunServerVersion=tcbRollUpdateCloudBaseRunServerVersion,exports.tcbModifyCloudBaseRunServerFlowConf=tcbModifyCloudBaseRunServerFlowConf,exports.tcbDeleteCloudBaseRunServer=tcbDeleteCloudBaseRunServer,exports.tcbDescribeCloudBaseCodeRepos=tcbDescribeCloudBaseCodeRepos,exports.tcbDescribeCloudBaseCodeBranch=tcbDescribeCloudBaseCodeBranch,exports.tcbCreateHostingDomain=tcbCreateHostingDomain,exports.tcbDescribePostpayPackageList=tcbDescribePostpayPackageList,exports.tcbQueryActivityPrice=tcbQueryActivityPrice,exports.tcbDeleteHostingDomain=tcbDeleteHostingDomain,exports.tcbCheckQualification=tcbCheckQualification,exports.tcbModifyHostingDomain=tcbModifyHostingDomain,exports.tcbCreateActivityDeal=tcbCreateActivityDeal,exports.tcbDescribeActivityGoods=tcbDescribeActivityGoods,exports.tcbInqueryPackagePrice=tcbInqueryPackagePrice,exports.tcbOnlineHostingDomain=tcbOnlineHostingDomain,exports.tcbDescribeEnvPostpayPackage=tcbDescribeEnvPostpayPackage,exports.tcbDescribePostpayQuotaLimit=tcbDescribePostpayQuotaLimit,exports.tcbUpdatePostpayQuotaLimitStatus=tcbUpdatePostpayQuotaLimitStatus,exports.tcbUpdatePostpayQuotaLimit=tcbUpdatePostpayQuotaLimit,exports.tcbUpdateScfConfig=tcbUpdateScfConfig,exports.tcbCreateInstallExtensionTask=tcbCreateInstallExtensionTask,exports.tcbCreateUninstallExtensionTask=tcbCreateUninstallExtensionTask,exports.tcbDescribeExtensionInstalled=tcbDescribeExtensionInstalled,exports.tcbDescribeExtensionTaskStatus=tcbDescribeExtensionTaskStatus,exports.tcbDescribeExtensionTemplates=tcbDescribeExtensionTemplates,exports.tcbDescribeExtensionUpgrade=tcbDescribeExtensionUpgrade,exports.tcbDescribeExtensions=tcbDescribeExtensions,exports.tcbCreateUpgradeExtensionTask=tcbCreateUpgradeExtensionTask,exports.tcbDescribeCloudBaseRunOperationDetails=tcbDescribeCloudBaseRunOperationDetails,exports.tcbDescribeCloudBaseRunVersionSnapshot=tcbDescribeCloudBaseRunVersionSnapshot,exports.tcbDescribeQcloudScene=tcbDescribeQcloudScene,exports.tcbDescribeSmsQuotas=tcbDescribeSmsQuotas,exports.tcbDescribeSmsAttrInfo=tcbDescribeSmsAttrInfo,exports.tcbDescribeTcbBalance=tcbDescribeTcbBalance,exports.tcbDescribeSmsRecords=tcbDescribeSmsRecords,exports.tcbDescribeCloudBaseRunServiceDomain=tcbDescribeCloudBaseRunServiceDomain,exports.tcbModifyEnv=tcbModifyEnv,exports.tcbDescribeWxCloudBaseRunEnvs=tcbDescribeWxCloudBaseRunEnvs,exports.tcbDescribeWxCloudBaseRunSubNets=tcbDescribeWxCloudBaseRunSubNets,exports.tcbRefundPostpaidPackage=tcbRefundPostpaidPackage,exports.tcbQueryPostpaidPackageDeals=tcbQueryPostpaidPackageDeals,exports.tcbSearchClsLog=tcbSearchClsLog,exports.tcbDescribeAuditRule=tcbDescribeAuditRule,exports.tcbDescribeCollections=tcbDescribeCollections,exports.tcbCreateAuditRules=tcbCreateAuditRules,exports.tcbDeleteAuditRule=tcbDeleteAuditRule,exports.tcbModifyAuditRule=tcbModifyAuditRule,exports.tcbDescribeAuditResults=tcbDescribeAuditResults,exports.tcbUnfreezeSecurityAuditRecord=tcbUnfreezeSecurityAuditRecord,exports.tcbDescribeSecurityAuditConfig=tcbDescribeSecurityAuditConfig,exports.tcbDeleteSecurityAuditConfig=tcbDeleteSecurityAuditConfig,exports.tcbCreateSecurityAuditConfig=tcbCreateSecurityAuditConfig,exports.tcbDescribeTriggerServiceParameters=tcbDescribeTriggerServiceParameters,exports.tcbCreateTriggerConfigs=tcbCreateTriggerConfigs,exports.tcbDescribeTriggerConfigs=tcbDescribeTriggerConfigs,exports.tcbUpdateTriggerConfig=tcbUpdateTriggerConfig,exports.tcbDeleteTriggerConfigs=tcbDeleteTriggerConfigs,exports.tcbCreateCopyEnvTask=tcbCreateCopyEnvTask,exports.tcbDescribeExtensionsInstalled=tcbDescribeExtensionsInstalled;
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.$multiType=exports.$arrayOf=exports.$optional=exports.Validator=exports.getType=void 0;const eventemitter3_1=require("eventemitter3");function getType(t){return Object.prototype.toString.call(t).slice(8,-1).toLowerCase()}exports.getType=getType;class Validator extends eventemitter3_1.EventEmitter{constructor(t,e){super(),this.typeName=t||"#",this.structure=e}validThrows(t){const e=Date.now();try{validJSON(t,this.structure,this.typeName),this.emit("success",t)}catch(e){throw this.emit("fail",t,e),e}finally{"undefined"!=typeof window&&(window.log&&window.log.i?log.i("[validator]",this.typeName,"cost time",Date.now()-e):console.log("[validator]",this.typeName,"cost time",Date.now()-e))}}}exports.Validator=Validator;class _S{constructor(t,e){this.symbol=t,this.o=e}}const _optionalSymbol=Symbol(),_arrayOfSymbol=Symbol(),_multiTypeSymbol=Symbol();function $optional(t){return new _S(_optionalSymbol,t)}function $arrayOf(t){return new _S(_arrayOfSymbol,t)}function $multiType(...t){return new _S(_multiTypeSymbol,t)}function validJSON(t,e,r="#"){if("function"==typeof e)return e(t,r,validJSON);if(e instanceof _S){switch(e.symbol){case _arrayOfSymbol:return validJSON(t,(t,r,o)=>{if(!Array.isArray(t))throw new Error(`Type not equal, at ${r}, ${getType(t)} !== array`);t.every((t,a)=>(o(t,e.o,r+"["+a+"]"),!0))},r);case _optionalSymbol:return validJSON(t,(t,r,o)=>{o(t,$multiType(void 0,null,e.o),r)},r);case _multiTypeSymbol:return validJSON(t,(t,r,o)=>{const a=e.o.length;let i=0;for(let n=0;n<a;n++)try{return void o(t,e.o[n],r)}catch(t){if(i+=1,i>=a)throw new Error(t)}},r);default:throw new Error("Unknown validation schema")}}const o=getType(t),a=getType(e);if(o!==a)throw new Error(`Type not equal, at ${r}, ${o} !== ${a}`);if((Array.isArray(t)?1:-1)*(Array.isArray(e)?1:-1)<0)throw new Error(`Type array not equal, at ${r}, ${Array.isArray(t)} !== ${Array.isArray(e)}`);if(Array.isArray(t)){if(e.length>t.length)throw new Error(`Array length < expected size, at ${r},  ${t.length} < ${e.length}`);for(let o=0;o<e.length;o++)validJSON(t[o],e[o],r+"["+o+"]");return}if(null==t){if(t!==e)throw new Error(`Type not equal, at ${r}, ${t} !== ${e}`);return}if("object"!==o){if(o!==a)throw new Error(`Type not equal, at ${r}, ${o} !== ${a}`);return}const i=Object.keys(e);for(const o of i)validJSON(t[o],e[o],r+"."+o)}exports.$optional=$optional,exports.$arrayOf=$arrayOf,exports.$multiType=$multiType;
}(require("licia/lazyImport")(require), require)
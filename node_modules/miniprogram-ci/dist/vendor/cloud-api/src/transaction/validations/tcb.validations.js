!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.tcbResourceRecoverOutputValidation=exports.tcbDescribeEnvResourceExceptionOutputValidation=exports.tcbDescribeAuthentificationOutputValidation=exports.tcbRevokeInvoiceOutputValidation=exports.tcbDescribeInvoiceDetailOutputValidation=exports.tcbDescribeInvoiceListOutputValidation=exports.tcbInvoiceBasicInfo=exports.tcbCreateInvoiceOutputValidation=exports.tcbDeleteInvoicePostInfoOutputValidation=exports.tcbModifyInvoicePostInfoOutputValidation=exports.tcbCreateInvoicePostInfoOutputValidation=exports.tcbDescribeInvoicePostInfoOutputValidation=exports.tcbInvoicePostInfoValidation=exports.tcbSetInvoiceSubjectOutputValidation=exports.tcbDescribeInvoiceSubjectOutputValidation=exports.tcbInvoiceVATSpecialValidation=exports.tcbInvoiceVATGeneralValidation=exports.tcbDescribeInvoiceAmountOutputValidation=exports.tcbDescribeNextExpireTimeOutputValidation=exports.tcbDescribeBillingInfoOutputValidation=exports.tcbOrderInfoValidation=exports.tcbCheckEnvPackageModifyOutputValidation=exports.tcbDeleteDealOutputValidation=exports.tcbCancelDealOutputValidation=exports.tcbQueryDealsOutputValidation=exports.tcbDescribePayInfoOutputValidation=exports.tcbCreateDealOutputValidation=exports.tcbInqueryPriceOutputValidation=exports.tcbDescribePackagesOutputValidation=exports.tcbDescribeEnvAccountCircleOutputValidation=exports.tcbCreateEnvAndResourceOutputValidation=exports.tcbCheckEnvIdOutputValidation=exports.tcbDescribeSafeRuleOutputValidation=exports.tcbModifySafeRuleOutputValidation=exports.tcbDatabaseMigrateQueryInfoOutputValidation=exports.tcbDatabaseMigrateExportOutputValidation=exports.tcbDatabaseMigrateImportOutputValidation=exports.tcbModifyDatabaseACLOutputValidation=exports.tcbDescribeDatabaseACLOutputValidation=exports.tcbDescribeStorageACLTaskOutputValidation=exports.tcbModifyStorageACLOutputValidation=exports.tcbDescribeStorageACLOutputValidation=exports.tcbDescribeCurveDataOutputValidation=exports.tcbDescribeMonitorDataOutputValidation=exports.tcbDescribeDbDistributionOutputValidation=exports.tcbDescribeQuotaDataOutputValidation=exports.tcbDescribeStatDataOutputValidation=exports.tcbGetResourceLimitOutputValidation=exports.tcbGetUsersOutputValidation=exports.tcbGetEnvironmentsOutputValidation=void 0,exports.tcbDescribeCloudBaseRunPodListOutputValidation=exports.tcbDeleteCloudBaseRunResourceOutputValidation=exports.tcbEstablishCloudBaseRunServerOutputValidation=exports.tcbDescribeCloudBaseRunServerVersionOutputValidation=exports.tcbCreateCloudBaseRunServerVersionOutputValidation=exports.tcbDescribeCloudBaseRunContainerSpecOutputValidation=exports.tcbDescribeCloudBaseRunServerOutputValidation=exports.tcbDescribeCloudBaseRunBuildServerOutputValidation=exports.tcbCreateCloudBaseRunResourceOutputValidation=exports.tcbDescribeCloudBaseRunServersOutputValidation=exports.tcbDescribeCloudBaseRunResourceOutputValidation=exports.tcbDescribeHostingDomainOutputValidation=exports.tcbDescribeEnvFreeQuotaOutputValidation=exports.tcbDescribeAccountInfoByPlatformIdOutputValidation=exports.tcbDescribeEnvLimitOutputValidation=exports.tcbDescribeStaticStoreOutputValidation=exports.tcbDestroyStaticStoreOutputValidation=exports.tcbCreateStaticStoreOutputValidation=exports.tcbModifySecurityRuleOutputValidation=exports.tcbDescribeSecurityRuleOutputValidation=exports.tcbUpdateLoginConfigOutputValidation=exports.tcbCreateLoginConfigOutputValidation=exports.tcbDescribeLoginConfigsOutputValidation=exports.tcbDescribeCDNChainTaskOutputValidation=exports.tcbDescribeStorageSafeRuleOutputValidation=exports.tcbModifyStorageSafeRuleOutputValidation=exports.tcbDescribePostpayFreeQuotasOutputValidation=exports.tcbInqueryPostpayPriceOutputValidation=exports.tcbCreatePostpayPackageOutputValidation=exports.tcbCommonServiceAPIOutputValidation=exports.tcbDescribeRestoreHistoryOutputValidation=exports.tcbDescribeChangePayOutputValidation=exports.tcbDescribeDauDataOutputValidation=exports.tcbModifyMonitorConditionOutputValidation=exports.tcbDeleteMonitorConditionOutputValidation=exports.tcbCreateMonitorConditionOutputValidation=exports.tcbDescribeMonitorConditionOutputValidation=exports.tcbModifyMonitorPolicyOutputValidation=exports.tcbDeleteMonitorPolicyOutputValidation=exports.tcbCreateMonitorPolicyOutputValidation=exports.tcbDescribeMonitorPolicyOutputValidation=exports.tcbDescribeMonitorResourceOutputValidation=exports.tcbDeleteVoucherApplicationOutputValidation=exports.tcbDescribeVoucherApplicationOutputValidation=exports.tcbApplyVoucherOutputValidation=exports.tcbDescribeVoucherPlanAvailableOutputValidation=exports.tcbDescribeVouchersInfoOutputValidation=exports.tcbDescribeAmountAfterDeductionOutputValidation=exports.tcbDescribeVouchersInfoByDealOutputValidation=exports.tcbDescribeResourceRecoverJobOutputValidation=void 0,exports.tcbRefundPostpaidPackageOutputValidation=exports.tcbDescribeWxCloudBaseRunSubNetsOutputValidation=exports.tcbDescribeWxCloudBaseRunEnvsOutputValidation=exports.tcbModifyEnvOutputValidation=exports.tcbDescribeCloudBaseRunServiceDomainOutputValidation=exports.tcbDescribeSmsRecordsOutputValidation=exports.tcbDescribeTcbBalanceOutputValidation=exports.tcbDescribeSmsAttrInfoOutputValidation=exports.tcbDescribeSmsQuotasOutputValidation=exports.tcbDescribeQcloudSceneOutputValidation=exports.tcbDescribeCloudBaseRunVersionSnapshotOutputValidation=exports.tcbDescribeCloudBaseRunOperationDetailsOutputValidation=exports.tcbCreateUpgradeExtensionTaskOutputValidation=exports.tcbDescribeExtensionsOutputValidation=exports.tcbDescribeExtensionUpgradeOutputValidation=exports.tcbDescribeExtensionTemplatesOutputValidation=exports.tcbDescribeExtensionTaskStatusOutputValidation=exports.tcbDescribeExtensionInstalledOutputValidation=exports.tcbCreateUninstallExtensionTaskOutputValidation=exports.tcbCreateInstallExtensionTaskOutputValidation=exports.tcbUpdateScfConfigOutputValidation=exports.tcbUpdatePostpayQuotaLimitOutputValidation=exports.tcbUpdatePostpayQuotaLimitStatusOutputValidation=exports.tcbDescribePostpayQuotaLimitOutputValidation=exports.tcbDescribeEnvPostpayPackageOutputValidation=exports.tcbInqueryPackagePriceOutputValidation=exports.tcbDescribeActivityGoodsOutputValidation=exports.tcbCreateActivityDealOutputValidation=exports.tcbCheckQualificationOutputValidation=exports.tcbQueryActivityPriceOutputValidation=exports.tcbDescribePostpayPackageListOutputValidation=exports.tcbOnlineHostingDomainOutputValidation=exports.tcbModifyHostingDomainOutputValidation=exports.tcbDeleteHostingDomainOutputValidation=exports.tcbCreateHostingDomainOutputValidation=exports.tcbDescribeCloudBaseCodeBranchOutputValidation=exports.tcbDescribeCloudBaseCodeReposOutputValidation=exports.tcbDeleteCloudBaseRunServerOutputValidation=exports.tcbModifyCloudBaseRunServerFlowConfOutputValidation=exports.tcbRollUpdateCloudBaseRunServerVersionOutputValidation=exports.tcbDeleteCloudBaseRunImageRepoOutputValidation=exports.tcbDeleteCloudBaseRunServerVersionOutputValidation=exports.tcbDescribeCloudBaseRunBuildStepLogOutputValidation=exports.tcbDescribeCloudBaseRunBuildStepsOutputValidation=exports.tcbDescribeCloudBaseRunBuildStagesOutputValidation=exports.tcbDescribeCloudBaseGWAPIOutputValidation=exports.tcbModifyCloudBaseRunServerVersionOutputValidation=exports.tcbDescribeCloudBaseRunVersionExceptionOutputValidation=exports.tcbDescribeCloudBaseBuildServiceOutputValidation=exports.tcbDescribeCloudBaseRunBuildLogOutputValidation=void 0,exports.tcbDescribeExtensionsInstalledOutputValidation=exports.tcbCreateCopyEnvTaskOutputValidation=exports.tcbDeleteTriggerConfigsOutputValidation=exports.tcbUpdateTriggerConfigOutputValidation=exports.tcbDescribeTriggerConfigsOutputValidation=exports.tcbCreateTriggerConfigsOutputValidation=exports.tcbDescribeTriggerServiceParametersOutputValidation=exports.tcbCreateSecurityAuditConfigOutputValidation=exports.tcbDeleteSecurityAuditConfigOutputValidation=exports.tcbDescribeSecurityAuditConfigOutputValidation=exports.tcbUnfreezeSecurityAuditRecordOutputValidation=exports.tcbDescribeAuditResultsOutputValidation=exports.tcbModifyAuditRuleOutputValidation=exports.tcbDeleteAuditRuleOutputValidation=exports.tcbCreateAuditRulesOutputValidation=exports.tcbDescribeCollectionsOutputValidation=exports.tcbDescribeAuditRuleOutputValidation=exports.tcbSearchClsLogOutputValidation=exports.tcbQueryPostpaidPackageDealsOutputValidation=void 0;const tslib_1=require("tslib"),v=tslib_1.__importStar(require("../../utils/validator")),common=tslib_1.__importStar(require("./validations"));exports.tcbGetEnvironmentsOutputValidation=Object.assign({},common.commonOutputValidation,{EnvList:v.$arrayOf(common.envInfoValidation)}),exports.tcbGetUsersOutputValidation=Object.assign({},common.commonOutputValidation,{Total:1,Users:v.$arrayOf(common.userInfoValidation)}),exports.tcbGetResourceLimitOutputValidation=Object.assign({},common.commonOutputValidation,{Database:v.$arrayOf({CapacityLimit:1,ConnectionLimit:1,CollectionLimit:1,IndexLimit:1,ReadLimit:common.limitInfoValidation,WriteLimit:common.limitInfoValidation,QPSLimit:1}),Storage:v.$arrayOf({CapacityLimit:1,DownloadLimit:common.limitInfoValidation,UploadLimit:common.limitInfoValidation,CdnOriginFlowLimit:common.limitInfoValidation}),Function:v.$arrayOf({NumberLimit:1,CallLimit:common.limitInfoValidation,ResourceUsageLimit:common.limitInfoValidation,ConcurrentLimit:1,OutboundTrafficLimit:common.limitInfoValidation})}),exports.tcbDescribeStatDataOutputValidation=Object.assign({},common.commonOutputValidation,{Resources:v.$arrayOf({ResourceType:"",MaxSize:v.$optional(1),CurSize:v.$optional(1)})}),exports.tcbDescribeQuotaDataOutputValidation=Object.assign({},common.commonOutputValidation,{Value:1}),exports.tcbDescribeDbDistributionOutputValidation=Object.assign({},common.commonOutputValidation,{Collections:v.$arrayOf({CollectionName:"",DocCount:1})}),exports.tcbDescribeMonitorDataOutputValidation=Object.assign({},common.commonOutputValidation,{StartTime:"",EndTime:"",MetricName:"",Period:1,Values:v.$arrayOf(1)}),exports.tcbDescribeCurveDataOutputValidation=Object.assign({},common.commonOutputValidation,{StartTime:"",EndTime:"",MetricName:"",Period:1,Values:v.$arrayOf(1),Time:v.$arrayOf(1)}),exports.tcbDescribeStorageACLOutputValidation=Object.assign({},common.commonOutputValidation,{AclTag:""}),exports.tcbModifyStorageACLOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeStorageACLTaskOutputValidation=Object.assign({},common.commonOutputValidation,{Status:""}),exports.tcbDescribeDatabaseACLOutputValidation=Object.assign({},common.commonOutputValidation,{AclTag:""}),exports.tcbModifyDatabaseACLOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDatabaseMigrateImportOutputValidation=Object.assign({},common.commonOutputValidation,{JobId:1}),exports.tcbDatabaseMigrateExportOutputValidation=Object.assign({},common.commonOutputValidation,{JobId:1}),exports.tcbDatabaseMigrateQueryInfoOutputValidation=Object.assign({},common.commonOutputValidation,{Status:"",RecordSuccess:v.$optional(1),RecordFail:v.$optional(1),ErrorMsg:v.$optional(""),FileUrl:v.$optional("")}),exports.tcbModifySafeRuleOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeSafeRuleOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCheckEnvIdOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCreateEnvAndResourceOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeEnvAccountCircleOutputValidation=Object.assign({},common.commonOutputValidation,{StartTime:"",EndTime:""}),exports.tcbDescribePackagesOutputValidation=Object.assign({},common.commonOutputValidation,{Packages:v.$arrayOf({PackageId:"",Name:v.$optional(""),Desc:v.$optional(""),Detail:v.$optional(""),Pid:v.$optional(""),PackageType:v.$optional("")})}),exports.tcbInqueryPriceOutputValidation=Object.assign({},common.commonOutputValidation,{Price:1}),exports.tcbCreateDealOutputValidation=Object.assign({},common.commonOutputValidation,{TranId:"",ModifyFailReason:v.$optional(v.$arrayOf({ResourceName:v.$optional(""),IndexName:v.$optional(""),IndexShowName:v.$optional(""),IndexUseAmount:v.$optional(1),IndexUseUnit:v.$optional("")}))}),exports.tcbDescribePayInfoOutputValidation=Object.assign({},common.commonOutputValidation,{PayCode:"",AppId:"",IsFreeOrRefundDeal:v.$optional(!0)}),exports.tcbQueryDealsOutputValidation=Object.assign({},common.commonOutputValidation,{Total:1,Deals:v.$arrayOf({TranId:"",DealOwner:"",CreateTime:"",PackageId:"",DealStatus:1,DealCost:1,EnvId:"",PayTime:"",TimeSpan:1,Price:1,PayMode:1,TimeUnit:"",RefundAmount:1,DealStatusDes:v.$optional(""),VoucherDecline:v.$optional(1),HasReturnPayCode:v.$optional(!0),Source:v.$optional("")})}),exports.tcbCancelDealOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDeleteDealOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCheckEnvPackageModifyOutputValidation=Object.assign({},common.commonOutputValidation,{AllowToModify:!0,InvoiceOverlimit:v.$optional({IsAmountOverlimit:!0,RefundAmount:1,InvoiceAmount:1}),QuotaOverlimitList:v.$optional(v.$arrayOf({ResourceName:"",QuotaName:v.$optional(""),QuotaChName:v.$optional(""),QuotaUsaged:1,Unit:v.$optional(""),Comments:v.$optional("")})),ForceToModify:v.$optional(!0)}),exports.tcbOrderInfoValidation={TranId:"",PackageId:"",TranType:"",TranStatus:"",UpdateTime:"",CreateTime:"",PayMode:""},exports.tcbDescribeBillingInfoOutputValidation=Object.assign({},common.commonOutputValidation,{EnvBillingInfoList:v.$optional(v.$arrayOf({EnvId:"",PackageId:"",IsAutoRenew:!0,Status:"",PayMode:"",IsolatedTime:"",ExpireTime:"",CreateTime:"",UpdateTime:"",IsAlwaysFree:!0,PaymentChannel:v.$optional(""),OrderInfo:v.$optional(exports.tcbOrderInfoValidation),FreeQuota:v.$optional("")}))}),exports.tcbDescribeNextExpireTimeOutputValidation=Object.assign({},common.commonOutputValidation,{ExpireTime:""}),exports.tcbDescribeInvoiceAmountOutputValidation=Object.assign({},common.commonOutputValidation,{InvoicedAmount:1,AvailableAmount:1}),exports.tcbInvoiceVATGeneralValidation={TaxPayerType:"",TaxPayerNumber:"",BankDeposit:"",BankAccount:"",RegisterAddress:"",RegisterPhone:""},exports.tcbInvoiceVATSpecialValidation={TaxPayerNumber:"",BankDeposit:"",BankAccount:"",RegisterAddress:"",RegisterPhone:""},exports.tcbDescribeInvoiceSubjectOutputValidation=Object.assign({},common.commonOutputValidation,{UserType:"",InvoiceHead:"",VATGeneral:v.$optional(exports.tcbInvoiceVATGeneralValidation),VATSpecial:v.$optional(exports.tcbInvoiceVATSpecialValidation),Status:"",StatusMessage:v.$optional("")}),exports.tcbSetInvoiceSubjectOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbInvoicePostInfoValidation={PostId:"",Contact:"",Province:"",City:"",Address:"",PostalCode:"",Cellphone:""},exports.tcbDescribeInvoicePostInfoOutputValidation=Object.assign({},common.commonOutputValidation,{PostInfoList:v.$optional(v.$arrayOf(exports.tcbInvoicePostInfoValidation))}),exports.tcbCreateInvoicePostInfoOutputValidation=Object.assign({},common.commonOutputValidation,{PostId:""}),exports.tcbModifyInvoicePostInfoOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDeleteInvoicePostInfoOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCreateInvoiceOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbInvoiceBasicInfo={InvoiceId:"",UserType:"",Amount:1,Status:"",InvoiceTime:""},exports.tcbDescribeInvoiceListOutputValidation=Object.assign({},common.commonOutputValidation,{Total:1,InvoiceList:v.$arrayOf(exports.tcbInvoiceBasicInfo)}),exports.tcbDescribeInvoiceDetailOutputValidation=Object.assign({},common.commonOutputValidation,{Amount:1,InvoiceNumber:"",UserType:"",InvoiceHead:"",VATGeneral:v.$optional(exports.tcbInvoiceVATGeneralValidation),VATSpecial:v.$optional(exports.tcbInvoiceVATSpecialValidation),PostInfo:exports.tcbInvoicePostInfoValidation,Status:"",PostCompany:v.$optional(""),PostNumber:v.$optional(""),InvoiceTime:"",Remark:v.$optional("")}),exports.tcbRevokeInvoiceOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeAuthentificationOutputValidation=Object.assign({},common.commonOutputValidation,{AuthentificationType:"",AuthentificationName:v.$optional("")}),exports.tcbDescribeEnvResourceExceptionOutputValidation=Object.assign({},common.commonOutputValidation,{Storage:v.$optional(v.$arrayOf(common.storageExceptionValidation)),LogService:v.$optional(v.$arrayOf(common.logServiceExceptionValidation))}),exports.tcbResourceRecoverOutputValidation=Object.assign({},common.commonOutputValidation,{Results:v.$arrayOf(common.recoverResultValidation)}),exports.tcbDescribeResourceRecoverJobOutputValidation=Object.assign({},common.commonOutputValidation,{JobStatusSet:v.$optional(v.$arrayOf(common.recoverJobStatusValidation))}),exports.tcbDescribeVouchersInfoByDealOutputValidation=Object.assign({},common.commonOutputValidation,{Vouchers:v.$arrayOf(common.voucherValidation)}),exports.tcbDescribeAmountAfterDeductionOutputValidation=Object.assign({},common.commonOutputValidation,{AmountAfterDeduction:1}),exports.tcbDescribeVouchersInfoOutputValidation=Object.assign({},common.commonOutputValidation,{Vouchers:v.$arrayOf(common.voucherValidation)}),exports.tcbDescribeVoucherPlanAvailableOutputValidation=Object.assign({},common.commonOutputValidation,{VoucherPlans:v.$optional(v.$arrayOf(1)),Qualified:!0}),exports.tcbApplyVoucherOutputValidation=Object.assign({},common.commonOutputValidation,{ApplicationId:""}),exports.tcbDescribeVoucherApplicationOutputValidation=Object.assign({},common.commonOutputValidation,{Applications:v.$optional(v.$arrayOf(common.voucherApplication))}),exports.tcbDeleteVoucherApplicationOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeMonitorResourceOutputValidation=Object.assign({},common.commonOutputValidation,{Data:v.$arrayOf(common.monitorResourceValidation)}),exports.tcbDescribeMonitorPolicyOutputValidation=Object.assign({},common.commonOutputValidation,{Data:v.$arrayOf(common.monitorPolicyInfoValidation)}),exports.tcbCreateMonitorPolicyOutputValidation=Object.assign({},common.commonOutputValidation,{Id:1}),exports.tcbDeleteMonitorPolicyOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbModifyMonitorPolicyOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeMonitorConditionOutputValidation=Object.assign({},common.commonOutputValidation,{Data:v.$arrayOf(common.monitorConditionInfoValidation)}),exports.tcbCreateMonitorConditionOutputValidation=Object.assign({},common.commonOutputValidation,{Id:1}),exports.tcbDeleteMonitorConditionOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbModifyMonitorConditionOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeDauDataOutputValidation=Object.assign({},common.commonOutputValidation,{DauDataList:v.$arrayOf(common.dauStatDataValidation)}),exports.tcbDescribeChangePayOutputValidation=Object.assign({},common.commonOutputValidation,{Changeable:!0,FailReason:"",RefundAmount:1,RefundType:1}),exports.tcbDescribeRestoreHistoryOutputValidation=Object.assign({},common.commonOutputValidation,{Total:1,Data:v.$arrayOf(common.restoreHistoryItemValidation)}),exports.tcbCommonServiceAPIOutputValidation=Object.assign({},common.commonOutputValidation,{JSONResp:""}),exports.tcbCreatePostpayPackageOutputValidation=Object.assign({},common.commonOutputValidation,{TranId:""}),exports.tcbInqueryPostpayPriceOutputValidation=Object.assign({},common.commonOutputValidation,{PriceInfoList:v.$arrayOf(common.priceInfoValidation)}),exports.tcbDescribePostpayFreeQuotasOutputValidation=Object.assign({},common.commonOutputValidation,{FreequotaInfoList:v.$optional(v.$arrayOf(common.freequotaInfoValidation))}),exports.tcbModifyStorageSafeRuleOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeStorageSafeRuleOutputValidation=Object.assign({},common.commonOutputValidation,{AclTag:"",Rule:""}),exports.tcbDescribeCDNChainTaskOutputValidation=Object.assign({},common.commonOutputValidation,{Status:""}),exports.tcbDescribeLoginConfigsOutputValidation=Object.assign({},common.commonOutputValidation,{ConfigList:v.$arrayOf(common.loginConfigItemValidation)}),exports.tcbCreateLoginConfigOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbUpdateLoginConfigOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeSecurityRuleOutputValidation=Object.assign({},common.commonOutputValidation,{AclTag:"",Rule:v.$optional("")}),exports.tcbModifySecurityRuleOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCreateStaticStoreOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional("")}),exports.tcbDestroyStaticStoreOutputValidation=Object.assign({},common.commonOutputValidation,{Result:""}),exports.tcbDescribeStaticStoreOutputValidation=Object.assign({},common.commonOutputValidation,{Data:v.$optional(v.$arrayOf(common.staticStoreInfoValidation))}),exports.tcbDescribeEnvLimitOutputValidation=Object.assign({},common.commonOutputValidation,{MaxEnvNum:1,CurrentEnvNum:1,MaxFreeEnvNum:1,CurrentFreeEnvNum:1,MaxDeleteTotal:1,CurrentDeleteTotal:1,MaxDeleteMonthly:1,CurrentDeleteMonthly:1}),exports.tcbDescribeAccountInfoByPlatformIdOutputValidation=Object.assign({},common.commonOutputValidation,{Uin:"",NickName:"",IsAuth:!0}),exports.tcbDescribeEnvFreeQuotaOutputValidation=Object.assign({},common.commonOutputValidation,{QuotaItems:v.$optional(v.$arrayOf(common.postpayEnvQuotaValidation))}),exports.tcbDescribeHostingDomainOutputValidation=Object.assign({},common.commonOutputValidation,{TotalCount:1,DomainSet:v.$optional(v.$arrayOf(common.hostingDomainValidation))}),exports.tcbDescribeCloudBaseRunResourceOutputValidation=Object.assign({},common.commonOutputValidation,{ClusterStatus:v.$optional(""),VirtualClusterId:v.$optional(""),VpcId:v.$optional(""),Region:v.$optional(""),SubnetIds:v.$optional(v.$arrayOf(common.cloudBaseRunVpcSubnetValidation))}),exports.tcbDescribeCloudBaseRunServersOutputValidation=Object.assign({},common.commonOutputValidation,{TotalCount:1,CloudBaseRunServerSet:v.$optional(v.$arrayOf(common.cloudBaseRunServerItemValidation))}),exports.tcbCreateCloudBaseRunResourceOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional("")}),exports.tcbDescribeCloudBaseRunBuildServerOutputValidation=Object.assign({},common.commonOutputValidation,{PackageRepositoryId:v.$optional(1),PackageRepositoryName:v.$optional(""),ImageNamespace:v.$optional(""),ProjectGlobalKey:v.$optional(""),ProjectToken:v.$optional(""),ProjectName:v.$optional(""),TeamGlobalKey:v.$optional(""),ProjectId:v.$optional(1)}),exports.tcbDescribeCloudBaseRunServerOutputValidation=Object.assign({},common.commonOutputValidation,{TotalCount:v.$optional(1),VersionItems:v.$optional(v.$arrayOf(common.cloudBaseRunServerVersionItemValidation)),ServerName:v.$optional(""),IsPublic:v.$optional(!0),ImageRepo:v.$optional(""),TrafficType:v.$optional("")}),exports.tcbDescribeCloudBaseRunContainerSpecOutputValidation=Object.assign({},common.commonOutputValidation,{ContainerStandards:v.$arrayOf(common.cloudBaseRunContainerStandardValidation)}),exports.tcbCreateCloudBaseRunServerVersionOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional(""),VersionName:v.$optional("")}),exports.tcbDescribeCloudBaseRunServerVersionOutputValidation=Object.assign({},common.commonOutputValidation,{VersionName:"",Remark:v.$optional(""),DockerfilePath:v.$optional(""),BuildDir:v.$optional(""),Cpu:1,Mem:1,MinNum:1,MaxNum:1,PolicyType:"",PolicyThreshold:1,EnvParams:v.$optional(""),CreatedTime:"",UpdatedTime:"",VersionIP:v.$optional(""),VersionPort:v.$optional(1),Status:v.$optional(""),PackageName:v.$optional(""),PackageVersion:v.$optional(""),UploadType:v.$optional(""),RepoType:v.$optional(""),Repo:v.$optional(""),Branch:v.$optional(""),ServerName:v.$optional(""),IsPublic:v.$optional(!0),VpcId:v.$optional(""),SubnetIds:v.$optional(v.$arrayOf("")),CustomLogs:v.$optional(""),ContainerPort:v.$optional(1),InitialDelaySeconds:v.$optional(1),ImageUrl:v.$optional("")}),exports.tcbEstablishCloudBaseRunServerOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDeleteCloudBaseRunResourceOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional("")}),exports.tcbDescribeCloudBaseRunPodListOutputValidation=Object.assign({},common.commonOutputValidation,{Offset:1,Limit:1,TotalCount:1,PodList:v.$optional(v.$arrayOf(common.cloudBaseRunVersionPodValidation))}),exports.tcbDescribeCloudBaseRunBuildLogOutputValidation=Object.assign({},common.commonOutputValidation,{Log:v.$optional(common.cloudBaseRunBuildLogValidation)}),exports.tcbDescribeCloudBaseBuildServiceOutputValidation=Object.assign({},common.commonOutputValidation,{UploadUrl:"",PackageName:"",PackageVersion:""}),exports.tcbDescribeCloudBaseRunVersionExceptionOutputValidation=Object.assign({},common.commonOutputValidation,{Status:"",ExceptionInfo:v.$optional(""),Advice:v.$optional(common.cloudRunExceptionAdviceValidation),ErrorType:v.$optional("")}),exports.tcbModifyCloudBaseRunServerVersionOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional("")}),exports.tcbDescribeCloudBaseGWAPIOutputValidation=Object.assign({},common.commonOutputValidation,{APISet:v.$optional(v.$arrayOf(common.cloudBaseGwapiValidation)),EnableService:!0,Total:v.$optional(1),Offset:v.$optional(1),Limit:v.$optional(1)}),exports.tcbDescribeCloudBaseRunBuildStagesOutputValidation=Object.assign({},common.commonOutputValidation,{Stages:v.$optional(common.cloudBaseRunBuildStagesValidation)}),exports.tcbDescribeCloudBaseRunBuildStepsOutputValidation=Object.assign({},common.commonOutputValidation,{Steps:v.$optional(common.cloudBaseRunBuildStagesValidation)}),exports.tcbDescribeCloudBaseRunBuildStepLogOutputValidation=Object.assign({},common.commonOutputValidation,{Log:v.$optional(common.cloudBaseRunBuildLogValidation)}),exports.tcbDeleteCloudBaseRunServerVersionOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional("")}),exports.tcbDeleteCloudBaseRunImageRepoOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbRollUpdateCloudBaseRunServerVersionOutputValidation=Object.assign({},common.commonOutputValidation,{Result:"",VersionName:v.$optional("")}),exports.tcbModifyCloudBaseRunServerFlowConfOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional("")}),exports.tcbDeleteCloudBaseRunServerOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$optional("")}),exports.tcbDescribeCloudBaseCodeReposOutputValidation=Object.assign({},common.commonOutputValidation,{RepoList:v.$optional(v.$arrayOf(common.cloudBaseCodeRepoNameValidation)),PageNumber:v.$optional(1),PageSize:v.$optional(1),IsFinished:v.$optional(!0)}),exports.tcbDescribeCloudBaseCodeBranchOutputValidation=Object.assign({},common.commonOutputValidation,{BranchList:v.$optional(v.$arrayOf(common.cloudBaseCodeBranchValidation)),PageNumber:v.$optional(1),PageSize:v.$optional(1),IsFinished:v.$optional(!0)}),exports.tcbCreateHostingDomainOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDeleteHostingDomainOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbModifyHostingDomainOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbOnlineHostingDomainOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribePostpayPackageListOutputValidation=Object.assign({},common.commonOutputValidation,{PostpayPackageInfoList:v.$arrayOf(common.postpayPackageInfoValidation)}),exports.tcbQueryActivityPriceOutputValidation=Object.assign({},common.commonOutputValidation,{Price:1}),exports.tcbCheckQualificationOutputValidation=Object.assign({},common.commonOutputValidation,{Available:1}),exports.tcbCreateActivityDealOutputValidation=Object.assign({},common.commonOutputValidation,{TranId:""}),exports.tcbDescribeActivityGoodsOutputValidation=Object.assign({},common.commonOutputValidation,{Products:v.$arrayOf(common.dianshiProductValidation)}),exports.tcbInqueryPackagePriceOutputValidation=Object.assign({},common.commonOutputValidation,{Price:1}),exports.tcbDescribeEnvPostpayPackageOutputValidation=Object.assign({},common.commonOutputValidation,{EnvPostpayPackageInfoList:v.$arrayOf(common.envPostpayPackageInfoValidation)}),exports.tcbDescribePostpayQuotaLimitOutputValidation=Object.assign({},common.commonOutputValidation,{QuotaLimitSet:v.$optional(v.$arrayOf(common.postpayQuotaLimitValidation))}),exports.tcbUpdatePostpayQuotaLimitStatusOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbUpdatePostpayQuotaLimitOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbUpdateScfConfigOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCreateInstallExtensionTaskOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCreateUninstallExtensionTaskOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeExtensionInstalledOutputValidation=Object.assign({},common.commonOutputValidation,{Extensions:v.$arrayOf(common.userExtensionInfoValidation),TotalCount:1}),exports.tcbDescribeExtensionTaskStatusOutputValidation=Object.assign({},common.commonOutputValidation,{ExtensionTaskInfo:v.$arrayOf(common.extensionTaskStatusValidation)}),exports.tcbDescribeExtensionTemplatesOutputValidation=Object.assign({},common.commonOutputValidation,{Templates:v.$arrayOf(common.objectKvValidation)}),exports.tcbDescribeExtensionUpgradeOutputValidation=Object.assign({},common.commonOutputValidation,{Extensions:v.$arrayOf(common.upgradeResItemValidation)}),exports.tcbDescribeExtensionsOutputValidation=Object.assign({},common.commonOutputValidation,{Extensions:v.$arrayOf(common.extensionInfoValidation),TotalCount:1}),exports.tcbCreateUpgradeExtensionTaskOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeCloudBaseRunOperationDetailsOutputValidation=Object.assign({},common.commonOutputValidation,{TotalCount:v.$optional(1),ActionDetails:v.$optional(v.$arrayOf(common.cloudBaseRunServiceHistoryActionValidation))}),exports.tcbDescribeCloudBaseRunVersionSnapshotOutputValidation=Object.assign({},common.commonOutputValidation,{Snapshots:v.$optional(v.$arrayOf(common.cloudRunServiceSimpleVersionSnapshotValidation))}),exports.tcbDescribeQcloudSceneOutputValidation=Object.assign({},common.commonOutputValidation,{Data:v.$optional(v.$arrayOf(common.monitorResourceValidation))}),exports.tcbDescribeSmsQuotasOutputValidation=Object.assign({},common.commonOutputValidation,{SmsFreeQuotaList:v.$optional(v.$arrayOf(common.smsFreeQuotaValidation))}),exports.tcbDescribeSmsAttrInfoOutputValidation=Object.assign({},common.commonOutputValidation,{Period:1,Record:v.$optional(v.$arrayOf(common.recordValidation)),Total:1}),exports.tcbDescribeTcbBalanceOutputValidation=Object.assign({},common.commonOutputValidation,{Balance:1}),exports.tcbDescribeSmsRecordsOutputValidation=Object.assign({},common.commonOutputValidation,{SmsRecords:v.$optional(v.$arrayOf(common.smsRecordValidation)),TotalCount:1}),exports.tcbDescribeCloudBaseRunServiceDomainOutputValidation=Object.assign({},common.commonOutputValidation,{DefaultPublicDomain:"",DefaultInternalDomain:"",AccessTypes:v.$arrayOf("")}),exports.tcbModifyEnvOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeWxCloudBaseRunEnvsOutputValidation=Object.assign({},common.commonOutputValidation,{EnvList:v.$arrayOf(common.envInfoValidation)}),exports.tcbDescribeWxCloudBaseRunSubNetsOutputValidation=Object.assign({},common.commonOutputValidation,{SubNetIds:v.$arrayOf("")}),exports.tcbRefundPostpaidPackageOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbQueryPostpaidPackageDealsOutputValidation=Object.assign({},common.commonOutputValidation,{Total:1,Deals:v.$arrayOf(common.postpaidPackageDealInfoValidation)}),exports.tcbSearchClsLogOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeAuditRuleOutputValidation=Object.assign({},common.commonOutputValidation,{AuditRules:v.$arrayOf(common.auditRuleInfoValidation),TotalCount:1}),exports.tcbDescribeCollectionsOutputValidation=Object.assign({},common.commonOutputValidation,{Collections:v.$arrayOf("")}),exports.tcbCreateAuditRulesOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDeleteAuditRuleOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbModifyAuditRuleOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeAuditResultsOutputValidation=Object.assign({},common.commonOutputValidation,{Result:v.$arrayOf(common.auditDetailInfoValidation),TotalCount:1}),exports.tcbUnfreezeSecurityAuditRecordOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeSecurityAuditConfigOutputValidation=Object.assign({},common.commonOutputValidation,{SwitchOn:!0}),exports.tcbDeleteSecurityAuditConfigOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCreateSecurityAuditConfigOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeTriggerServiceParametersOutputValidation=Object.assign({},common.commonOutputValidation,{TriggerTypes:v.$arrayOf(common.triggerTypeParameterValidation)}),exports.tcbCreateTriggerConfigsOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeTriggerConfigsOutputValidation=Object.assign({},common.commonOutputValidation,{Triggers:v.$arrayOf(common.triggerConfigValidation),TotalCount:1}),exports.tcbUpdateTriggerConfigOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDeleteTriggerConfigsOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbCreateCopyEnvTaskOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.tcbDescribeExtensionsInstalledOutputValidation=Object.assign({},common.commonOutputValidation,{EnvExtensions:v.$arrayOf(common.envExtensionItemValidation)});
}(require("licia/lazyImport")(require), require)
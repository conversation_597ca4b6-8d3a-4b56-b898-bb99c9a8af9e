!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.scfPutProvisionedConcurrencyConfigOutputValidation=exports.scfGetProvisionedConcurrencyConfigOutputValidation=exports.scfListVersionByFunctionOutputValidation=exports.scfPublishVersionOutputValidation=exports.scfGetAliasOutputValidation=exports.scfUpdateAliasOutputValidation=exports.scfGetFunctionLogsOutputValidation=exports.scfInvokeFunctionOutputValidation=exports.scfGetFunctionAddressOutputValidation=exports.scfBatchCreateTriggerOutputValidation=exports.scfUpdateFunctionTestModelOutputValidation=exports.scfDeleteFunctionTestModelOutputValidation=exports.scfCreateFunctionTestModelOutputValidation=exports.scfGetFunctionTestModelOutputValidation=exports.scfListFunctionTestModelsOutputValidation=exports.scfGetFunctionInfoOutputValidation=exports.scfDeleteFunctionOutputValidation=exports.scfUpdateFunctionInfoOutputValidation=exports.scfUpdateFunctionIncrementalCodeOutputValidation=exports.scfUpdateFunctionOutputValidation=exports.scfCreateFunctionOutputValidation=exports.scfListFunctionsOutputValidation=exports.scfFunctionValidation=exports.scfFunctionTagValidation=void 0;const tslib_1=require("tslib"),v=tslib_1.__importStar(require("../../utils/validator")),common=tslib_1.__importStar(require("./validations"));exports.scfFunctionTagValidation=Object.assign({},{Key:"",Value:""}),exports.scfFunctionValidation=Object.assign({},{ModTime:"",AddTime:"",Runtime:"",FunctionName:"",FunctionId:"",Namespace:"",Status:"",StatusDesc:"",Description:"",Tags:v.$arrayOf(exports.scfFunctionTagValidation)}),exports.scfListFunctionsOutputValidation=Object.assign({},common.commonOutputValidation,{Functions:v.$arrayOf(exports.scfFunctionValidation),TotalCount:1}),exports.scfCreateFunctionOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfUpdateFunctionOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfUpdateFunctionIncrementalCodeOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfUpdateFunctionInfoOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfDeleteFunctionOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfGetFunctionInfoOutputValidation=Object.assign({},common.commonOutputValidation,{Environment:v.$optional({Variables:v.$arrayOf({Key:"",Value:""})}),FunctionName:"",Runtime:"",Handler:"",MemorySize:1,Timeout:1}),exports.scfListFunctionTestModelsOutputValidation=Object.assign({},common.commonOutputValidation,{TestModels:v.$optional(v.$arrayOf(""))}),exports.scfGetFunctionTestModelOutputValidation=Object.assign({},common.commonOutputValidation,{TestModelValue:""}),exports.scfCreateFunctionTestModelOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfDeleteFunctionTestModelOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfUpdateFunctionTestModelOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfBatchCreateTriggerOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfGetFunctionAddressOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfInvokeFunctionOutputValidation=Object.assign({},common.commonOutputValidation,{Result:{FunctionRequestId:""}}),exports.scfGetFunctionLogsOutputValidation=Object.assign({},common.commonOutputValidation,{Data:v.$optional(v.$arrayOf({FunctionName:"",RetMsg:"",RequestId:"",StartTime:"",RetCode:1,InvokeFinished:1,Duration:1,BillDuration:1,MemUsage:1,Log:""}))}),exports.scfUpdateAliasOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.scfGetAliasOutputValidation=Object.assign({},common.commonOutputValidation,{FunctionVersion:"",Name:"",RoutingConfig:common.routingConfigValidation,Description:v.$optional(""),AddTime:v.$optional(""),ModTime:v.$optional("")}),exports.scfPublishVersionOutputValidation=Object.assign({},common.commonOutputValidation,{FunctionVersion:"",CodeSize:1,MemorySize:1,Description:"",Handler:"",Timeout:1,Runtime:"",Namespace:""}),exports.scfListVersionByFunctionOutputValidation=Object.assign({},common.commonOutputValidation,{FunctionVersion:v.$arrayOf(""),Versions:v.$optional(v.$arrayOf(common.functionVersionValidation))}),exports.scfGetProvisionedConcurrencyConfigOutputValidation=Object.assign({},common.commonOutputValidation,{UnallocatedConcurrencyNum:1,Allocated:v.$arrayOf(common.versionProvisionedConcurrencyInfoValidation)}),exports.scfPutProvisionedConcurrencyConfigOutputValidation=Object.assign({},common.commonOutputValidation,{});
}(require("licia/lazyImport")(require), require)
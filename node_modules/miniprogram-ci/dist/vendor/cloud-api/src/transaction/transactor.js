!function(require, directRequire){
"use strict";var TransactType;Object.defineProperty(exports,"__esModule",{value:!0}),exports.transact=exports.getDefaultAppID=exports.setDefaultAppID=exports.setTransactType=exports.setRequest=exports.TransactType=void 0,function(t){t[t.Mock=1]="Mock",t[t.HTTP=2]="HTTP",t[t.IDEPlugin=3]="IDEPlugin",t[t.IDE=4]="IDE"}(TransactType=exports.TransactType||(exports.TransactType={}));const defaultOptions={validOutput:!0,autoRecord:!0,isPoll:!1};let request,transactType,defaultAppID;function setRequest(t){request=t}function setTransactType(t){transactType=t}function setDefaultAppID(t){defaultAppID=t}function getDefaultAppID(){return defaultAppID}async function transact(t,e,a={}){const s=a.request||request;if(!s)throw new Error("[transactor] request function hasn't been initialized.");const r=a.transactType||transactType;if(!r)throw new Error("[transactor] transactType hasn't been initialized.");const n=Date.now(),p=Object.assign(Object.assign({},defaultOptions),a);let o,u;const c=t.getHttpAgentIdentity(e);let i;try{const a=t.inputTransformation(e,r);i=await s({postdata:a,identity:c,contract:t,_input:e}),o=t.outputTransformationThrows(i,r)}catch(t){u=t}const T=Date.now();if(!u&&p.validOutput)try{t.validOutputThrows(o)}catch(t){u=t}if(p.autoRecord&&t.commitRecord({timestamps:[n,T],input:e,output:o,error:u,rawOutput:i,httpAgentIdentity:c},p.isPoll),u)throw u;return o}exports.setRequest=setRequest,exports.setTransactType=setTransactType,exports.setDefaultAppID=setDefaultAppID,exports.getDefaultAppID=getDefaultAppID,exports.transact=transact,exports.default=transact;
}(require("licia/lazyImport")(require), require)
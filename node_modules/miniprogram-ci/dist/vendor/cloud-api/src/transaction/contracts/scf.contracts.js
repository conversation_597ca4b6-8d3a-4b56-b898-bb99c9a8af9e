!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.scfPutProvisionedConcurrencyConfigContract=exports.scfGetProvisionedConcurrencyConfigContract=exports.scfListVersionByFunctionContract=exports.scfPublishVersionContract=exports.scfGetAliasContract=exports.scfUpdateAliasContract=exports.scfGetFunctionLogsContract=exports.scfInvokeFunctionContract=exports.scfGetFunctionAddressContract=exports.scfBatchCreateTriggerContract=exports.scfUpdateFunctionTestModelContract=exports.scfDeleteFunctionTestModelContract=exports.scfCreateFunctionTestModelContract=exports.scfGetFunctionTestModelContract=exports.scfListFunctionTestModelsContract=exports.scfUpdateFunctionInfoContract=exports.scfGetFunctionInfoContract=exports.scfDeleteFunctionContract=exports.scfUpdateFunctionIncrementalCodeContract=exports.scfUpdateFunctionContract=exports.scfCreateFunctionContract=exports.scfListFunctionsContract=void 0;const tslib_1=require("tslib"),factory_1=tslib_1.__importDefault(require("./factory")),validations=tslib_1.__importStar(require("../validations/validations")),transactor_1=require("../transactor");function sharedInputTransformation(t,n){return(t&&n===transactor_1.TransactType.HTTP||n===transactor_1.TransactType.IDEPlugin||n===transactor_1.TransactType.IDE)&&(delete t.Action,delete t.Version,delete t.Region),JSON.stringify(t)}function sharedOutputTransformationThrows(t,n){if(!(t=JSON.parse(t))||!t.Response)throw new Error("content empty, "+JSON.stringify(t));const o=t.Response;if(o.Error&&o.Error.Code){const t=new Error(o.Error.Code+", "+o.Error.Message+" ("+(o.RequestId||"?")+")");throw t.code=o.Error.Code,t}return delete o.Error,o}exports.scfListFunctionsContract=new factory_1.default("ITCSCFListFunctionsInput","ITCSCFListFunctionsOutput",validations.scfListFunctionsOutputValidation,t=>({cgi_id:401,service:"scf",action:"ListFunctions",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfCreateFunctionContract=new factory_1.default("ITCSCFCreateFunctionInput","ITCSCFCreateFunctionOutput",validations.scfCreateFunctionOutputValidation,t=>({cgi_id:402,service:"scf",action:"CreateFunction",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfUpdateFunctionContract=new factory_1.default("ITCSCFUpdateFunctionInput","ITCSCFUpdateFunctionOutput",validations.scfUpdateFunctionOutputValidation,t=>({cgi_id:415,service:"scf",action:"UpdateFunctionCode",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfUpdateFunctionIncrementalCodeContract=new factory_1.default("ITCSCFUpdateFunctionIncrementalCodeInput","ITCSCFUpdateFunctionIncrementalCodeOutput",validations.scfUpdateFunctionIncrementalCodeOutputValidation,t=>({cgi_id:415,service:"scf",action:"UpdateFunctionIncrementalCode",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfDeleteFunctionContract=new factory_1.default("ITCSCFDeleteFunctionInput","ITCSCFDeleteFunctionOutput",validations.scfDeleteFunctionOutputValidation,t=>({cgi_id:403,service:"scf",action:"DeleteFunction",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfGetFunctionInfoContract=new factory_1.default("ITCSCFGetFunctionInfoInput","ITCSCFGetFunctionInfoOutput",validations.scfGetFunctionInfoOutputValidation,t=>({cgi_id:404,service:"scf",action:"GetFunction",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfUpdateFunctionInfoContract=new factory_1.default("ITCSCFUpdateFunctionInfoInput","ITCSCFUpdateFunctionInfoOutput",validations.scfUpdateFunctionInfoOutputValidation,t=>({cgi_id:405,service:"scf",action:"UpdateFunctionConfiguration",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfListFunctionTestModelsContract=new factory_1.default("ITCSCFListFunctionTestModelsInput","ITCSCFListFunctionTestModelsOutput",validations.scfListFunctionTestModelsOutputValidation,t=>({cgi_id:406,service:"scf",action:"ListFunctionTestModels",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfGetFunctionTestModelContract=new factory_1.default("ITCSCFGetFunctionTestModelInput","ITCSCFGetFunctionTestModelOutput",validations.scfGetFunctionTestModelOutputValidation,t=>({cgi_id:407,service:"scf",action:"GetFunctionTestModel",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfCreateFunctionTestModelContract=new factory_1.default("ITCSCFCreateFunctionTestModelInput","ITCSCFCreateFunctionTestModelOutput",validations.scfCreateFunctionTestModelOutputValidation,t=>({cgi_id:408,service:"scf",action:"CreateFunctionTestModel",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfDeleteFunctionTestModelContract=new factory_1.default("ITCSCFDeleteFunctionTestModelInput","ITCSCFDeleteFunctionTestModelOutput",validations.scfDeleteFunctionTestModelOutputValidation,t=>({cgi_id:409,service:"scf",action:"DeleteFunctionTestModel",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfUpdateFunctionTestModelContract=new factory_1.default("ITCSCFUpdateFunctionTestModelInput","ITCSCFUpdateFunctionTestModelOutput",validations.scfUpdateFunctionTestModelOutputValidation,t=>({cgi_id:410,service:"scf",action:"UpdateFunctionTestModel",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfBatchCreateTriggerContract=new factory_1.default("ITCSCFBatchCreateTriggerInput","ITCSCFBatchCreateTriggerOutput",validations.scfBatchCreateTriggerOutputValidation,t=>({cgi_id:413,service:"scf",action:"BatchCreateTrigger",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfGetFunctionAddressContract=new factory_1.default("ITCSCFGetFunctionAddressInput","ITCSCFGetFunctionAddressOutput",validations.scfGetFunctionAddressOutputValidation,t=>({cgi_id:414,service:"scf",action:"GetFunctionAddress",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfInvokeFunctionContract=new factory_1.default("ITCSCFInvokeFunctionInput","ITCSCFInvokeFunctionOutput",validations.scfInvokeFunctionOutputValidation,t=>({cgi_id:411,service:"scf",action:"Invoke",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfGetFunctionLogsContract=new factory_1.default("ITCSCFGetFunctionLogsInput","ITCSCFGetFunctionLogsOutput",validations.scfGetFunctionLogsOutputValidation,t=>({cgi_id:412,service:"scf",action:"GetFunctionLogs",version:"2018-04-16",region:t.Region||""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfUpdateAliasContract=new factory_1.default("ITCSCFUpdateAliasInput","ITCSCFUpdateAliasOutput",validations.scfUpdateAliasOutputValidation,()=>({cgi_id:413,service:"scf",action:"UpdateAlias",version:"2018-04-16",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfGetAliasContract=new factory_1.default("ITCSCFGetAliasInput","ITCSCFGetAliasOutput",validations.scfGetAliasOutputValidation,()=>({cgi_id:414,service:"scf",action:"GetAlias",version:"2018-04-16",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfPublishVersionContract=new factory_1.default("ITCSCFPublishVersionInput","ITCSCFPublishVersionOutput",validations.scfPublishVersionOutputValidation,()=>({cgi_id:415,service:"scf",action:"PublishVersion",version:"2018-04-16",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfListVersionByFunctionContract=new factory_1.default("ITCSCFListVersionByFunctionInput","ITCSCFListVersionByFunctionOutput",validations.scfListVersionByFunctionOutputValidation,()=>({cgi_id:416,service:"scf",action:"ListVersionByFunction",version:"2018-04-16",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfGetProvisionedConcurrencyConfigContract=new factory_1.default("ITCSCFGetProvisionedConcurrencyConfigInput","ITCSCFGetProvisionedConcurrencyConfigOutput",validations.scfGetProvisionedConcurrencyConfigOutputValidation,()=>({cgi_id:417,service:"scf",action:"GetProvisionedConcurrencyConfig",version:"2018-04-16",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.scfPutProvisionedConcurrencyConfigContract=new factory_1.default("ITCSCFPutProvisionedConcurrencyConfigInput","ITCSCFPutProvisionedConcurrencyConfigOutput",validations.scfPutProvisionedConcurrencyConfigOutputValidation,()=>({cgi_id:418,service:"scf",action:"PutProvisionedConcurrencyConfig",version:"2018-04-16",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows});
}(require("licia/lazyImport")(require), require)
!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.sslDescribeCertificatesContract=void 0;const tslib_1=require("tslib"),factory_1=tslib_1.__importDefault(require("./factory")),validations=tslib_1.__importStar(require("../validations/validations")),transactor_1=require("../transactor");function sharedInputTransformation(r,t){return(r&&t===transactor_1.TransactType.HTTP||t===transactor_1.TransactType.IDEPlugin||t===transactor_1.TransactType.IDE)&&(delete r.Action,delete r.Version,delete r.Region),JSON.stringify(r)}function sharedOutputTransformationThrows(r,t){if(!(r=JSON.parse(r))||!r.Response)throw new Error("content empty, "+JSON.stringify(r));const e=r.Response;if(e.Error&&e.Error.Code){const r=new Error(e.Error.Code+", "+e.Error.Message+" ("+(e.RequestId||"?")+")");throw r.code=e.Error.Code,r}return delete e.Error,e}exports.sslDescribeCertificatesContract=new factory_1.default("ITCSSLDescribeCertificatesInput","ITCSSLDescribeCertificatesOutput",validations.sslDescribeCertificatesOutputValidation,()=>({cgi_id:-1,service:"ssl",action:"DescribeCertificates",version:"2019-12-05",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows});
}(require("licia/lazyImport")(require), require)
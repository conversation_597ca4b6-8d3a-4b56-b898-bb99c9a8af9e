!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.tcbDescribeVoucherPlanAvailableContract=exports.tcbDescribeVouchersInfoContract=exports.tcbDescribeAmountAfterDeductionContract=exports.tcbDescribeVouchersInfoByDealContract=exports.tcbDescribeResourceRecoverJobContract=exports.tcbResourceRecoverContract=exports.tcbDescribeEnvResourceExceptionContract=exports.tcbDescribeAuthentificationContract=exports.tcbRevokeInvoiceContract=exports.tcbDescribeInvoiceDetailContract=exports.tcbDescribeInvoiceListContract=exports.tcbCreateInvoiceContract=exports.tcbDeleteInvoicePostInfoContract=exports.tcbModifyInvoicePostInfoContract=exports.tcbCreateInvoicePostInfoContract=exports.tcbDescribeInvoicePostInfoContract=exports.tcbSetInvoiceSubjectContract=exports.tcbDescribeInvoiceSubjectContract=exports.tcbDescribeInvoiceAmountContract=exports.tcbDescribeNextExpireTimeContract=exports.tcbDescribeBillingInfoContract=exports.tcbCheckEnvPackageModifyContract=exports.tcbDeleteDealContract=exports.tcbCancelDealContract=exports.tcbQueryDealsContract=exports.tcbDescribePayInfoContract=exports.tcbCreateDealContract=exports.tcbInqueryPriceContract=exports.tcbDescribePackagesContract=exports.tcbDescribeEnvAccountCircleContract=exports.tcbCheckEnvIdContract=exports.tcbCreateEnvAndResourceContract=exports.tcbDescribeSafeRuleContract=exports.tcbModifySafeRuleContract=exports.tcbDatabaseMigrateQueryInfoContract=exports.tcbDatabaseMigrateExportContract=exports.tcbDatabaseMigrateImportContract=exports.tcbModifyDatabaseACLContract=exports.tcbDescribeDatabaseACLContract=exports.tcbDescribeStorageACLTaskContract=exports.tcbModifyStorageACLContract=exports.tcbDescribeStorageACLContract=exports.tcbDescribeCurveDataContract=exports.tcbDescribeMonitorDataContract=exports.tcbDescribeDbDistributionContract=exports.tcbDescribeQuotaDataContract=exports.tcbDescribeStatDataContract=exports.tcbGetResourceLimitContract=exports.tcbGetUsersContract=exports.tcbGetEnvironmentsContract=void 0,exports.tcbDescribeCloudBaseGWAPIContract=exports.tcbModifyCloudBaseRunServerVersionContract=exports.tcbDescribeCloudBaseRunVersionExceptionContract=exports.tcbDescribeCloudBaseBuildServiceContract=exports.tcbDescribeCloudBaseRunBuildLogContract=exports.tcbDescribeCloudBaseRunPodListContract=exports.tcbDeleteCloudBaseRunResourceContract=exports.tcbEstablishCloudBaseRunServerContract=exports.tcbDescribeCloudBaseRunServerVersionContract=exports.tcbCreateCloudBaseRunServerVersionContract=exports.tcbDescribeCloudBaseRunContainerSpecContract=exports.tcbDescribeCloudBaseRunServerContract=exports.tcbDescribeCloudBaseRunBuildServerContract=exports.tcbCreateCloudBaseRunResourceContract=exports.tcbDescribeCloudBaseRunServersContract=exports.tcbDescribeCloudBaseRunResourceContract=exports.tcbDescribeHostingDomainContract=exports.tcbDescribeEnvFreeQuotaContract=exports.tcbDescribeAccountInfoByPlatformIdContract=exports.tcbDescribeEnvLimitContract=exports.tcbDescribeStaticStoreContract=exports.tcbDestroyStaticStoreContract=exports.tcbCreateStaticStoreContract=exports.tcbModifySecurityRuleContract=exports.tcbDescribeSecurityRuleContract=exports.tcbUpdateLoginConfigContract=exports.tcbCreateLoginConfigContract=exports.tcbDescribeLoginConfigsContract=exports.tcbDescribeCDNChainTaskContract=exports.tcbDescribeStorageSafeRuleContract=exports.tcbModifyStorageSafeRuleContract=exports.tcbDescribePostpayFreeQuotasContract=exports.tcbInqueryPostpayPriceContract=exports.tcbCreatePostpayPackageContract=exports.tcbCommonServiceAPIContract=exports.tcbDescribeRestoreHistoryContract=exports.tcbDescribeChangePayContract=exports.tcbDescribeDauDataContract=exports.tcbModifyMonitorConditionContract=exports.tcbDeleteMonitorConditionContract=exports.tcbCreateMonitorConditionContract=exports.tcbDescribeMonitorConditionContract=exports.tcbModifyMonitorPolicyContract=exports.tcbDeleteMonitorPolicyContract=exports.tcbCreateMonitorPolicyContract=exports.tcbDescribeMonitorPolicyContract=exports.tcbDescribeMonitorResourceContract=exports.tcbDeleteVoucherApplicationContract=exports.tcbDescribeVoucherApplicationContract=exports.tcbApplyVoucherContract=void 0,exports.tcbCreateAuditRulesContract=exports.tcbDescribeCollectionsContract=exports.tcbDescribeAuditRuleContract=exports.tcbSearchClsLogContract=exports.tcbQueryPostpaidPackageDealsContract=exports.tcbRefundPostpaidPackageContract=exports.tcbDescribeWxCloudBaseRunSubNetsContract=exports.tcbDescribeWxCloudBaseRunEnvsContract=exports.tcbModifyEnvContract=exports.tcbDescribeCloudBaseRunServiceDomainContract=exports.tcbDescribeSmsRecordsContract=exports.tcbDescribeTcbBalanceContract=exports.tcbDescribeSmsAttrInfoContract=exports.tcbDescribeSmsQuotasContract=exports.tcbDescribeQcloudSceneContract=exports.tcbDescribeCloudBaseRunVersionSnapshotContract=exports.tcbDescribeCloudBaseRunOperationDetailsContract=exports.tcbCreateUpgradeExtensionTaskContract=exports.tcbDescribeExtensionsContract=exports.tcbDescribeExtensionUpgradeContract=exports.tcbDescribeExtensionTemplatesContract=exports.tcbDescribeExtensionTaskStatusContract=exports.tcbDescribeExtensionInstalledContract=exports.tcbCreateUninstallExtensionTaskContract=exports.tcbCreateInstallExtensionTaskContract=exports.tcbUpdateScfConfigContract=exports.tcbUpdatePostpayQuotaLimitContract=exports.tcbUpdatePostpayQuotaLimitStatusContract=exports.tcbDescribePostpayQuotaLimitContract=exports.tcbDescribeEnvPostpayPackageContract=exports.tcbInqueryPackagePriceContract=exports.tcbDescribeActivityGoodsContract=exports.tcbCreateActivityDealContract=exports.tcbOnlineHostingDomainContract=exports.tcbCheckQualificationContract=exports.tcbModifyHostingDomainContract=exports.tcbQueryActivityPriceContract=exports.tcbDeleteHostingDomainContract=exports.tcbDescribePostpayPackageListContract=exports.tcbCreateHostingDomainContract=exports.tcbDescribeCloudBaseCodeBranchContract=exports.tcbDescribeCloudBaseCodeReposContract=exports.tcbDeleteCloudBaseRunServerContract=exports.tcbModifyCloudBaseRunServerFlowConfContract=exports.tcbRollUpdateCloudBaseRunServerVersionContract=exports.tcbDeleteCloudBaseRunImageRepoContract=exports.tcbDeleteCloudBaseRunServerVersionContract=exports.tcbDescribeCloudBaseRunBuildStepLogContract=exports.tcbDescribeCloudBaseRunBuildStepsContract=exports.tcbDescribeCloudBaseRunBuildStagesContract=void 0,exports.tcbDescribeExtensionsInstalledContract=exports.tcbCreateCopyEnvTaskContract=exports.tcbDeleteTriggerConfigsContract=exports.tcbUpdateTriggerConfigContract=exports.tcbDescribeTriggerConfigsContract=exports.tcbCreateTriggerConfigsContract=exports.tcbDescribeTriggerServiceParametersContract=exports.tcbCreateSecurityAuditConfigContract=exports.tcbDeleteSecurityAuditConfigContract=exports.tcbDescribeSecurityAuditConfigContract=exports.tcbUnfreezeSecurityAuditRecordContract=exports.tcbDescribeAuditResultsContract=exports.tcbModifyAuditRuleContract=exports.tcbDeleteAuditRuleContract=void 0;const tslib_1=require("tslib"),factory_1=tslib_1.__importDefault(require("./factory")),validations=tslib_1.__importStar(require("../validations/validations")),transactor_1=require("../transactor");function sharedInputTransformation(t,o){return(t&&o===transactor_1.TransactType.HTTP||o===transactor_1.TransactType.IDEPlugin||o===transactor_1.TransactType.IDE)&&(delete t.Action,delete t.Version),JSON.stringify(t)}function sharedOutputTransformationThrows(t,o){if(!(t=JSON.parse(t))||!t.Response)throw new Error("content empty, "+JSON.stringify(t));const r=t.Response;if(r.Error&&r.Error.Code)throw new Error(r.Error.Code+", "+r.Error.Message+" ("+(r.RequestId||"?")+")");return delete r.Error,r}exports.tcbGetEnvironmentsContract=new factory_1.default("ITCTCBGetEnvironmentsInput","ITCTCBGetEnvironmentsOutput",validations.tcbGetEnvironmentsOutputValidation,()=>({cgi_id:101,service:"tcb",action:"DescribeEnvs",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbGetUsersContract=new factory_1.default("ITCTCBGetUsersInput","ITCTCBGetUsersOutput",validations.tcbGetUsersOutputValidation,()=>({cgi_id:102,service:"tcb",action:"DescribeUsers",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbGetResourceLimitContract=new factory_1.default("ITCTCBGetResourceLimitInput","ITCTCBGetResourceLimitOutput",validations.tcbGetResourceLimitOutputValidation,()=>({cgi_id:103,service:"tcb",action:"DescribeResourceLimit",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeStatDataContract=new factory_1.default("ITCTCBDescribeStatDataInput","ITCTCBDescribeStatDataOutput",validations.tcbDescribeStatDataOutputValidation,()=>({cgi_id:104,service:"tcb",action:"DescribeStatData",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeQuotaDataContract=new factory_1.default("ITCTCBDescribeQuotaDataInput","ITCTCBDescribeQuotaDataOutput",validations.tcbDescribeQuotaDataOutputValidation,()=>({cgi_id:118,service:"tcb",action:"DescribeQuotaData",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeDbDistributionContract=new factory_1.default("ITCTCBDescribeDbDistributionInput","ITCTCBDescribeDbDistributionOutput",validations.tcbDescribeDbDistributionOutputValidation,()=>({cgi_id:105,service:"tcb",action:"DescribeDbDistribution",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeMonitorDataContract=new factory_1.default("ITCTCBDescribeMonitorDataInput","ITCTCBDescribeMonitorDataOutput",validations.tcbDescribeMonitorDataOutputValidation,()=>({cgi_id:106,service:"tcb",action:"DescribeMonitorData",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCurveDataContract=new factory_1.default("ITCTCBDescribeCurveDataInput","ITCTCBDescribeCurveDataOutput",validations.tcbDescribeCurveDataOutputValidation,()=>({cgi_id:117,service:"tcb",action:"DescribeCurveData",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeStorageACLContract=new factory_1.default("ITCTCBDescribeStorageACLInput","ITCTCBDescribeStorageACLOutput",validations.tcbDescribeStorageACLOutputValidation,()=>({cgi_id:107,service:"tcb",action:"DescribeStorageACL",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyStorageACLContract=new factory_1.default("ITCTCBModifyStorageACLInput","ITCTCBModifyStorageACLOutput",validations.tcbModifyStorageACLOutputValidation,()=>({cgi_id:108,service:"tcb",action:"ModifyStorageACL",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeStorageACLTaskContract=new factory_1.default("ITCTCBDescribeStorageACLTaskInput","ITCTCBDescribeStorageACLTaskOutput",validations.tcbDescribeStorageACLTaskOutputValidation,()=>({cgi_id:109,service:"tcb",action:"DescribeStorageACLTask",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeDatabaseACLContract=new factory_1.default("ITCTCBDescribeDatabaseACLInput","ITCTCBDescribeDatabaseACLOutput",validations.tcbDescribeDatabaseACLOutputValidation,()=>({cgi_id:110,service:"tcb",action:"DescribeDatabaseACL",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyDatabaseACLContract=new factory_1.default("ITCTCBModifyDatabaseACLInput","ITCTCBModifyDatabaseACLOutput",validations.tcbModifyDatabaseACLOutputValidation,()=>({cgi_id:111,service:"tcb",action:"ModifyDatabaseACL",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDatabaseMigrateImportContract=new factory_1.default("ITCTCBDatabaseMigrateImportInput","ITCTCBDatabaseMigrateImportOutput",validations.tcbDatabaseMigrateImportOutputValidation,()=>({cgi_id:112,service:"tcb",action:"DatabaseMigrateImport",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDatabaseMigrateExportContract=new factory_1.default("ITCTCBDatabaseMigrateExportInput","ITCTCBDatabaseMigrateExportOutput",validations.tcbDatabaseMigrateExportOutputValidation,()=>({cgi_id:113,service:"tcb",action:"DatabaseMigrateExport",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDatabaseMigrateQueryInfoContract=new factory_1.default("ITCTCBDatabaseMigrateQueryInfoInput","ITCTCBDatabaseMigrateQueryInfoOutput",validations.tcbDatabaseMigrateQueryInfoOutputValidation,()=>({cgi_id:114,service:"tcb",action:"DatabaseMigrateQueryInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifySafeRuleContract=new factory_1.default("ITCTCBModifySafeRuleInput","ITCTCBModifySafeRuleOutput",validations.tcbModifySafeRuleOutputValidation,()=>({cgi_id:114,service:"tcb",action:"ModifySafeRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeSafeRuleContract=new factory_1.default("ITCTCBDescribeSafeRuleInput","ITCTCBDescribeSafeRuleOutput",validations.tcbDescribeSafeRuleOutputValidation,()=>({cgi_id:114,service:"tcb",action:"DescribeSafeRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateEnvAndResourceContract=new factory_1.default("ITCTCBCreateEnvAndResourceInput","ITCTCBCreateEnvAndResourceOutput",validations.tcbCreateEnvAndResourceOutputValidation,()=>({cgi_id:115,service:"tcb",action:"CreateEnvAndResource",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCheckEnvIdContract=new factory_1.default("ITCTCBCheckEnvIdInput","ITCTCBCheckEnvIdOutput",validations.tcbCheckEnvIdOutputValidation,()=>({cgi_id:116,service:"tcb",action:"CheckEnvId",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeEnvAccountCircleContract=new factory_1.default("ITCTCBDescribeEnvAccountCircleInput","ITCTCBDescribeEnvAccountCircleOutput",validations.tcbDescribeEnvAccountCircleOutputValidation,()=>({cgi_id:119,service:"tcb",action:"DescribeEnvAccountCircle",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribePackagesContract=new factory_1.default("ITCTCBDescribePackagesInput","ITCTCBDescribePackagesOutput",validations.tcbDescribePackagesOutputValidation,()=>({cgi_id:120,service:"tcb",action:"DescribePackages",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbInqueryPriceContract=new factory_1.default("ITCTCBInqueryPriceInput","ITCTCBInqueryPriceOutput",validations.tcbInqueryPriceOutputValidation,()=>({cgi_id:121,service:"tcb",action:"InqueryPrice",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateDealContract=new factory_1.default("ITCTCBCreateDealInput","ITCTCBCreateDealOutput",validations.tcbCreateDealOutputValidation,()=>({cgi_id:122,service:"tcb",action:"CreateDeal",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribePayInfoContract=new factory_1.default("ITCTCBDescribePayInfoInput","ITCTCBDescribePayInfoOutput",validations.tcbDescribePayInfoOutputValidation,()=>({cgi_id:123,service:"tcb",action:"DescribePayInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbQueryDealsContract=new factory_1.default("ITCTCBQueryDealsInput","ITCTCBQueryDealsOutput",validations.tcbQueryDealsOutputValidation,()=>({cgi_id:124,service:"tcb",action:"QueryDeals",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCancelDealContract=new factory_1.default("ITCTCBCancelDealInput","ITCTCBCancelDealOutput",validations.tcbCancelDealOutputValidation,()=>({cgi_id:125,service:"tcb",action:"CancelDeal",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteDealContract=new factory_1.default("ITCTCBDeleteDealInput","ITCTCBDeleteDealOutput",validations.tcbDeleteDealOutputValidation,()=>({cgi_id:126,service:"tcb",action:"DeleteDeal",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCheckEnvPackageModifyContract=new factory_1.default("ITCTCBCheckEnvPackageModifyInput","ITCTCBCheckEnvPackageModifyOutput",validations.tcbCheckEnvPackageModifyOutputValidation,()=>({cgi_id:127,service:"tcb",action:"CheckEnvPackageModify",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeBillingInfoContract=new factory_1.default("ITCTCBDescribeBillingInfoInput","ITCTCBDescribeBillingInfoOutput",validations.tcbDescribeBillingInfoOutputValidation,()=>({cgi_id:128,service:"tcb",action:"DescribeBillingInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeNextExpireTimeContract=new factory_1.default("ITCTCBDescribeNextExpireTimeInput","ITCTCBDescribeNextExpireTimeOutput",validations.tcbDescribeNextExpireTimeOutputValidation,()=>({cgi_id:129,service:"tcb",action:"DescribeNextExpireTime",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeInvoiceAmountContract=new factory_1.default("ITCTCBDescribeInvoiceAmountInput","ITCTCBDescribeInvoiceAmountOutput",validations.tcbDescribeInvoiceAmountOutputValidation,()=>({cgi_id:129,service:"tcb",action:"DescribeInvoiceAmount",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeInvoiceSubjectContract=new factory_1.default("ITCTCBDescribeInvoiceSubjectInput","ITCTCBDescribeInvoiceSubjectOutput",validations.tcbDescribeInvoiceSubjectOutputValidation,()=>({cgi_id:130,service:"tcb",action:"DescribeInvoiceSubject",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbSetInvoiceSubjectContract=new factory_1.default("ITCTCBSetInvoiceSubjectInput","ITCTCBSetInvoiceSubjectOutput",validations.tcbSetInvoiceSubjectOutputValidation,()=>({cgi_id:131,service:"tcb",action:"SetInvoiceSubject",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeInvoicePostInfoContract=new factory_1.default("ITCTCBDescribeInvoicePostInfoInput","ITCTCBDescribeInvoicePostInfoOutput",validations.tcbDescribeInvoicePostInfoOutputValidation,()=>({cgi_id:132,service:"tcb",action:"DescribeInvoicePostInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateInvoicePostInfoContract=new factory_1.default("ITCTCBCreateInvoicePostInfoInput","ITCTCBCreateInvoicePostInfoOutput",validations.tcbCreateInvoicePostInfoOutputValidation,()=>({cgi_id:133,service:"tcb",action:"CreateInvoicePostInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyInvoicePostInfoContract=new factory_1.default("ITCTCBModifyInvoicePostInfoInput","ITCTCBModifyInvoicePostInfoOutput",validations.tcbModifyInvoicePostInfoOutputValidation,()=>({cgi_id:134,service:"tcb",action:"ModifyInvoicePostInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteInvoicePostInfoContract=new factory_1.default("ITCTCBDeleteInvoicePostInfoInput","ITCTCBDeleteInvoicePostInfoOutput",validations.tcbDeleteInvoicePostInfoOutputValidation,()=>({cgi_id:135,service:"tcb",action:"DeleteInvoicePostInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateInvoiceContract=new factory_1.default("ITCTCBCreateInvoiceInput","ITCTCBCreateInvoiceOutput",validations.tcbCreateInvoiceOutputValidation,()=>({cgi_id:136,service:"tcb",action:"CreateInvoice",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeInvoiceListContract=new factory_1.default("ITCTCBDescribeInvoiceListInput","ITCTCBDescribeInvoiceListOutput",validations.tcbDescribeInvoiceListOutputValidation,()=>({cgi_id:137,service:"tcb",action:"DescribeInvoiceList",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeInvoiceDetailContract=new factory_1.default("ITCTCBDescribeInvoiceDetailInput","ITCTCBDescribeInvoiceDetailOutput",validations.tcbDescribeInvoiceDetailOutputValidation,()=>({cgi_id:138,service:"tcb",action:"DescribeInvoiceDetail",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbRevokeInvoiceContract=new factory_1.default("ITCTCBRevokeInvoiceInput","ITCTCBRevokeInvoiceOutput",validations.tcbRevokeInvoiceOutputValidation,()=>({cgi_id:139,service:"tcb",action:"RevokeInvoice",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeAuthentificationContract=new factory_1.default("ITCTCBDescribeAuthentificationInput","ITCTCBDescribeAuthentificationOutput",validations.tcbDescribeAuthentificationOutputValidation,()=>({cgi_id:140,service:"tcb",action:"DescribeAuthentification",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeEnvResourceExceptionContract=new factory_1.default("ITCTCBDescribeEnvResourceExceptionInput","ITCTCBDescribeEnvResourceExceptionOutput",validations.tcbDescribeEnvResourceExceptionOutputValidation,()=>({cgi_id:141,service:"tcb",action:"DescribeEnvResourceException",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbResourceRecoverContract=new factory_1.default("ITCTCBResourceRecoverInput","ITCTCBResourceRecoverOutput",validations.tcbResourceRecoverOutputValidation,()=>({cgi_id:142,service:"tcb",action:"ResourceRecover",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeResourceRecoverJobContract=new factory_1.default("ITCTCBDescribeResourceRecoverJobInput","ITCTCBDescribeResourceRecoverJobOutput",validations.tcbDescribeResourceRecoverJobOutputValidation,()=>({cgi_id:143,service:"tcb",action:"DescribeResourceRecoverJob",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeVouchersInfoByDealContract=new factory_1.default("ITCTCBDescribeVouchersInfoByDealInput","ITCTCBDescribeVouchersInfoByDealOutput",validations.tcbDescribeVouchersInfoByDealOutputValidation,()=>({cgi_id:144,service:"tcb",action:"DescribeVouchersInfoByDeal",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeAmountAfterDeductionContract=new factory_1.default("ITCTCBDescribeAmountAfterDeductionInput","ITCTCBDescribeAmountAfterDeductionOutput",validations.tcbDescribeAmountAfterDeductionOutputValidation,()=>({cgi_id:145,service:"tcb",action:"DescribeAmountAfterDeduction",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeVouchersInfoContract=new factory_1.default("ITCTCBDescribeVouchersInfoInput","ITCTCBDescribeVouchersInfoOutput",validations.tcbDescribeVouchersInfoOutputValidation,()=>({cgi_id:146,service:"tcb",action:"DescribeVouchersInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeVoucherPlanAvailableContract=new factory_1.default("ITCTCBDescribeVoucherPlanAvailableInput","ITCTCBDescribeVoucherPlanAvailableOutput",validations.tcbDescribeVoucherPlanAvailableOutputValidation,()=>({cgi_id:147,service:"tcb",action:"DescribeVoucherPlanAvailable",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbApplyVoucherContract=new factory_1.default("ITCTCBApplyVoucherInput","ITCTCBApplyVoucherOutput",validations.tcbApplyVoucherOutputValidation,()=>({cgi_id:148,service:"tcb",action:"ApplyVoucher",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeVoucherApplicationContract=new factory_1.default("ITCTCBDescribeVoucherApplicationInput","ITCTCBDescribeVoucherApplicationOutput",validations.tcbDescribeVoucherApplicationOutputValidation,()=>({cgi_id:149,service:"tcb",action:"DescribeVoucherApplication",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteVoucherApplicationContract=new factory_1.default("ITCTCBDeleteVoucherApplicationInput","ITCTCBDeleteVoucherApplicationOutput",validations.tcbDeleteVoucherApplicationOutputValidation,()=>({cgi_id:150,service:"tcb",action:"DeleteVoucherApplication",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeMonitorResourceContract=new factory_1.default("ITCTCBDescribeMonitorResourceInput","ITCTCBDescribeMonitorResourceOutput",validations.tcbDescribeMonitorResourceOutputValidation,()=>({cgi_id:151,service:"tcb",action:"DescribeMonitorResource",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeMonitorPolicyContract=new factory_1.default("ITCTCBDescribeMonitorPolicyInput","ITCTCBDescribeMonitorPolicyOutput",validations.tcbDescribeMonitorPolicyOutputValidation,()=>({cgi_id:152,service:"tcb",action:"DescribeMonitorPolicy",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateMonitorPolicyContract=new factory_1.default("ITCTCBCreateMonitorPolicyInput","ITCTCBCreateMonitorPolicyOutput",validations.tcbCreateMonitorPolicyOutputValidation,()=>({cgi_id:153,service:"tcb",action:"CreateMonitorPolicy",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteMonitorPolicyContract=new factory_1.default("ITCTCBDeleteMonitorPolicyInput","ITCTCBDeleteMonitorPolicyOutput",validations.tcbDeleteMonitorPolicyOutputValidation,()=>({cgi_id:154,service:"tcb",action:"DeleteMonitorPolicy",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyMonitorPolicyContract=new factory_1.default("ITCTCBModifyMonitorPolicyInput","ITCTCBModifyMonitorPolicyOutput",validations.tcbModifyMonitorPolicyOutputValidation,()=>({cgi_id:155,service:"tcb",action:"ModifyMonitorPolicy",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeMonitorConditionContract=new factory_1.default("ITCTCBDescribeMonitorConditionInput","ITCTCBDescribeMonitorConditionOutput",validations.tcbDescribeMonitorConditionOutputValidation,()=>({cgi_id:156,service:"tcb",action:"DescribeMonitorCondition",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateMonitorConditionContract=new factory_1.default("ITCTCBCreateMonitorConditionInput","ITCTCBCreateMonitorConditionOutput",validations.tcbCreateMonitorConditionOutputValidation,()=>({cgi_id:157,service:"tcb",action:"CreateMonitorCondition",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteMonitorConditionContract=new factory_1.default("ITCTCBDeleteMonitorConditionInput","ITCTCBDeleteMonitorConditionOutput",validations.tcbDeleteMonitorConditionOutputValidation,()=>({cgi_id:158,service:"tcb",action:"DeleteMonitorCondition",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyMonitorConditionContract=new factory_1.default("ITCTCBModifyMonitorConditionInput","ITCTCBModifyMonitorConditionOutput",validations.tcbModifyMonitorConditionOutputValidation,()=>({cgi_id:159,service:"tcb",action:"ModifyMonitorCondition",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeDauDataContract=new factory_1.default("ITCTCBDescribeDauDataInput","ITCTCBDescribeDauDataOutput",validations.tcbDescribeDauDataOutputValidation,()=>({cgi_id:160,service:"tcb",action:"DescribeDauData",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeChangePayContract=new factory_1.default("ITCTCBDescribeChangePayInput","ITCTCBDescribeChangePayOutput",validations.tcbDescribeChangePayOutputValidation,()=>({cgi_id:161,service:"tcb",action:"DescribeChangePay",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeRestoreHistoryContract=new factory_1.default("ITCTCBDescribeRestoreHistoryInput","ITCTCBDescribeRestoreHistoryOutput",validations.tcbDescribeRestoreHistoryOutputValidation,()=>({cgi_id:162,service:"tcb",action:"DescribeRestoreHistory",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCommonServiceAPIContract=new factory_1.default("ITCTCBCommonServiceAPIInput","ITCTCBCommonServiceAPIOutput",validations.tcbCommonServiceAPIOutputValidation,()=>({cgi_id:163,service:"tcb",action:"CommonServiceAPI",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreatePostpayPackageContract=new factory_1.default("ITCTCBCreatePostpayPackageInput","ITCTCBCreatePostpayPackageOutput",validations.tcbCreatePostpayPackageOutputValidation,()=>({cgi_id:164,service:"tcb",action:"CreatePostpayPackage",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbInqueryPostpayPriceContract=new factory_1.default("ITCTCBInqueryPostpayPriceInput","ITCTCBInqueryPostpayPriceOutput",validations.tcbInqueryPostpayPriceOutputValidation,()=>({cgi_id:165,service:"tcb",action:"InqueryPostpayPrice",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribePostpayFreeQuotasContract=new factory_1.default("ITCTCBDescribePostpayFreeQuotasInput","ITCTCBDescribePostpayFreeQuotasOutput",validations.tcbDescribePostpayFreeQuotasOutputValidation,()=>({cgi_id:166,service:"tcb",action:"DescribePostpayFreeQuotas",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyStorageSafeRuleContract=new factory_1.default("ITCTCBModifyStorageSafeRuleInput","ITCTCBModifyStorageSafeRuleOutput",validations.tcbModifyStorageSafeRuleOutputValidation,()=>({cgi_id:167,service:"tcb",action:"ModifyStorageSafeRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeStorageSafeRuleContract=new factory_1.default("ITCTCBDescribeStorageSafeRuleInput","ITCTCBDescribeStorageSafeRuleOutput",validations.tcbDescribeStorageSafeRuleOutputValidation,()=>({cgi_id:168,service:"tcb",action:"DescribeStorageSafeRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCDNChainTaskContract=new factory_1.default("ITCTCBDescribeCDNChainTaskInput","ITCTCBDescribeCDNChainTaskOutput",validations.tcbDescribeCDNChainTaskOutputValidation,()=>({cgi_id:169,service:"tcb",action:"DescribeCDNChainTask",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeLoginConfigsContract=new factory_1.default("ITCTCBDescribeLoginConfigsInput","ITCTCBDescribeLoginConfigsOutput",validations.tcbDescribeLoginConfigsOutputValidation,()=>({cgi_id:170,service:"tcb",action:"DescribeLoginConfigs",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateLoginConfigContract=new factory_1.default("ITCTCBCreateLoginConfigInput","ITCTCBCreateLoginConfigOutput",validations.tcbCreateLoginConfigOutputValidation,()=>({cgi_id:171,service:"tcb",action:"CreateLoginConfig",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbUpdateLoginConfigContract=new factory_1.default("ITCTCBUpdateLoginConfigInput","ITCTCBUpdateLoginConfigOutput",validations.tcbUpdateLoginConfigOutputValidation,()=>({cgi_id:172,service:"tcb",action:"UpdateLoginConfig",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeSecurityRuleContract=new factory_1.default("ITCTCBDescribeSecurityRuleInput","ITCTCBDescribeSecurityRuleOutput",validations.tcbDescribeSecurityRuleOutputValidation,()=>({cgi_id:173,service:"tcb",action:"DescribeSecurityRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifySecurityRuleContract=new factory_1.default("ITCTCBModifySecurityRuleInput","ITCTCBModifySecurityRuleOutput",validations.tcbModifySecurityRuleOutputValidation,()=>({cgi_id:174,service:"tcb",action:"ModifySecurityRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateStaticStoreContract=new factory_1.default("ITCTCBCreateStaticStoreInput","ITCTCBCreateStaticStoreOutput",validations.tcbCreateStaticStoreOutputValidation,()=>({cgi_id:175,service:"tcb",action:"CreateStaticStore",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDestroyStaticStoreContract=new factory_1.default("ITCTCBDestroyStaticStoreInput","ITCTCBDestroyStaticStoreOutput",validations.tcbDestroyStaticStoreOutputValidation,()=>({cgi_id:176,service:"tcb",action:"DestroyStaticStore",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeStaticStoreContract=new factory_1.default("ITCTCBDescribeStaticStoreInput","ITCTCBDescribeStaticStoreOutput",validations.tcbDescribeStaticStoreOutputValidation,()=>({cgi_id:177,service:"tcb",action:"DescribeStaticStore",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeEnvLimitContract=new factory_1.default("ITCTCBDescribeEnvLimitInput","ITCTCBDescribeEnvLimitOutput",validations.tcbDescribeEnvLimitOutputValidation,()=>({cgi_id:178,service:"tcb",action:"DescribeEnvLimit",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeAccountInfoByPlatformIdContract=new factory_1.default("ITCTCBDescribeAccountInfoByPlatformIdInput","ITCTCBDescribeAccountInfoByPlatformIdOutput",validations.tcbDescribeAccountInfoByPlatformIdOutputValidation,()=>({cgi_id:179,service:"tcb",action:"DescribeAccountInfoByPlatformId",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeEnvFreeQuotaContract=new factory_1.default("ITCTCBDescribeEnvFreeQuotaInput","ITCTCBDescribeEnvFreeQuotaOutput",validations.tcbDescribeEnvFreeQuotaOutputValidation,()=>({cgi_id:180,service:"tcb",action:"DescribeEnvFreeQuota",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeHostingDomainContract=new factory_1.default("ITCTCBDescribeHostingDomainInput","ITCTCBDescribeHostingDomainOutput",validations.tcbDescribeHostingDomainOutputValidation,()=>({cgi_id:181,service:"tcb",action:"DescribeHostingDomain",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunResourceContract=new factory_1.default("ITCTCBDescribeCloudBaseRunResourceInput","ITCTCBDescribeCloudBaseRunResourceOutput",validations.tcbDescribeCloudBaseRunResourceOutputValidation,()=>({cgi_id:182,service:"tcb",action:"DescribeCloudBaseRunResource",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunServersContract=new factory_1.default("ITCTCBDescribeCloudBaseRunServersInput","ITCTCBDescribeCloudBaseRunServersOutput",validations.tcbDescribeCloudBaseRunServersOutputValidation,()=>({cgi_id:183,service:"tcb",action:"DescribeCloudBaseRunServers",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateCloudBaseRunResourceContract=new factory_1.default("ITCTCBCreateCloudBaseRunResourceInput","ITCTCBCreateCloudBaseRunResourceOutput",validations.tcbCreateCloudBaseRunResourceOutputValidation,()=>({cgi_id:184,service:"tcb",action:"CreateCloudBaseRunResource",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunBuildServerContract=new factory_1.default("ITCTCBDescribeCloudBaseRunBuildServerInput","ITCTCBDescribeCloudBaseRunBuildServerOutput",validations.tcbDescribeCloudBaseRunBuildServerOutputValidation,()=>({cgi_id:210,service:"tcb",action:"DescribeCloudBaseRunBuildServer",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunServerContract=new factory_1.default("ITCTCBDescribeCloudBaseRunServerInput","ITCTCBDescribeCloudBaseRunServerOutput",validations.tcbDescribeCloudBaseRunServerOutputValidation,()=>({cgi_id:211,service:"tcb",action:"DescribeCloudBaseRunServer",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunContainerSpecContract=new factory_1.default("ITCTCBDescribeCloudBaseRunContainerSpecInput","ITCTCBDescribeCloudBaseRunContainerSpecOutput",validations.tcbDescribeCloudBaseRunContainerSpecOutputValidation,()=>({cgi_id:212,service:"tcb",action:"DescribeCloudBaseRunContainerSpec",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateCloudBaseRunServerVersionContract=new factory_1.default("ITCTCBCreateCloudBaseRunServerVersionInput","ITCTCBCreateCloudBaseRunServerVersionOutput",validations.tcbCreateCloudBaseRunServerVersionOutputValidation,()=>({cgi_id:213,service:"tcb",action:"CreateCloudBaseRunServerVersion",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunServerVersionContract=new factory_1.default("ITCTCBDescribeCloudBaseRunServerVersionInput","ITCTCBDescribeCloudBaseRunServerVersionOutput",validations.tcbDescribeCloudBaseRunServerVersionOutputValidation,()=>({cgi_id:214,service:"tcb",action:"DescribeCloudBaseRunServerVersion",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbEstablishCloudBaseRunServerContract=new factory_1.default("ITCTCBEstablishCloudBaseRunServerInput","ITCTCBEstablishCloudBaseRunServerOutput",validations.tcbEstablishCloudBaseRunServerOutputValidation,()=>({cgi_id:215,service:"tcb",action:"EstablishCloudBaseRunServer",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteCloudBaseRunResourceContract=new factory_1.default("ITCTCBDeleteCloudBaseRunResourceInput","ITCTCBDeleteCloudBaseRunResourceOutput",validations.tcbDeleteCloudBaseRunResourceOutputValidation,()=>({cgi_id:216,service:"tcb",action:"DeleteCloudBaseRunResource",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunPodListContract=new factory_1.default("ITCTCBDescribeCloudBaseRunPodListInput","ITCTCBDescribeCloudBaseRunPodListOutput",validations.tcbDescribeCloudBaseRunPodListOutputValidation,()=>({cgi_id:217,service:"tcb",action:"DescribeCloudBaseRunPodList",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunBuildLogContract=new factory_1.default("ITCTCBDescribeCloudBaseRunBuildLogInput","ITCTCBDescribeCloudBaseRunBuildLogOutput",validations.tcbDescribeCloudBaseRunBuildLogOutputValidation,()=>({cgi_id:218,service:"tcb",action:"DescribeCloudBaseRunBuildLog",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseBuildServiceContract=new factory_1.default("ITCTCBDescribeCloudBaseBuildServiceInput","ITCTCBDescribeCloudBaseBuildServiceOutput",validations.tcbDescribeCloudBaseBuildServiceOutputValidation,()=>({cgi_id:219,service:"tcb",action:"DescribeCloudBaseBuildService",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunVersionExceptionContract=new factory_1.default("ITCTCBDescribeCloudBaseRunVersionExceptionInput","ITCTCBDescribeCloudBaseRunVersionExceptionOutput",validations.tcbDescribeCloudBaseRunVersionExceptionOutputValidation,()=>({cgi_id:220,service:"tcb",action:"DescribeCloudBaseRunVersionException",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyCloudBaseRunServerVersionContract=new factory_1.default("ITCTCBModifyCloudBaseRunServerVersionInput","ITCTCBModifyCloudBaseRunServerVersionOutput",validations.tcbModifyCloudBaseRunServerVersionOutputValidation,()=>({cgi_id:221,service:"tcb",action:"ModifyCloudBaseRunServerVersion",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseGWAPIContract=new factory_1.default("ITCTCBDescribeCloudBaseGWAPIInput","ITCTCBDescribeCloudBaseGWAPIOutput",validations.tcbDescribeCloudBaseGWAPIOutputValidation,()=>({cgi_id:222,service:"tcb",action:"DescribeCloudBaseGWAPI",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunBuildStagesContract=new factory_1.default("ITCTCBDescribeCloudBaseRunBuildStagesInput","ITCTCBDescribeCloudBaseRunBuildStagesOutput",validations.tcbDescribeCloudBaseRunBuildStagesOutputValidation,()=>({cgi_id:223,service:"tcb",action:"DescribeCloudBaseRunBuildStages",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunBuildStepsContract=new factory_1.default("ITCTCBDescribeCloudBaseRunBuildStepsInput","ITCTCBDescribeCloudBaseRunBuildStepsOutput",validations.tcbDescribeCloudBaseRunBuildStepsOutputValidation,()=>({cgi_id:224,service:"tcb",action:"DescribeCloudBaseRunBuildSteps",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunBuildStepLogContract=new factory_1.default("ITCTCBDescribeCloudBaseRunBuildStepLogInput","ITCTCBDescribeCloudBaseRunBuildStepLogOutput",validations.tcbDescribeCloudBaseRunBuildStepLogOutputValidation,()=>({cgi_id:225,service:"tcb",action:"DescribeCloudBaseRunBuildStepLog",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteCloudBaseRunServerVersionContract=new factory_1.default("ITCTCBDeleteCloudBaseRunServerVersionInput","ITCTCBDeleteCloudBaseRunServerVersionOutput",validations.tcbDeleteCloudBaseRunServerVersionOutputValidation,()=>({cgi_id:226,service:"tcb",action:"DeleteCloudBaseRunServerVersion",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteCloudBaseRunImageRepoContract=new factory_1.default("ITCTCBDeleteCloudBaseRunImageRepoInput","ITCTCBDeleteCloudBaseRunImageRepoOutput",validations.tcbDeleteCloudBaseRunImageRepoOutputValidation,()=>({cgi_id:227,service:"tcb",action:"DeleteCloudBaseRunImageRepo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbRollUpdateCloudBaseRunServerVersionContract=new factory_1.default("ITCTCBRollUpdateCloudBaseRunServerVersionInput","ITCTCBRollUpdateCloudBaseRunServerVersionOutput",validations.tcbRollUpdateCloudBaseRunServerVersionOutputValidation,()=>({cgi_id:228,service:"tcb",action:"RollUpdateCloudBaseRunServerVersion",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyCloudBaseRunServerFlowConfContract=new factory_1.default("ITCTCBModifyCloudBaseRunServerFlowConfInput","ITCTCBModifyCloudBaseRunServerFlowConfOutput",validations.tcbModifyCloudBaseRunServerFlowConfOutputValidation,()=>({cgi_id:229,service:"tcb",action:"ModifyCloudBaseRunServerFlowConf",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteCloudBaseRunServerContract=new factory_1.default("ITCTCBDeleteCloudBaseRunServerInput","ITCTCBDeleteCloudBaseRunServerOutput",validations.tcbDeleteCloudBaseRunServerOutputValidation,()=>({cgi_id:230,service:"tcb",action:"DeleteCloudBaseRunServer",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseCodeReposContract=new factory_1.default("ITCTCBDescribeCloudBaseCodeReposInput","ITCTCBDescribeCloudBaseCodeReposOutput",validations.tcbDescribeCloudBaseCodeReposOutputValidation,()=>({cgi_id:231,service:"tcb",action:"DescribeCloudBaseCodeRepos",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseCodeBranchContract=new factory_1.default("ITCTCBDescribeCloudBaseCodeBranchInput","ITCTCBDescribeCloudBaseCodeBranchOutput",validations.tcbDescribeCloudBaseCodeBranchOutputValidation,()=>({cgi_id:232,service:"tcb",action:"DescribeCloudBaseCodeBranch",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateHostingDomainContract=new factory_1.default("ITCTCBCreateHostingDomainInput","ITCTCBCreateHostingDomainOutput",validations.tcbCreateHostingDomainOutputValidation,()=>({cgi_id:233,service:"tcb",action:"CreateHostingDomain",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribePostpayPackageListContract=new factory_1.default("ITCTCBDescribePostpayPackageListInput","ITCTCBDescribePostpayPackageListOutput",validations.tcbDescribePostpayPackageListOutputValidation,()=>({cgi_id:233,service:"tcb",action:"DescribePostpayPackageList",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteHostingDomainContract=new factory_1.default("ITCTCBDeleteHostingDomainInput","ITCTCBDeleteHostingDomainOutput",validations.tcbDeleteHostingDomainOutputValidation,()=>({cgi_id:234,service:"tcb",action:"DeleteHostingDomain",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbQueryActivityPriceContract=new factory_1.default("ITCTCBQueryActivityPriceInput","ITCTCBQueryActivityPriceOutput",validations.tcbQueryActivityPriceOutputValidation,()=>({cgi_id:234,service:"tcb",action:"QueryActivityPrice",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyHostingDomainContract=new factory_1.default("ITCTCBModifyHostingDomainInput","ITCTCBModifyHostingDomainOutput",validations.tcbModifyHostingDomainOutputValidation,()=>({cgi_id:235,service:"tcb",action:"ModifyHostingDomain",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCheckQualificationContract=new factory_1.default("ITCTCBCheckQualificationInput","ITCTCBCheckQualificationOutput",validations.tcbCheckQualificationOutputValidation,()=>({cgi_id:235,service:"tcb",action:"CheckQualification",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbOnlineHostingDomainContract=new factory_1.default("ITCTCBOnlineHostingDomainInput","ITCTCBOnlineHostingDomainOutput",validations.tcbOnlineHostingDomainOutputValidation,()=>({cgi_id:236,service:"tcb",action:"OnlineHostingDomain",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateActivityDealContract=new factory_1.default("ITCTCBCreateActivityDealInput","ITCTCBCreateActivityDealOutput",validations.tcbCreateActivityDealOutputValidation,()=>({cgi_id:236,service:"tcb",action:"CreateActivityDeal",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeActivityGoodsContract=new factory_1.default("ITCTCBDescribeActivityGoodsInput","ITCTCBDescribeActivityGoodsOutput",validations.tcbDescribeActivityGoodsOutputValidation,()=>({cgi_id:237,service:"tcb",action:"DescribeActivityGoods",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbInqueryPackagePriceContract=new factory_1.default("ITCTCBInqueryPackagePriceInput","ITCTCBInqueryPackagePriceOutput",validations.tcbInqueryPackagePriceOutputValidation,()=>({cgi_id:238,service:"tcb",action:"InqueryPackagePrice",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeEnvPostpayPackageContract=new factory_1.default("ITCTCBDescribeEnvPostpayPackageInput","ITCTCBDescribeEnvPostpayPackageOutput",validations.tcbDescribeEnvPostpayPackageOutputValidation,()=>({cgi_id:239,service:"tcb",action:"DescribeEnvPostpayPackage",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribePostpayQuotaLimitContract=new factory_1.default("ITCTCBDescribePostpayQuotaLimitInput","ITCTCBDescribePostpayQuotaLimitOutput",validations.tcbDescribePostpayQuotaLimitOutputValidation,()=>({cgi_id:240,service:"tcb",action:"DescribePostpayQuotaLimit",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbUpdatePostpayQuotaLimitStatusContract=new factory_1.default("ITCTCBUpdatePostpayQuotaLimitStatusInput","ITCTCBUpdatePostpayQuotaLimitStatusOutput",validations.tcbUpdatePostpayQuotaLimitStatusOutputValidation,()=>({cgi_id:241,service:"tcb",action:"UpdatePostpayQuotaLimitStatus",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbUpdatePostpayQuotaLimitContract=new factory_1.default("ITCTCBUpdatePostpayQuotaLimitInput","ITCTCBUpdatePostpayQuotaLimitOutput",validations.tcbUpdatePostpayQuotaLimitOutputValidation,()=>({cgi_id:242,service:"tcb",action:"UpdatePostpayQuotaLimit",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbUpdateScfConfigContract=new factory_1.default("ITCTCBUpdateScfConfigInput","ITCTCBUpdateScfConfigOutput",validations.tcbUpdateScfConfigOutputValidation,()=>({cgi_id:243,service:"tcb",action:"UpdateScfConfig",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateInstallExtensionTaskContract=new factory_1.default("ITCTCBCreateInstallExtensionTaskInput","ITCTCBCreateInstallExtensionTaskOutput",validations.tcbCreateInstallExtensionTaskOutputValidation,()=>({cgi_id:244,service:"tcb",action:"CreateInstallExtensionTask",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateUninstallExtensionTaskContract=new factory_1.default("ITCTCBCreateUninstallExtensionTaskInput","ITCTCBCreateUninstallExtensionTaskOutput",validations.tcbCreateUninstallExtensionTaskOutputValidation,()=>({cgi_id:245,service:"tcb",action:"CreateUninstallExtensionTask",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeExtensionInstalledContract=new factory_1.default("ITCTCBDescribeExtensionInstalledInput","ITCTCBDescribeExtensionInstalledOutput",validations.tcbDescribeExtensionInstalledOutputValidation,()=>({cgi_id:246,service:"tcb",action:"DescribeExtensionInstalled",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeExtensionTaskStatusContract=new factory_1.default("ITCTCBDescribeExtensionTaskStatusInput","ITCTCBDescribeExtensionTaskStatusOutput",validations.tcbDescribeExtensionTaskStatusOutputValidation,()=>({cgi_id:247,service:"tcb",action:"DescribeExtensionTaskStatus",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeExtensionTemplatesContract=new factory_1.default("ITCTCBDescribeExtensionTemplatesInput","ITCTCBDescribeExtensionTemplatesOutput",validations.tcbDescribeExtensionTemplatesOutputValidation,()=>({cgi_id:248,service:"tcb",action:"DescribeExtensionTemplates",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeExtensionUpgradeContract=new factory_1.default("ITCTCBDescribeExtensionUpgradeInput","ITCTCBDescribeExtensionUpgradeOutput",validations.tcbDescribeExtensionUpgradeOutputValidation,()=>({cgi_id:249,service:"tcb",action:"DescribeExtensionUpgrade",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeExtensionsContract=new factory_1.default("ITCTCBDescribeExtensionsInput","ITCTCBDescribeExtensionsOutput",validations.tcbDescribeExtensionsOutputValidation,()=>({cgi_id:250,service:"tcb",action:"DescribeExtensions",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateUpgradeExtensionTaskContract=new factory_1.default("ITCTCBCreateUpgradeExtensionTaskInput","ITCTCBCreateUpgradeExtensionTaskOutput",validations.tcbCreateUpgradeExtensionTaskOutputValidation,()=>({cgi_id:251,service:"tcb",action:"CreateUpgradeExtensionTask",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunOperationDetailsContract=new factory_1.default("ITCTCBDescribeCloudBaseRunOperationDetailsInput","ITCTCBDescribeCloudBaseRunOperationDetailsOutput",validations.tcbDescribeCloudBaseRunOperationDetailsOutputValidation,()=>({cgi_id:252,service:"tcb",action:"DescribeCloudBaseRunOperationDetails",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunVersionSnapshotContract=new factory_1.default("ITCTCBDescribeCloudBaseRunVersionSnapshotInput","ITCTCBDescribeCloudBaseRunVersionSnapshotOutput",validations.tcbDescribeCloudBaseRunVersionSnapshotOutputValidation,()=>({cgi_id:253,service:"tcb",action:"DescribeCloudBaseRunVersionSnapshot",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeQcloudSceneContract=new factory_1.default("ITCTCBDescribeQcloudSceneInput","ITCTCBDescribeQcloudSceneOutput",validations.tcbDescribeQcloudSceneOutputValidation,()=>({cgi_id:254,service:"tcb",action:"DescribeQcloudScene",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeSmsQuotasContract=new factory_1.default("ITCTCBDescribeSmsQuotasInput","ITCTCBDescribeSmsQuotasOutput",validations.tcbDescribeSmsQuotasOutputValidation,()=>({cgi_id:255,service:"tcb",action:"DescribeSmsQuotas",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeSmsAttrInfoContract=new factory_1.default("ITCTCBDescribeSmsAttrInfoInput","ITCTCBDescribeSmsAttrInfoOutput",validations.tcbDescribeSmsAttrInfoOutputValidation,()=>({cgi_id:256,service:"tcb",action:"DescribeSmsAttrInfo",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeTcbBalanceContract=new factory_1.default("ITCTCBDescribeTcbBalanceInput","ITCTCBDescribeTcbBalanceOutput",validations.tcbDescribeTcbBalanceOutputValidation,()=>({cgi_id:257,service:"tcb",action:"DescribeTcbBalance",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeSmsRecordsContract=new factory_1.default("ITCTCBDescribeSmsRecordsInput","ITCTCBDescribeSmsRecordsOutput",validations.tcbDescribeSmsRecordsOutputValidation,()=>({cgi_id:258,service:"tcb",action:"DescribeSmsRecords",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCloudBaseRunServiceDomainContract=new factory_1.default("ITCTCBDescribeCloudBaseRunServiceDomainInput","ITCTCBDescribeCloudBaseRunServiceDomainOutput",validations.tcbDescribeCloudBaseRunServiceDomainOutputValidation,()=>({cgi_id:259,service:"tcb",action:"DescribeCloudBaseRunServiceDomain",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyEnvContract=new factory_1.default("ITCTCBModifyEnvInput","ITCTCBModifyEnvOutput",validations.tcbModifyEnvOutputValidation,()=>({cgi_id:260,service:"tcb",action:"ModifyEnv",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeWxCloudBaseRunEnvsContract=new factory_1.default("ITCTCBDescribeWxCloudBaseRunEnvsInput","ITCTCBDescribeWxCloudBaseRunEnvsOutput",validations.tcbDescribeWxCloudBaseRunEnvsOutputValidation,()=>({cgi_id:260,service:"tcb",action:"DescribeWxCloudBaseRunEnvs",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeWxCloudBaseRunSubNetsContract=new factory_1.default("ITCTCBDescribeWxCloudBaseRunSubNetsInput","ITCTCBDescribeWxCloudBaseRunSubNetsOutput",validations.tcbDescribeWxCloudBaseRunSubNetsOutputValidation,()=>({cgi_id:261,service:"tcb",action:"DescribeWxCloudBaseRunSubNets",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbRefundPostpaidPackageContract=new factory_1.default("ITCTCBRefundPostpaidPackageInput","ITCTCBRefundPostpaidPackageOutput",validations.tcbRefundPostpaidPackageOutputValidation,()=>({cgi_id:266,service:"tcb",action:"RefundPostpaidPackage",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbQueryPostpaidPackageDealsContract=new factory_1.default("ITCTCBQueryPostpaidPackageDealsInput","ITCTCBQueryPostpaidPackageDealsOutput",validations.tcbQueryPostpaidPackageDealsOutputValidation,()=>({cgi_id:267,service:"tcb",action:"QueryPostpaidPackageDeals",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbSearchClsLogContract=new factory_1.default("ITCTCBSearchClsLogInput","ITCTCBSearchClsLogOutput",validations.tcbSearchClsLogOutputValidation,()=>({cgi_id:268,service:"tcb",action:"SearchClsLog",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeAuditRuleContract=new factory_1.default("ITCTCBDescribeAuditRuleInput","ITCTCBDescribeAuditRuleOutput",validations.tcbDescribeAuditRuleOutputValidation,()=>({cgi_id:269,service:"tcb",action:"DescribeAuditRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeCollectionsContract=new factory_1.default("ITCTCBDescribeCollectionsInput","ITCTCBDescribeCollectionsOutput",validations.tcbDescribeCollectionsOutputValidation,()=>({cgi_id:270,service:"tcb",action:"DescribeCollections",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateAuditRulesContract=new factory_1.default("ITCTCBCreateAuditRulesInput","ITCTCBCreateAuditRulesOutput",validations.tcbCreateAuditRulesOutputValidation,()=>({cgi_id:271,service:"tcb",action:"CreateAuditRules",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteAuditRuleContract=new factory_1.default("ITCTCBDeleteAuditRuleInput","ITCTCBDeleteAuditRuleOutput",validations.tcbDeleteAuditRuleOutputValidation,()=>({cgi_id:274,service:"tcb",action:"DeleteAuditRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbModifyAuditRuleContract=new factory_1.default("ITCTCBModifyAuditRuleInput","ITCTCBModifyAuditRuleOutput",validations.tcbModifyAuditRuleOutputValidation,()=>({cgi_id:275,service:"tcb",action:"ModifyAuditRule",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeAuditResultsContract=new factory_1.default("ITCTCBDescribeAuditResultsInput","ITCTCBDescribeAuditResultsOutput",validations.tcbDescribeAuditResultsOutputValidation,()=>({cgi_id:276,service:"tcb",action:"DescribeAuditResults",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbUnfreezeSecurityAuditRecordContract=new factory_1.default("ITCTCBUnfreezeSecurityAuditRecordInput","ITCTCBUnfreezeSecurityAuditRecordOutput",validations.tcbUnfreezeSecurityAuditRecordOutputValidation,()=>({cgi_id:277,service:"tcb",action:"UnfreezeSecurityAuditRecord",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeSecurityAuditConfigContract=new factory_1.default("ITCTCBDescribeSecurityAuditConfigInput","ITCTCBDescribeSecurityAuditConfigOutput",validations.tcbDescribeSecurityAuditConfigOutputValidation,()=>({cgi_id:278,service:"tcb",action:"DescribeSecurityAuditConfig",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteSecurityAuditConfigContract=new factory_1.default("ITCTCBDeleteSecurityAuditConfigInput","ITCTCBDeleteSecurityAuditConfigOutput",validations.tcbDeleteSecurityAuditConfigOutputValidation,()=>({cgi_id:279,service:"tcb",action:"DeleteSecurityAuditConfig",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateSecurityAuditConfigContract=new factory_1.default("ITCTCBCreateSecurityAuditConfigInput","ITCTCBCreateSecurityAuditConfigOutput",validations.tcbCreateSecurityAuditConfigOutputValidation,()=>({cgi_id:280,service:"tcb",action:"CreateSecurityAuditConfig",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeTriggerServiceParametersContract=new factory_1.default("ITCTCBDescribeTriggerServiceParametersInput","ITCTCBDescribeTriggerServiceParametersOutput",validations.tcbDescribeTriggerServiceParametersOutputValidation,()=>({cgi_id:281,service:"tcb",action:"DescribeTriggerServiceParameters",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateTriggerConfigsContract=new factory_1.default("ITCTCBCreateTriggerConfigsInput","ITCTCBCreateTriggerConfigsOutput",validations.tcbCreateTriggerConfigsOutputValidation,()=>({cgi_id:282,service:"tcb",action:"CreateTriggerConfigs",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeTriggerConfigsContract=new factory_1.default("ITCTCBDescribeTriggerConfigsInput","ITCTCBDescribeTriggerConfigsOutput",validations.tcbDescribeTriggerConfigsOutputValidation,()=>({cgi_id:283,service:"tcb",action:"DescribeTriggerConfigs",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbUpdateTriggerConfigContract=new factory_1.default("ITCTCBUpdateTriggerConfigInput","ITCTCBUpdateTriggerConfigOutput",validations.tcbUpdateTriggerConfigOutputValidation,()=>({cgi_id:284,service:"tcb",action:"UpdateTriggerConfig",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDeleteTriggerConfigsContract=new factory_1.default("ITCTCBDeleteTriggerConfigsInput","ITCTCBDeleteTriggerConfigsOutput",validations.tcbDeleteTriggerConfigsOutputValidation,()=>({cgi_id:285,service:"tcb",action:"DeleteTriggerConfigs",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbCreateCopyEnvTaskContract=new factory_1.default("ITCTCBCreateCopyEnvTaskInput","ITCTCBCreateCopyEnvTaskOutput",validations.tcbCreateCopyEnvTaskOutputValidation,()=>({cgi_id:286,service:"tcb",action:"CreateCopyEnvTask",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.tcbDescribeExtensionsInstalledContract=new factory_1.default("ITCTCBDescribeExtensionsInstalledInput","ITCTCBDescribeExtensionsInstalledOutput",validations.tcbDescribeExtensionsInstalledOutputValidation,()=>({cgi_id:287,service:"tcb",action:"DescribeExtensionsInstalled",version:"2018-06-08",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows});
}(require("licia/lazyImport")(require), require)
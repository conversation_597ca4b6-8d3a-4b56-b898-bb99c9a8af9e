var _typeof=require("./typeof"),setPrototypeOf=require("./setPrototypeOf"),inherits=require("./inherits");function _wrapRegExp(){module.exports=_wrapRegExp=function(e,r){return new t(e,void 0,r)};var e=RegExp.prototype,r=new WeakMap;function t(e,o,n){var p=new RegExp(e,o);return r.set(p,n||r.get(e)),setPrototypeOf(p,t.prototype)}function o(e,t){var o=r.get(t);return Object.keys(o).reduce((function(r,t){var n=o[t];if("number"==typeof n)r[t]=e[n];else{for(var p=0;void 0===e[n[p]]&&p+1<n.length;)p++;r[t]=e[n[p]]}return r}),Object.create(null))}return inherits(t,RegExp),t.prototype.exec=function(r){var t=e.exec.call(this,r);if(t){t.groups=o(t,this);var n=t.indices;n&&(n.groups=o(n,this))}return t},t.prototype[Symbol.replace]=function(t,n){if("string"==typeof n){var p=r.get(this);return e[Symbol.replace].call(this,t,n.replace(/\$<([^>]+)>/g,(function(e,r){var t=p[r];return"$"+(Array.isArray(t)?t.join("$"):t)})))}if("function"==typeof n){var i=this;return e[Symbol.replace].call(this,t,(function(){var e=arguments;return"object"!=_typeof(e[e.length-1])&&(e=[].slice.call(e)).push(o(e,i)),n.apply(this,e)}))}return e[Symbol.replace].call(this,t,n)},_wrapRegExp.apply(this,arguments)}module.exports=_wrapRegExp;
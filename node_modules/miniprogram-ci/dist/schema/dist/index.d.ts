export declare const NEW_CHECK_JSON_WAY = true;
export interface Schema {
    id?: string;
    $schema?: string;
    $ref?: string;
    title?: string;
    description?: string;
    multipleOf?: number;
    maximum?: number;
    exclusiveMaximum?: boolean;
    minimum?: number;
    exclusiveMinimum?: boolean;
    maxLength?: number;
    minLength?: number;
    pattern?: string | RegExp;
    additionalItems?: boolean | Schema;
    items?: Schema | Schema[];
    maxItems?: number;
    minItems?: number;
    uniqueItems?: boolean;
    maxProperties?: number;
    minProperties?: number;
    required?: string[];
    additionalProperties?: boolean | Schema;
    definitions?: {
        [name: string]: Schema;
    };
    properties?: {
        [name: string]: Schema;
    };
    patternProperties?: {
        [name: string]: Schema;
    };
    dependencies?: {
        [name: string]: Schema | string[];
    };
    'enum'?: any[];
    type?: string | string[];
    format?: string;
    allOf?: Schema[];
    anyOf?: Schema[];
    oneOf?: Schema[];
    not?: Schema;
}
export interface ISchema extends Schema {
    $version: number;
}
export interface IValidateResult {
    warning: string;
    error: Array<{
        errorType: string;
        errorProperty: string;
        correctType?: Array<string>;
        requireProperty?: string;
    }>;
}
export declare const config: {
    [key: string]: ISchema;
};
export declare type FILE = 'app' | 'ext' | 'game' | 'page' | 'plugin' | 'pluginpage' | 'projectconfig' | 'projectprivateconfig' | 'sitemap' | 'theme';

'use strict';
var parent = require('../../stable/array');
require('../../modules/esnext.array.from-async');
require('../../modules/esnext.array.group');
require('../../modules/esnext.array.group-to-map');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.array.find-last');
require('../../modules/esnext.array.find-last-index');
require('../../modules/esnext.array.group-by');
require('../../modules/esnext.array.group-by-to-map');
require('../../modules/esnext.array.to-reversed');
require('../../modules/esnext.array.to-sorted');
require('../../modules/esnext.array.to-spliced');
require('../../modules/esnext.array.with');

module.exports = parent;

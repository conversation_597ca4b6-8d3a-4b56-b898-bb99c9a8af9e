// 测试配置文件
const testConfig = {
  // 测试环境配置
  environment: {
    cloudEnv: 'xinan-ai-test', // 测试环境ID
    apiTimeout: 10000, // API超时时间
    maxRetries: 3, // 最大重试次数
    testDataPrefix: 'test_' // 测试数据前缀
  },
  
  // 测试用户配置
  testUsers: [
    {
      openid: 'test_user_001',
      nickname: '测试用户1',
      avatarUrl: 'https://example.com/avatar1.png'
    },
    {
      openid: 'test_user_002', 
      nickname: '测试用户2',
      avatarUrl: 'https://example.com/avatar2.png'
    }
  ],
  
  // 测试数据配置
  testData: {
    // 焦虑倾诉测试数据
    anxietyTexts: [
      '我最近感到很焦虑，因为工作压力很大，不知道该怎么办',
      '明天要考试了，我很紧张，担心考不好',
      '和朋友发生了矛盾，我不知道该如何处理这种关系',
      '找工作一直没有结果，我开始怀疑自己的能力',
      '家里的经济压力让我喘不过气来'
    ],
    
    // 任务测试数据
    tasks: [
      {
        title: '制定学习计划',
        description: '为即将到来的考试制定详细的复习计划',
        priority: 'high',
        category: '学习',
        estimatedTime: 30
      },
      {
        title: '整理工作资料',
        description: '整理桌面上的工作文件和资料',
        priority: 'medium',
        category: '工作',
        estimatedTime: 45
      },
      {
        title: '运动锻炼',
        description: '进行30分钟的有氧运动',
        priority: 'low',
        category: '健康',
        estimatedTime: 30
      }
    ],
    
    // 心情测试数据
    moods: [
      {
        primary: 'happy',
        intensity: 8,
        note: '今天工作顺利，心情很好'
      },
      {
        primary: 'anxious',
        intensity: 6,
        note: '明天有重要会议，有点紧张'
      },
      {
        primary: 'calm',
        intensity: 7,
        note: '做了冥想练习，感觉很平静'
      }
    ]
  },
  
  // 性能测试配置
  performance: {
    // 并发用户数
    concurrentUsers: 10,
    // 测试持续时间（秒）
    duration: 60,
    // 请求间隔（毫秒）
    requestInterval: 1000,
    // 性能阈值
    thresholds: {
      responseTime: 2000, // 响应时间阈值（毫秒）
      errorRate: 0.05, // 错误率阈值（5%）
      throughput: 100 // 吞吐量阈值（请求/分钟）
    }
  },
  
  // 功能测试配置
  functional: {
    // 测试用例超时时间
    timeout: 30000,
    // 是否清理测试数据
    cleanup: true,
    // 测试报告路径
    reportPath: './test/reports/',
    // 截图路径
    screenshotPath: './test/screenshots/'
  },
  
  // 云函数测试配置
  cloudFunctions: {
    // 需要测试的云函数列表
    functions: [
      'auth',
      'anxiety', 
      'tasks',
      'mood',
      'notification',
      'achievements',
      'user'
    ],
    // 测试数据库集合
    collections: [
      'users',
      'anxietyRecords',
      'tasks',
      'moodJournals',
      'userAchievements',
      'subscriptions'
    ]
  },
  
  // 安全测试配置
  security: {
    // SQL注入测试
    sqlInjection: [
      "'; DROP TABLE users; --",
      "' OR '1'='1",
      "'; SELECT * FROM users; --"
    ],
    // XSS测试
    xssPayloads: [
      "<script>alert('XSS')</script>",
      "javascript:alert('XSS')",
      "<img src=x onerror=alert('XSS')>"
    ],
    // 权限测试
    unauthorizedAccess: [
      'other_user_openid',
      'invalid_openid',
      null,
      undefined
    ]
  },
  
  // 兼容性测试配置
  compatibility: {
    // 微信版本
    wechatVersions: [
      '8.0.0',
      '8.0.10',
      '8.0.20',
      '8.0.30'
    ],
    // 操作系统
    platforms: [
      'iOS 14',
      'iOS 15',
      'iOS 16',
      'Android 10',
      'Android 11',
      'Android 12'
    ],
    // 设备型号
    devices: [
      'iPhone 12',
      'iPhone 13',
      'iPhone 14',
      'Samsung Galaxy S21',
      'Huawei P40',
      'Xiaomi Mi 11'
    ]
  },
  
  // 监控配置
  monitoring: {
    // 监控指标
    metrics: [
      'response_time',
      'error_rate',
      'throughput',
      'cpu_usage',
      'memory_usage',
      'database_connections'
    ],
    // 告警阈值
    alerts: {
      responseTime: 3000,
      errorRate: 0.1,
      cpuUsage: 0.8,
      memoryUsage: 0.8
    },
    // 监控间隔（秒）
    interval: 30
  }
};

// 测试工具函数
const testUtils = {
  // 生成随机测试数据
  generateRandomData: {
    // 生成随机字符串
    randomString: (length = 10) => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    },
    
    // 生成随机日期
    randomDate: (start = new Date(2023, 0, 1), end = new Date()) => {
      return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    },
    
    // 生成随机整数
    randomInt: (min = 0, max = 100) => {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    // 生成随机布尔值
    randomBoolean: () => {
      return Math.random() < 0.5;
    }
  },
  
  // 测试断言函数
  assertions: {
    // 检查响应格式
    checkResponseFormat: (response) => {
      return response && 
             typeof response.code === 'number' &&
             typeof response.message === 'string' &&
             response.hasOwnProperty('data');
    },
    
    // 检查成功响应
    checkSuccessResponse: (response) => {
      return testUtils.assertions.checkResponseFormat(response) && 
             response.code === 0;
    },
    
    // 检查错误响应
    checkErrorResponse: (response) => {
      return testUtils.assertions.checkResponseFormat(response) && 
             response.code !== 0;
    },
    
    // 检查数据完整性
    checkDataIntegrity: (data, requiredFields) => {
      if (!data || typeof data !== 'object') return false;
      return requiredFields.every(field => data.hasOwnProperty(field));
    }
  },
  
  // 性能测试工具
  performance: {
    // 测量执行时间
    measureTime: async (fn) => {
      const start = Date.now();
      const result = await fn();
      const end = Date.now();
      return {
        result,
        duration: end - start
      };
    },
    
    // 并发测试
    concurrentTest: async (fn, concurrency = 10, iterations = 100) => {
      const results = [];
      const promises = [];
      
      for (let i = 0; i < concurrency; i++) {
        const promise = (async () => {
          const iterationResults = [];
          for (let j = 0; j < Math.ceil(iterations / concurrency); j++) {
            try {
              const result = await testUtils.performance.measureTime(fn);
              iterationResults.push(result);
            } catch (error) {
              iterationResults.push({ error });
            }
          }
          return iterationResults;
        })();
        promises.push(promise);
      }
      
      const concurrentResults = await Promise.all(promises);
      concurrentResults.forEach(iterationResults => {
        results.push(...iterationResults);
      });
      
      return results;
    }
  },
  
  // 数据清理工具
  cleanup: {
    // 清理测试数据
    cleanupTestData: async (collections) => {
      // 这里应该实现清理逻辑
      console.log('清理测试数据:', collections);
    },
    
    // 重置测试环境
    resetTestEnvironment: async () => {
      // 这里应该实现重置逻辑
      console.log('重置测试环境');
    }
  }
};

// 导出配置和工具
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testConfig,
    testUtils
  };
} else {
  // 小程序环境
  global.testConfig = testConfig;
  global.testUtils = testUtils;
}

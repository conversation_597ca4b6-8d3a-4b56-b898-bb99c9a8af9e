“心安AI”小程序产品需求文档 (最终版)
文档版本: Final V1.0
创建日期: 2025年7月6日
产品形态: 微信小程序

1. 产品简介与愿景
1.1 背景与问题
在快节奏的现代社会中，无论是学生的学业压力、职场人士的工作与生活压力，还是中年群体面临的家庭与养老压力，都日益沉重。焦虑、抑郁等心理健康问题逐渐普遍化，成为困扰许多人的“时代病”。大多数焦虑的根源在于，人们面对看似庞大、模糊且棘手的“问题集合”，感到无从下手，从而产生失控感和持续的负面情绪。

1.2 产品定位
“心安AI” 是一款专注于缓解焦虑情绪的AI心理支持微信小程序。它致力于成为用户在微信里最方便触达的“AI情绪管理伙伴”。

1.3 产品愿景
我们不旨在替代专业的心理咨询，而是希望通过智能化的引导，帮助用户将压得喘不过气的“一团乱麻”梳理成清晰、可控的行动路径，让他们通过一步步的行动重获对生活的掌控感，实现“在行动中消解焦虑”的目标。

2. 目标用户与典型场景
2.1 目标用户画像
焦虑的学生 (小李): 16-22岁，面临升学、考试、论文等多重压力。对未来感到迷茫，学习任务繁重不知从何开始。

典型场景: “下个月就要期末考了，五门课要复习，感觉什么都没看，肯定要挂科了，我该怎么办？”

迷茫的职场新人 (张伟): 23-30岁，对工作任务、人际关系和个人成长感到压力。对复杂项目感到畏惧，担心无法按时完成。

典型场景: “老板突然交给我一个大项目，下周就要看到初步方案，我一点头绪都没有，感觉自己能力不行，要被淘汰了。”

承压的中年人 (王女士): 35-50岁，家庭的中流砥柱，同时为父母健康、子女教育、个人事业和家庭财务担忧，精力被多方撕扯。

典型场景: “妈妈最近身体不好，孩子马上面临小升初，公司项目又在关键期，感觉每件事都火烧眉毛，我快撑不住了。”

3. V1.0 核心功能模块 (MVP)
3.1 核心用户流程
打开小程序 -> 倾诉焦虑 -> AI智能拆解任务 -> 生成任务清单 -> 订阅提醒 -> 执行与反馈 -> 生成成就卡片 -> 分享或自我激励

3.2 功能详述
模块名称

功能描述 (微信小程序实现)

优先级

1. 焦虑倾诉 (Anxiety Dump)

- 提供一个安全、私密的界面，让用户通过文字输入或调用微信的语音输入接口，无压力地倾诉焦虑。<br>- 界面设计简洁、温暖，底部有清晰的输入框和语音按钮。<br>- 语音输入实时转为文字，方便用户确认。

高

2. AI 智能拆解 (AI Task Breakdown)

- (核心引擎，后台实现) AI接收用户的焦虑描述后，通过自然语言处理（NLP）识别核心焦虑源、关键任务和情绪关键词。<br>- 智能拆解: 将模糊的焦虑（如“复习不完”）拆解为一系列具体的、小颗粒度的、可执行的任务清单（如“今晚7-8点，复习数学第一章公式”）。<br>- 设定优先级和时间建议: AI会根据任务的紧急性和重要性，为用户建议一个初步的执行顺序和时间规划。

高

3. 任务清单管理 (Actionable Checklist)

- 将AI生成的任务以清单（Checklist）形式呈现给用户。<br>- 用户可以查看、勾选完成、左滑删除或点击编辑单个任务，交互方式符合小程序用户习惯。<br>- 完成任务时，有即时的、积极的动画反馈（如打勾动画、弹出“太棒了！”的提示）。

高

4. 订阅式提醒 (Subscribe Message)

- (小程序特色) 利用微信的**“订阅消息”**机制实现提醒功能。<br>- 在AI生成任务清单后，会引导用户授权：“需要我稍后提醒你开始任务吗？”<br>- 用户授权后，小程序才能在任务开始前发送一条服务通知。<br>- 通知文案友好、非压迫性：“叮~ 你的任务‘复习数学第一章’准备好开始了吗？”

中

5. 情绪追踪日记 (Mood Journal)

- 提供一个简单的入口，让用户每天可以快速记录自己的情绪状态（如用表情符号+一句话）。<br>- 可以在倾诉焦虑前和完成一个主要焦虑事件后，引导用户记录情绪变化，以感知自身进步。<br>- 数据安全地存储在云端数据库中。

中

6. 成就分享卡片 (Victory Card Sharing)

- (小程序特色) 当用户完成一个重要的焦虑事件（即完成其下所有子任务）时，系统会自动生成一张精美的“成就卡片”。<br>- 卡片上包含：解决的焦虑事件名称、完成的任务数、一句鼓励的话和一个小勋章。<br>- 用户可以选择将这张卡片**“分享给朋友”或“发送到文件传输助手”**（作为给自己的鼓励），利用微信生态进行自传播和正向强化。

低

4. 非功能性需求
类别

需求描述

技术选型

- 前端: 使用小程序原生或Taro/uni-app等主流框架进行开发。<br>- 后端: 强烈建议使用微信云开发，它提供云函数、云数据库、云存储等一体化服务，可以极大简化开发、降低成本，并天然解决数据安全和鉴权问题。

用户体验 (UX/UI)

- 设计规范: 遵循微信官方设计指南，保证体验一致性。<br>- 界面风格: 采用柔和、温暖的色调（如淡蓝、米白、浅绿），避免使用高饱和度、有压迫感的颜色。字体清晰易读。<br>- 交互设计: 流程简单直观，减少用户的思考和操作成本。动画效果流畅、治愈。

数据隐私与安全

- 最高优先级。 在用户首次使用时，通过弹窗明确告知隐私政策。强调所有数据存储在加密的云端数据库中，仅用户本人可见，并提供数据删除入口。

性能

- 冷启动速度: 小程序的首次加载时间应尽可能短，优化代码包大小。<br>- AI响应速度: 通过云函数调用AI接口，保证后台处理不阻塞前端界面，并设置好加载状态（Loading），避免用户在焦虑状态下长时间等待。

5. 未来发展路线图 (Roadmap)
V1.1 (上线后优化):

AI能力增强: 根据用户反馈，持续优化AI对焦虑的理解和任务拆解的合理性。

任务模式丰富: 增加“番茄钟模式”，帮助用户专注执行任务。

关联公众号: 创建同名公众号，定期推送焦虑管理、心理学科普知识，将用户从小程序引流至公众号，建立长期联系。

V2.0 (生态与深化):

引入CBT/ACT专业模型: 与心理学专家合作，将更专业的认知行为疗法（CBT）或接纳承诺疗法（ACT）流程融入产品。

付费功能探索: 推出高级功能，如无限次AI分析、深度情绪报告、专业冥想音频等，通过微信支付实现商业闭环。

内容生态建设: 对接视频号，通过短视频进行心理健康科普，吸引更多泛用户，打造小程序、公众号、视频号的内容矩阵。
// 部署配置文件
const deployConfig = {
  // 环境配置
  environments: {
    development: {
      name: '开发环境',
      cloudEnv: 'xinan-ai-dev',
      appId: 'cloud1-0gfpogwuc45d1489',
      version: '1.0.0-dev',
      description: '开发测试版本',
      features: {
        enableDebug: true,
        enableMockData: true,
        enablePerformanceMonitor: true,
        enableErrorReporting: true
      }
    },
    staging: {
      name: '预发布环境',
      cloudEnv: 'xinan-ai-staging',
      appId: 'cloud1-0gfpogwuc45d1489',
      version: '1.0.0-rc',
      description: '预发布测试版本',
      features: {
        enableDebug: false,
        enableMockData: false,
        enablePerformanceMonitor: true,
        enableErrorReporting: true
      }
    },
    production: {
      name: '生产环境',
      cloudEnv: 'cloud1-0gfpogwuc45d1489',
      appId: 'wx5682fa2b114e12de',
      version: '1.0.0',
      description: '正式发布版本',
      features: {
        enableDebug: false,
        enableMockData: false,
        enablePerformanceMonitor: true,
        enableErrorReporting: true
      }
    }
  },
  
  // 云函数配置
  cloudFunctions: {
    // 需要部署的云函数列表
    functions: [
      {
        name: 'auth',
        description: '用户认证',
        timeout: 10,
        memory: 256,
        environment: {
          NODE_ENV: 'production'
        }
      },
      {
        name: 'anxiety',
        description: '焦虑倾诉',
        timeout: 30,
        memory: 512,
        environment: {
          NODE_ENV: 'production',
          AI_API_KEY: '${AI_API_KEY}' // 从环境变量获取
        }
      },
      {
        name: 'tasks',
        description: '任务管理',
        timeout: 15,
        memory: 256,
        environment: {
          NODE_ENV: 'production'
        }
      },
      {
        name: 'mood',
        description: '情绪日记',
        timeout: 10,
        memory: 256,
        environment: {
          NODE_ENV: 'production'
        }
      },
      {
        name: 'notification',
        description: '消息推送',
        timeout: 20,
        memory: 256,
        environment: {
          NODE_ENV: 'production'
        }
      },
      {
        name: 'achievements',
        description: '成就系统',
        timeout: 15,
        memory: 256,
        environment: {
          NODE_ENV: 'production'
        }
      },
      {
        name: 'user',
        description: '用户数据',
        timeout: 15,
        memory: 256,
        environment: {
          NODE_ENV: 'production'
        }
      }
    ],
    
    // 部署配置
    deploy: {
      // 并发部署数量
      concurrency: 3,
      // 部署超时时间（秒）
      timeout: 300,
      // 是否强制更新
      force: false,
      // 是否备份旧版本
      backup: true
    }
  },
  
  // 数据库配置
  database: {
    // 集合配置
    collections: [
      {
        name: 'users',
        description: '用户信息',
        indexes: [
          { keys: { openid: 1 }, unique: true },
          { keys: { createdAt: -1 } }
        ]
      },
      {
        name: 'anxietyRecords',
        description: '焦虑记录',
        indexes: [
          { keys: { userId: 1, createdAt: -1 } },
          { keys: { status: 1 } }
        ]
      },
      {
        name: 'tasks',
        description: '任务列表',
        indexes: [
          { keys: { userId: 1, status: 1 } },
          { keys: { userId: 1, dueDate: 1 } },
          { keys: { createdAt: -1 } }
        ]
      },
      {
        name: 'moodJournals',
        description: '情绪日记',
        indexes: [
          { keys: { userId: 1, recordDate: -1 } },
          { keys: { userId: 1, createdAt: -1 } }
        ]
      },
      {
        name: 'userAchievements',
        description: '用户成就',
        indexes: [
          { keys: { userId: 1, achievementId: 1 }, unique: true },
          { keys: { userId: 1, unlockedAt: -1 } }
        ]
      },
      {
        name: 'subscriptions',
        description: '订阅消息',
        indexes: [
          { keys: { userId: 1, templateId: 1 } },
          { keys: { status: 1 } }
        ]
      }
    ],
    
    // 数据库权限配置
    permissions: {
      read: true,
      write: true,
      create: true,
      delete: false // 生产环境禁止删除
    }
  },
  
  // 存储配置
  storage: {
    // 存储桶配置
    buckets: [
      {
        name: 'user-avatars',
        description: '用户头像',
        acl: 'public-read',
        maxSize: '2MB',
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
      },
      {
        name: 'achievement-cards',
        description: '成就卡片',
        acl: 'public-read',
        maxSize: '5MB',
        allowedTypes: ['image/jpeg', 'image/png']
      },
      {
        name: 'voice-records',
        description: '语音记录',
        acl: 'private',
        maxSize: '10MB',
        allowedTypes: ['audio/mp3', 'audio/wav', 'audio/m4a']
      }
    ]
  },
  
  // 安全配置
  security: {
    // 域名白名单
    domainWhitelist: [
      'https://xinan-ai.com',
      'https://api.xinan-ai.com',
      'https://cdn.xinan-ai.com'
    ],
    
    // API限流配置
    rateLimit: {
      // 每分钟最大请求数
      maxRequestsPerMinute: 100,
      // 每小时最大请求数
      maxRequestsPerHour: 1000,
      // 每天最大请求数
      maxRequestsPerDay: 10000
    },
    
    // 数据加密
    encryption: {
      // 敏感数据加密
      enableDataEncryption: true,
      // 传输加密
      enableTransportEncryption: true
    }
  },
  
  // 监控配置
  monitoring: {
    // 性能监控
    performance: {
      enabled: true,
      sampleRate: 0.1, // 10%采样率
      thresholds: {
        responseTime: 2000,
        errorRate: 0.05
      }
    },
    
    // 错误监控
    errorTracking: {
      enabled: true,
      reportUrl: 'https://api.xinan-ai.com/errors',
      maxErrors: 100
    },
    
    // 用户行为分析
    analytics: {
      enabled: true,
      trackingId: 'UA-XXXXXXXX-X',
      events: [
        'page_view',
        'user_action',
        'error_occurred',
        'performance_metric'
      ]
    }
  },
  
  // 部署脚本配置
  scripts: {
    // 部署前脚本
    preDeploy: [
      'npm run test',
      'npm run build',
      'npm run lint'
    ],
    
    // 部署后脚本
    postDeploy: [
      'npm run verify',
      'npm run smoke-test'
    ],
    
    // 回滚脚本
    rollback: [
      'npm run rollback-functions',
      'npm run rollback-database'
    ]
  },
  
  // 版本控制
  versioning: {
    // 版本号格式
    format: 'semantic', // semantic | timestamp | custom
    
    // 自动版本号
    autoIncrement: true,
    
    // 版本标签
    tags: {
      development: 'dev',
      staging: 'rc',
      production: 'release'
    }
  },
  
  // 通知配置
  notifications: {
    // 部署通知
    deploy: {
      enabled: true,
      channels: ['email', 'webhook'],
      recipients: ['<EMAIL>']
    },
    
    // 错误通知
    error: {
      enabled: true,
      channels: ['email', 'sms'],
      recipients: ['<EMAIL>']
    }
  }
};

// 部署工具函数
const deployUtils = {
  // 获取当前环境配置
  getCurrentEnv: () => {
    const env = process.env.NODE_ENV || 'development';
    return deployConfig.environments[env];
  },
  
  // 验证配置
  validateConfig: (config) => {
    const required = ['cloudEnv', 'appId', 'version'];
    return required.every(key => config[key]);
  },
  
  // 生成部署清单
  generateManifest: (env) => {
    const config = deployConfig.environments[env];
    return {
      environment: env,
      version: config.version,
      timestamp: new Date().toISOString(),
      functions: deployConfig.cloudFunctions.functions.map(f => f.name),
      collections: deployConfig.database.collections.map(c => c.name),
      features: config.features
    };
  },
  
  // 检查部署状态
  checkDeployStatus: async (env) => {
    // 这里应该实现实际的状态检查逻辑
    return {
      status: 'success',
      timestamp: new Date().toISOString(),
      environment: env
    };
  }
};

// 导出配置
module.exports = {
  deployConfig,
  deployUtils
};

/**app.wxss**/

/* 全局变量定义 */
page {
  /* 主色调 */
  --primary-color: #4A90E2;
  --primary-light: #7BB3F0;
  --primary-dark: #2E5C8A;
  
  /* 辅助色 */
  --secondary-color: #7ED321;
  --secondary-light: #A8E65C;
  --secondary-dark: #5BA617;
  
  /* 文字颜色 */
  --text-primary: #2C3E50;
  --text-secondary: #7F8C8D;
  --text-tertiary: #BDC3C7;
  --text-disabled: #ECF0F1;
  
  /* 背景颜色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --bg-tertiary: #E9ECEF;
  --bg-overlay: rgba(0,0,0,0.5);
  
  /* 功能色 */
  --success-color: #27AE60;
  --warning-color: #F39C12;
  --error-color: #E74C3C;
  --info-color: #3498DB;
  
  /* 情绪色彩 */
  --mood-happy: #FFD93D;
  --mood-calm: #6C5CE7;
  --mood-anxious: #FD79A8;
  --mood-sad: #74B9FF;
  
  /* 字体大小 */
  --font-size-h1: 32rpx;
  --font-size-h2: 28rpx;
  --font-size-h3: 24rpx;
  --font-size-body: 28rpx;
  --font-size-body-sm: 24rpx;
  --font-size-caption: 20rpx;
  --font-size-button: 28rpx;
  --font-size-input: 28rpx;
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  --spacing-xxl: 64rpx;
  
  /* 页面边距 */
  --page-padding: 32rpx;
  --component-margin: 24rpx;
  --content-padding: 16rpx;
  
  /* 圆角 */
  --border-radius-sm: 8rpx;
  --border-radius-md: 12rpx;
  --border-radius-lg: 16rpx;
  --border-radius-xl: 24rpx;
  
  /* 阴影 */
  --shadow-sm: 0 2rpx 8rpx rgba(0,0,0,0.05);
  --shadow-md: 0 4rpx 12rpx rgba(0,0,0,0.08);
  --shadow-lg: 0 8rpx 24rpx rgba(0,0,0,0.12);
}

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: var(--bg-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  font-size: var(--font-size-body);
  color: var(--text-primary);
  line-height: 1.6;
}

/* 通用布局类 */
.container {
  padding: var(--page-padding);
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 文字样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

/* 标题样式 */
.title-h1 {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.title-h2 {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.title-h3 {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-button);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::after {
  border: none;
}

.btn-primary {
  background: var(--primary-color);
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.btn-primary:active {
  background: var(--primary-dark);
  transform: translateY(2rpx);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-secondary:active {
  background: rgba(74, 144, 226, 0.1);
}

.btn-success {
  background: var(--success-color);
  color: #FFFFFF;
}

.btn-warning {
  background: var(--warning-color);
  color: #FFFFFF;
}

.btn-error {
  background: var(--error-color);
  color: #FFFFFF;
}

.btn-disabled {
  background: var(--bg-tertiary);
  color: var(--text-disabled);
  box-shadow: none;
}

.btn-small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-body-sm);
}

.btn-large {
  padding: var(--spacing-lg) var(--spacing-xxl);
  font-size: var(--font-size-h3);
}

/* 卡片样式 */
.card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--component-margin);
}

.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1rpx solid var(--bg-tertiary);
}

.card-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.card-content {
  color: var(--text-secondary);
  line-height: 1.8;
}

/* 输入框样式 */
.input-field {
  background: var(--bg-primary);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: var(--font-size-input);
  color: var(--text-primary);
  transition: border-color 0.3s ease;
}

.input-field:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}

.input-field::placeholder {
  color: var(--text-tertiary);
}

/* 列表样式 */
.list-item {
  background: var(--bg-primary);
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
}

.list-item:active {
  background: var(--bg-secondary);
}

.list-item:last-child {
  border-bottom: none;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
  margin-right: var(--spacing-xs);
}

.tag-primary {
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.tag-error {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-size-body-sm);
  margin-left: var(--spacing-sm);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  color: var(--text-tertiary);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--font-size-body-sm);
  text-align: center;
  line-height: 1.8;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(100rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20rpx); }
  60% { transform: translateY(-10rpx); }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: var(--spacing-md);
  }
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 性能优化工具
const PerformanceUtils = {
  // 缓存管理
  cache: {
    // 内存缓存
    memoryCache: new Map(),
    
    // 设置缓存
    set: (key, value, ttl = 300000) => { // 默认5分钟过期
      const expireTime = Date.now() + ttl;
      PerformanceUtils.cache.memoryCache.set(key, {
        value,
        expireTime
      });
    },
    
    // 获取缓存
    get: (key) => {
      const cached = PerformanceUtils.cache.memoryCache.get(key);
      if (!cached) return null;
      
      if (Date.now() > cached.expireTime) {
        PerformanceUtils.cache.memoryCache.delete(key);
        return null;
      }
      
      return cached.value;
    },
    
    // 删除缓存
    delete: (key) => {
      return PerformanceUtils.cache.memoryCache.delete(key);
    },
    
    // 清空缓存
    clear: () => {
      PerformanceUtils.cache.memoryCache.clear();
    },
    
    // 缓存大小
    size: () => {
      return PerformanceUtils.cache.memoryCache.size;
    }
  },
  
  // 防抖函数
  debounce: (func, wait, immediate = false) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func(...args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func(...args);
    };
  },
  
  // 节流函数
  throttle: (func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },
  
  // 请求队列管理
  requestQueue: {
    queue: [],
    processing: false,
    maxConcurrent: 3,
    currentRequests: 0,
    
    // 添加请求到队列
    add: (requestFn, priority = 0) => {
      return new Promise((resolve, reject) => {
        PerformanceUtils.requestQueue.queue.push({
          requestFn,
          priority,
          resolve,
          reject
        });
        
        // 按优先级排序
        PerformanceUtils.requestQueue.queue.sort((a, b) => b.priority - a.priority);
        
        PerformanceUtils.requestQueue.process();
      });
    },
    
    // 处理队列
    process: async () => {
      if (PerformanceUtils.requestQueue.processing || 
          PerformanceUtils.requestQueue.currentRequests >= PerformanceUtils.requestQueue.maxConcurrent ||
          PerformanceUtils.requestQueue.queue.length === 0) {
        return;
      }
      
      PerformanceUtils.requestQueue.processing = true;
      
      while (PerformanceUtils.requestQueue.queue.length > 0 && 
             PerformanceUtils.requestQueue.currentRequests < PerformanceUtils.requestQueue.maxConcurrent) {
        
        const request = PerformanceUtils.requestQueue.queue.shift();
        PerformanceUtils.requestQueue.currentRequests++;
        
        // 异步执行请求
        (async () => {
          try {
            const result = await request.requestFn();
            request.resolve(result);
          } catch (error) {
            request.reject(error);
          } finally {
            PerformanceUtils.requestQueue.currentRequests--;
            PerformanceUtils.requestQueue.process();
          }
        })();
      }
      
      PerformanceUtils.requestQueue.processing = false;
    }
  },
  
  // 图片优化
  imageOptimization: {
    // 压缩图片
    compressImage: (filePath, quality = 0.8) => {
      return new Promise((resolve, reject) => {
        wx.compressImage({
          src: filePath,
          quality: Math.floor(quality * 100),
          success: resolve,
          fail: reject
        });
      });
    },
    
    // 获取图片信息
    getImageInfo: (src) => {
      return new Promise((resolve, reject) => {
        wx.getImageInfo({
          src,
          success: resolve,
          fail: reject
        });
      });
    },
    
    // 预加载图片
    preloadImages: (imageUrls) => {
      const promises = imageUrls.map(url => 
        PerformanceUtils.imageOptimization.getImageInfo(url).catch(() => null)
      );
      return Promise.allSettled(promises);
    }
  },
  
  // 数据预加载
  dataPreloader: {
    preloadedData: new Map(),
    
    // 预加载数据
    preload: async (key, dataLoader, ttl = 300000) => {
      try {
        const data = await dataLoader();
        PerformanceUtils.cache.set(key, data, ttl);
        PerformanceUtils.dataPreloader.preloadedData.set(key, true);
        return data;
      } catch (error) {
        console.error('数据预加载失败:', error);
        return null;
      }
    },
    
    // 获取预加载的数据
    get: (key) => {
      return PerformanceUtils.cache.get(key);
    },
    
    // 检查是否已预加载
    isPreloaded: (key) => {
      return PerformanceUtils.dataPreloader.preloadedData.has(key);
    }
  },
  
  // 性能监控
  monitor: {
    metrics: new Map(),
    
    // 开始监控
    start: (name) => {
      PerformanceUtils.monitor.metrics.set(name, {
        startTime: Date.now(),
        endTime: null,
        duration: null
      });
    },
    
    // 结束监控
    end: (name) => {
      const metric = PerformanceUtils.monitor.metrics.get(name);
      if (metric) {
        metric.endTime = Date.now();
        metric.duration = metric.endTime - metric.startTime;
        
        // 记录性能日志
        if (metric.duration > 1000) {
          console.warn(`性能警告: ${name} 耗时 ${metric.duration}ms`);
        }
        
        return metric.duration;
      }
      return null;
    },
    
    // 获取性能指标
    getMetrics: () => {
      const results = {};
      PerformanceUtils.monitor.metrics.forEach((value, key) => {
        results[key] = value;
      });
      return results;
    },
    
    // 清空指标
    clear: () => {
      PerformanceUtils.monitor.metrics.clear();
    }
  },
  
  // 内存优化
  memory: {
    // 清理未使用的数据
    cleanup: () => {
      // 清理过期缓存
      const now = Date.now();
      PerformanceUtils.cache.memoryCache.forEach((value, key) => {
        if (now > value.expireTime) {
          PerformanceUtils.cache.memoryCache.delete(key);
        }
      });
      
      // 清理预加载标记
      PerformanceUtils.dataPreloader.preloadedData.clear();
      
      // 触发垃圾回收（如果支持）
      if (typeof wx !== 'undefined' && wx.triggerGC) {
        wx.triggerGC();
      }
    },
    
    // 获取内存使用情况
    getUsage: () => {
      if (typeof wx !== 'undefined' && wx.getPerformance) {
        return wx.getPerformance().memory;
      }
      return null;
    }
  },
  
  // 网络优化
  network: {
    // 检查网络状态
    checkNetworkStatus: () => {
      return new Promise((resolve) => {
        wx.getNetworkType({
          success: (res) => {
            resolve({
              networkType: res.networkType,
              isConnected: res.networkType !== 'none'
            });
          },
          fail: () => {
            resolve({
              networkType: 'unknown',
              isConnected: false
            });
          }
        });
      });
    },
    
    // 网络请求重试
    retryRequest: async (requestFn, maxRetries = 3, delay = 1000) => {
      let lastError;
      
      for (let i = 0; i <= maxRetries; i++) {
        try {
          return await requestFn();
        } catch (error) {
          lastError = error;
          
          if (i < maxRetries) {
            // 指数退避
            const waitTime = delay * Math.pow(2, i);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      }
      
      throw lastError;
    },
    
    // 批量请求
    batchRequest: async (requests, batchSize = 5) => {
      const results = [];
      
      for (let i = 0; i < requests.length; i += batchSize) {
        const batch = requests.slice(i, i + batchSize);
        const batchResults = await Promise.allSettled(batch);
        results.push(...batchResults);
      }
      
      return results;
    }
  },
  
  // 懒加载
  lazyLoad: {
    observers: new Map(),
    
    // 创建懒加载观察器
    createObserver: (callback, options = {}) => {
      if (typeof wx !== 'undefined' && wx.createIntersectionObserver) {
        return wx.createIntersectionObserver(null, {
          threshold: options.threshold || 0.1,
          initialRatio: options.initialRatio || 0,
          observeAll: options.observeAll || false
        });
      }
      return null;
    },
    
    // 懒加载图片
    lazyLoadImages: (selector = '.lazy-image') => {
      const observer = PerformanceUtils.lazyLoad.createObserver((entries) => {
        entries.forEach(entry => {
          if (entry.intersectionRatio > 0) {
            const target = entry.target;
            const dataSrc = target.dataset.src;
            
            if (dataSrc) {
              target.src = dataSrc;
              target.removeAttribute('data-src');
              observer.unobserve(target);
            }
          }
        });
      });
      
      if (observer) {
        observer.observe(selector);
        PerformanceUtils.lazyLoad.observers.set(selector, observer);
      }
    },
    
    // 销毁观察器
    destroy: (selector) => {
      const observer = PerformanceUtils.lazyLoad.observers.get(selector);
      if (observer) {
        observer.disconnect();
        PerformanceUtils.lazyLoad.observers.delete(selector);
      }
    }
  }
};

// 自动清理定时器
setInterval(() => {
  PerformanceUtils.memory.cleanup();
}, 300000); // 每5分钟清理一次

module.exports = PerformanceUtils;

/* pages/anxiety-dump/index.wxss */

.anxiety-dump-page {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* 页面头部 */
.anxiety-dump-header {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-title {
  display: block;
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 引导文案区域 */
.guide-section {
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  margin: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
}

.guide-text {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.8;
  text-align: center;
}

/* 输入区域 */
.input-section {
  padding: var(--spacing-lg);
  position: relative;
}

.text-input {
  width: 100%;
  min-height: 400rpx;
  padding: var(--spacing-lg);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-body);
  line-height: 1.6;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.text-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}

.text-input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
}

/* 输入提示 */
.input-hints {
  position: absolute;
  top: 120rpx;
  left: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  z-index: 10;
  animation: fadeIn 0.3s ease-in-out;
}

.hint-title {
  display: block;
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.hint-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.hint-item {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-body-sm);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.hint-item:active {
  background: var(--primary-color);
  color: #FFFFFF;
  transform: scale(0.98);
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: var(--spacing-md) var(--spacing-lg);
}

.voice-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.voice-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.voice-button::after {
  border: none;
}

.voice-button:active {
  transform: scale(0.95);
}

.voice-button.recording {
  background: var(--error-color);
  animation: pulse 1s infinite;
}

.voice-button:disabled {
  background: var(--bg-tertiary);
  box-shadow: none;
}

.voice-icon {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
}

.voice-tip {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
  margin-top: var(--spacing-sm);
  text-align: center;
}

.word-count-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.word-count {
  font-size: var(--font-size-caption);
  color: var(--text-tertiary);
}

.char-limit {
  font-size: var(--font-size-caption);
  color: var(--warning-color);
  margin-top: var(--spacing-xs);
}

/* 语音识别结果 */
.voice-result {
  margin: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border-left: 4rpx solid var(--primary-color);
  animation: slideUp 0.3s ease-out;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.result-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.result-confidence {
  font-size: var(--font-size-caption);
  color: var(--success-color);
}

.result-content {
  margin-bottom: var(--spacing-lg);
}

.result-text {
  font-size: var(--font-size-body);
  color: var(--text-primary);
  line-height: 1.6;
}

.result-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.action-buttons .btn {
  flex: 1;
}

/* 历史记录 */
.history-section {
  margin: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
}

.history-header:active {
  background: var(--bg-secondary);
}

.history-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;
}

.history-list {
  animation: slideDown 0.3s ease-out;
}

.history-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: var(--bg-secondary);
}

.history-content {
  flex: 1;
}

.history-text {
  display: block;
  font-size: var(--font-size-body);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-time {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.history-status {
  margin-left: var(--spacing-md);
}

.status-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
  font-weight: var(--font-weight-medium);
}

.status-tag.completed {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

.status-tag.processing {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.status-tag.pending {
  background: rgba(52, 152, 219, 0.1);
  color: var(--info-color);
}

/* 录音状态弹窗 */
.recording-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xxl);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.recording-animation {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
}

.wave-circle {
  position: absolute;
  border: 2rpx solid var(--primary-color);
  border-radius: 50%;
  animation: wave 2s infinite;
}

.wave-1 {
  width: 120rpx;
  height: 120rpx;
  animation-delay: 0s;
}

.wave-2 {
  width: 160rpx;
  height: 160rpx;
  animation-delay: 0.5s;
}

.wave-3 {
  width: 200rpx;
  height: 200rpx;
  animation-delay: 1s;
}

.mic-icon {
  width: 64rpx;
  height: 64rpx;
  z-index: 10;
}

.recording-text {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.recording-time {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.recording-tip {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 语音处理弹窗 */
.processing-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-animation {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  background: var(--primary-color);
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

.processing-text {
  font-size: var(--font-size-body);
  color: var(--text-secondary);
}

/* 动画定义 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes wave {
  0% { transform: scale(0.8); opacity: 1; }
  100% { transform: scale(1.2); opacity: 0; }
}

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

@keyframes slideDown {
  from { max-height: 0; opacity: 0; }
  to { max-height: 1000rpx; opacity: 1; }
}

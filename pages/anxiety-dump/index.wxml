<!--pages/anxiety-dump/index.wxml-->
<view class="anxiety-dump-page">
  <!-- 页面头部 -->
  <view class="anxiety-dump-header">
    <text class="page-title">倾诉你的焦虑</text>
    <text class="page-subtitle">在这里，你可以安全地表达内心的困扰</text>
  </view>

  <!-- 引导文案 -->
  <view class="guide-section">
    <text class="guide-text">{{guideText}}</text>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <textarea 
      class="text-input"
      placeholder="{{placeholder}}"
      value="{{inputText}}"
      bindinput="onTextInput"
      bindblur="onInputBlur"
      bindfocus="onInputFocus"
      auto-height
      maxlength="-1"
      show-confirm-bar="{{false}}"
      adjust-position="{{true}}"
    />
    
    <!-- 输入提示 -->
    <view class="input-hints" wx:if="{{showHints && inputText.length === 0}}">
      <text class="hint-title">不知道从何说起？试试这些：</text>
      <view class="hint-list">
        <text 
          class="hint-item"
          wx:for="{{hintTexts}}" 
          wx:key="*this"
          bindtap="selectHint"
          data-hint="{{item}}"
        >
          {{item}}
        </text>
      </view>
    </view>
  </view>

  <!-- 工具栏 -->
  <view class="toolbar">
    <!-- 语音按钮 -->
    <view class="voice-section">
      <button 
        class="voice-button {{isRecording ? 'recording' : ''}}"
        bindtouchstart="startRecording"
        bindtouchend="stopRecording"
        bindtouchcancel="cancelRecording"
        disabled="{{isProcessingVoice}}"
      >
        <image 
          src="/images/icons/{{isRecording ? 'recording' : 'microphone'}}.png" 
          class="voice-icon" 
        />
      </button>
      <text class="voice-tip">{{voiceTip}}</text>
    </view>
    
    <!-- 字数统计 -->
    <view class="word-count-section">
      <text class="word-count">字数: {{wordCount}}</text>
      <text class="char-limit" wx:if="{{wordCount > 1000}}">建议控制在1000字以内</text>
    </view>
  </view>

  <!-- 语音识别结果 -->
  <view class="voice-result" wx:if="{{voiceResult}}">
    <view class="result-header">
      <text class="result-title">语音识别结果</text>
      <text class="result-confidence">准确度: {{voiceConfidence}}%</text>
    </view>
    <view class="result-content">
      <text class="result-text">{{voiceResult}}</text>
    </view>
    <view class="result-actions">
      <button class="btn btn-secondary btn-small" bindtap="reRecord">重新录音</button>
      <button class="btn btn-primary btn-small" bindtap="confirmVoiceResult">确认添加</button>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button 
      class="btn btn-secondary" 
      bindtap="clearInput"
      disabled="{{inputText.length === 0}}"
    >
      清空
    </button>
    <button 
      class="btn btn-primary" 
      bindtap="submitAnxiety"
      disabled="{{!canSubmit}}"
      loading="{{isSubmitting}}"
    >
      {{isSubmitting ? '分析中...' : '开始分析'}}
    </button>
  </view>

  <!-- 历史记录入口 -->
  <view class="history-section" wx:if="{{hasHistory}}">
    <view class="history-header" bindtap="toggleHistory">
      <text class="history-title">历史倾诉记录</text>
      <image 
        src="/images/icons/arrow-{{showHistory ? 'up' : 'down'}}.png" 
        class="arrow-icon"
      />
    </view>
    
    <view class="history-list" wx:if="{{showHistory}}">
      <view 
        class="history-item"
        wx:for="{{historyList}}" 
        wx:key="_id"
        bindtap="loadHistoryItem"
        data-id="{{item._id}}"
      >
        <view class="history-content">
          <text class="history-text">{{item.preview}}</text>
          <text class="history-time">{{item.timeText}}</text>
        </view>
        <view class="history-status">
          <text class="status-tag {{item.status}}">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 录音状态弹窗 -->
<view class="recording-modal" wx:if="{{isRecording}}">
  <view class="modal-overlay"></view>
  <view class="modal-content">
    <view class="recording-animation">
      <view class="wave-circle wave-1"></view>
      <view class="wave-circle wave-2"></view>
      <view class="wave-circle wave-3"></view>
      <image src="/images/icons/microphone-white.png" class="mic-icon" />
    </view>
    <text class="recording-text">正在录音...</text>
    <text class="recording-time">{{recordingTime}}s</text>
    <text class="recording-tip">松开结束录音，上滑取消</text>
  </view>
</view>

<!-- 语音处理弹窗 -->
<view class="processing-modal" wx:if="{{isProcessingVoice}}">
  <view class="modal-overlay"></view>
  <view class="modal-content">
    <view class="loading-animation">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
    <text class="processing-text">正在识别语音...</text>
  </view>
</view>

// pages/task-list/index.js
const app = getApp();

Page({
  data: {
    // 任务数据
    taskList: [],
    totalTasks: 0,
    isLoading: true,
    isLoadingMore: false,
    hasMore: true,
    
    // 筛选和排序
    currentFilter: 'all',
    filterOptions: [
      { value: 'all', label: '全部', count: 0 },
      { value: 'pending', label: '待办', count: 0 },
      { value: 'in_progress', label: '进行中', count: 0 },
      { value: 'completed', label: '已完成', count: 0 }
    ],
    
    // 弹窗状态
    showFilter: false,
    showSort: false,
    showActions: false,
    selectedTaskId: '',
    
    // 筛选设置
    filterSettings: {
      priority: 'all',
      category: 'all'
    },
    
    // 排序设置
    sortSettings: {
      field: 'createdAt',
      order: 'desc'
    },
    
    // 选项数据
    priorityOptions: [
      { value: 'all', label: '全部优先级' },
      { value: 'high', label: '紧急' },
      { value: 'medium', label: '重要' },
      { value: 'low', label: '普通' }
    ],
    
    categoryOptions: [
      { value: 'all', label: '全部分类' },
      { value: '焦虑管理', label: '焦虑管理' },
      { value: '学习', label: '学习' },
      { value: '工作', label: '工作' },
      { value: '生活', label: '生活' },
      { value: '健康', label: '健康' },
      { value: '其他', label: '其他' }
    ],
    
    sortOptions: [
      { field: 'createdAt', order: 'desc', label: '创建时间（最新）' },
      { field: 'createdAt', order: 'asc', label: '创建时间（最早）' },
      { field: 'dueDate', order: 'asc', label: '截止时间（最近）' },
      { field: 'dueDate', order: 'desc', label: '截止时间（最远）' },
      { field: 'priority', order: 'desc', label: '优先级（高到低）' },
      { field: 'priority', order: 'asc', label: '优先级（低到高）' }
    ],
    
    // 分页
    currentPage: 1,
    pageSize: 20
  },

  onLoad() {
    console.log('任务列表页面加载');
    this.loadTasks();
  },

  onShow() {
    console.log('任务列表页面显示');
    // 刷新任务列表
    this.refreshTasks();
  },

  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshTasks().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    console.log('触底加载更多');
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadMore();
    }
  },

  // 加载任务列表
  async loadTasks(reset = true) {
    try {
      if (reset) {
        this.setData({ 
          isLoading: true,
          currentPage: 1
        });
      } else {
        this.setData({ isLoadingMore: true });
      }

      const { currentFilter, filterSettings, sortSettings, currentPage, pageSize } = this.data;
      
      const res = await wx.cloud.callFunction({
        name: 'tasks',
        data: {
          action: 'getList',
          status: currentFilter === 'all' ? 'all' : currentFilter,
          priority: filterSettings.priority === 'all' ? 'all' : filterSettings.priority,
          category: filterSettings.category === 'all' ? 'all' : filterSettings.category,
          page: currentPage,
          limit: pageSize,
          sortBy: sortSettings.field,
          sortOrder: sortSettings.order
        }
      });

      if (res.result.code === 0) {
        const { tasks, pagination } = res.result.data;
        
        let taskList;
        if (reset) {
          taskList = tasks;
        } else {
          taskList = [...this.data.taskList, ...tasks];
        }
        
        this.setData({
          taskList: taskList,
          totalTasks: pagination.total,
          hasMore: currentPage < pagination.totalPages,
          currentPage: currentPage
        });
        
        // 更新筛选选项的计数
        await this.updateFilterCounts();
      } else {
        app.showError(res.result.message);
      }
    } catch (error) {
      console.error('加载任务列表失败:', error);
      app.showError('加载失败，请重试');
    } finally {
      this.setData({ 
        isLoading: false,
        isLoadingMore: false
      });
    }
  },

  // 刷新任务列表
  async refreshTasks() {
    await this.loadTasks(true);
  },

  // 加载更多
  async loadMore() {
    const nextPage = this.data.currentPage + 1;
    this.setData({ currentPage: nextPage });
    await this.loadTasks(false);
  },

  // 更新筛选选项计数
  async updateFilterCounts() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'tasks',
        data: { action: 'getStats' }
      });

      if (res.result.code === 0) {
        const stats = res.result.data;
        const filterOptions = this.data.filterOptions.map(option => {
          switch (option.value) {
            case 'all':
              return { ...option, count: stats.total };
            case 'pending':
              return { ...option, count: stats.pending };
            case 'completed':
              return { ...option, count: stats.completed };
            default:
              return option;
          }
        });
        
        this.setData({ filterOptions });
      }
    } catch (error) {
      console.error('更新筛选计数失败:', error);
    }
  },

  // 切换筛选
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    if (filter !== this.data.currentFilter) {
      this.setData({ currentFilter: filter });
      this.loadTasks(true);
    }
  },

  // 切换任务状态
  async toggleTaskStatus(e) {
    const taskId = e.currentTarget.dataset.id;
    const task = this.data.taskList.find(t => t._id === taskId);
    
    if (!task) return;
    
    try {
      const newStatus = task.status === 'completed' ? 'pending' : 'completed';
      
      const res = await wx.cloud.callFunction({
        name: 'tasks',
        data: {
          action: 'updateStatus',
          taskId: taskId,
          status: newStatus
        }
      });
      
      if (res.result.code === 0) {
        // 更新本地数据
        const updatedTasks = this.data.taskList.map(t => {
          if (t._id === taskId) {
            return { ...t, status: newStatus };
          }
          return t;
        });
        
        this.setData({ taskList: updatedTasks });
        
        if (newStatus === 'completed') {
          app.showSuccess('任务完成！');
        }
        
        // 更新计数
        this.updateFilterCounts();
      } else {
        app.showError(res.result.message);
      }
    } catch (error) {
      console.error('更新任务状态失败:', error);
      app.showError('操作失败，请重试');
    }
  },

  // 切换提醒
  async toggleReminder(e) {
    const taskId = e.currentTarget.dataset.id;
    const task = this.data.taskList.find(t => t._id === taskId);
    
    if (!task) return;
    
    try {
      const enabled = !(task.reminder && task.reminder.enabled);
      
      // 如果开启提醒，需要先请求订阅消息权限
      if (enabled) {
        const subscribeResult = await this.requestSubscribeMessage();
        if (!subscribeResult) {
          app.showError('需要订阅消息权限才能开启提醒');
          return;
        }
      }
      
      const res = await wx.cloud.callFunction({
        name: 'tasks',
        data: {
          action: 'update',
          taskId: taskId,
          'reminder.enabled': enabled
        }
      });
      
      if (res.result.code === 0) {
        // 更新本地数据
        const updatedTasks = this.data.taskList.map(t => {
          if (t._id === taskId) {
            return {
              ...t,
              reminder: {
                ...t.reminder,
                enabled: enabled
              }
            };
          }
          return t;
        });
        
        this.setData({ taskList: updatedTasks });
        
        app.showSuccess(enabled ? '提醒已开启' : '提醒已关闭');
      } else {
        app.showError(res.result.message);
      }
    } catch (error) {
      console.error('切换提醒失败:', error);
      app.showError('操作失败，请重试');
    }
  },

  // 请求订阅消息权限
  async requestSubscribeMessage() {
    return new Promise((resolve) => {
      wx.requestSubscribeMessage({
        tmplIds: ['your_template_id'], // 替换为实际的模板ID
        success: (res) => {
          console.log('订阅消息结果:', res);
          resolve(true);
        },
        fail: (err) => {
          console.error('订阅消息失败:', err);
          resolve(false);
        }
      });
    });
  },

  // 跳转到任务详情
  goToTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/task-detail/index?id=${taskId}`
    });
  },

  // 创建任务
  createTask() {
    wx.navigateTo({
      url: '/pages/task-create/index'
    });
  },

  // 显示筛选弹窗
  showFilterModal() {
    this.setData({ showFilter: true });
  },

  hideFilterModal() {
    this.setData({ showFilter: false });
  },

  // 显示排序弹窗
  showSortModal() {
    this.setData({ showSort: true });
  },

  hideSortModal() {
    this.setData({ showSort: false });
  },

  // 显示任务操作弹窗
  showTaskActions(e) {
    const taskId = e.currentTarget.dataset.id;
    this.setData({
      showActions: true,
      selectedTaskId: taskId
    });
  },

  hideActionModal() {
    this.setData({ showActions: false });
  },

  // 筛选相关方法
  selectFilterOption(e) {
    const { type, value } = e.currentTarget.dataset;
    this.setData({
      [`filterSettings.${type}`]: value
    });
  },

  resetFilter() {
    this.setData({
      filterSettings: {
        priority: 'all',
        category: 'all'
      }
    });
  },

  applyFilter() {
    this.setData({ showFilter: false });
    this.loadTasks(true);
  },

  // 排序相关方法
  selectSortOption(e) {
    const { field, order } = e.currentTarget.dataset;
    this.setData({
      sortSettings: { field, order },
      showSort: false
    });
    this.loadTasks(true);
  },

  // 任务操作方法
  editTask() {
    const taskId = this.data.selectedTaskId;
    this.setData({ showActions: false });
    wx.navigateTo({
      url: `/pages/task-edit/index?id=${taskId}`
    });
  },

  async duplicateTask() {
    // 复制任务逻辑
    this.setData({ showActions: false });
    app.showSuccess('任务已复制');
  },

  shareTask() {
    // 分享任务逻辑
    this.setData({ showActions: false });
    app.showSuccess('分享功能开发中');
  },

  async deleteTask() {
    const taskId = this.data.selectedTaskId;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个任务吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await wx.cloud.callFunction({
              name: 'tasks',
              data: {
                action: 'delete',
                taskId: taskId
              }
            });
            
            if (result.result.code === 0) {
              // 从列表中移除任务
              const updatedTasks = this.data.taskList.filter(t => t._id !== taskId);
              this.setData({ 
                taskList: updatedTasks,
                showActions: false
              });
              
              app.showSuccess('任务已删除');
              this.updateFilterCounts();
            } else {
              app.showError(result.result.message);
            }
          } catch (error) {
            console.error('删除任务失败:', error);
            app.showError('删除失败，请重试');
          }
        }
        this.setData({ showActions: false });
      }
    });
  },

  // 工具方法
  getPriorityText(priority) {
    const priorityMap = {
      high: '紧急',
      medium: '重要',
      low: '普通'
    };
    return priorityMap[priority] || '普通';
  },

  formatDueTime(dueDate) {
    if (!dueDate) return '无截止时间';
    
    const now = new Date();
    const due = new Date(dueDate);
    const diffMs = due.getTime() - now.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffDays > 7) {
      return app.formatDate(due, 'MM-DD');
    } else if (diffDays > 0) {
      return `${diffDays}天后`;
    } else if (diffHours > 0) {
      return `${diffHours}小时后`;
    } else if (diffMs > 0) {
      return '即将到期';
    } else {
      return '已过期';
    }
  },

  getEmptyTitle() {
    const titleMap = {
      all: '还没有任务',
      pending: '没有待办任务',
      in_progress: '没有进行中的任务',
      completed: '还没有完成任务'
    };
    return titleMap[this.data.currentFilter] || '没有任务';
  },

  getEmptyDesc() {
    const descMap = {
      all: '创建你的第一个任务，开始管理你的焦虑',
      pending: '所有任务都已完成，真棒！',
      in_progress: '没有正在进行的任务',
      completed: '完成一些任务来看看你的成就'
    };
    return descMap[this.data.currentFilter] || '';
  }
});

<!--pages/task-list/index.wxml-->
<view class="task-list-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">我的任务</text>
      <text class="task-count">共 {{totalTasks}} 个任务</text>
    </view>
    <view class="header-actions">
      <button class="btn btn-primary btn-small" bindtap="createTask">
        <image src="/images/icons/add.png" class="btn-icon" />
        新建
      </button>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-tabs">
        <text 
          class="filter-tab {{currentFilter === item.value ? 'active' : ''}}"
          wx:for="{{filterOptions}}" 
          wx:key="value"
          bindtap="switchFilter"
          data-filter="{{item.value}}"
        >
          {{item.label}}
          <text class="tab-count" wx:if="{{item.count > 0}}">({{item.count}})</text>
        </text>
      </view>
    </scroll-view>
    
    <view class="filter-actions">
      <button class="filter-btn" bindtap="showFilterModal">
        <image src="/images/icons/filter.png" class="filter-icon" />
      </button>
      <button class="filter-btn" bindtap="showSortModal">
        <image src="/images/icons/sort.png" class="filter-icon" />
      </button>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="task-content">
    <!-- 加载状态 -->
    <view class="loading-section" wx:if="{{isLoading}}">
      <view class="loading-animation">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 任务列表 -->
    <view class="tasks-container" wx:if="{{!isLoading}}">
      <view 
        class="task-item {{item.priority}} {{item.status}}"
        wx:for="{{taskList}}" 
        wx:key="_id"
        bindtap="goToTaskDetail"
        data-id="{{item._id}}"
      >
        <!-- 任务复选框 -->
        <view class="task-checkbox" bindtap="toggleTaskStatus" data-id="{{item._id}}" catchtap="true">
          <image 
            src="/images/icons/{{item.status === 'completed' ? 'check-filled' : 'check-empty'}}.png" 
            class="checkbox-icon"
          />
        </view>
        
        <!-- 任务内容 -->
        <view class="task-content-area">
          <view class="task-header">
            <text class="task-title {{item.status === 'completed' ? 'completed' : ''}}">{{item.title}}</text>
            <view class="task-meta">
              <text class="priority-tag {{item.priority}}">{{getPriorityText(item.priority)}}</text>
              <text class="category-tag">{{item.category}}</text>
            </view>
          </view>
          
          <text class="task-description" wx:if="{{item.description}}">{{item.description}}</text>
          
          <view class="task-footer">
            <view class="task-time">
              <image src="/images/icons/clock.png" class="time-icon" />
              <text class="time-text">{{formatDueTime(item.dueDate)}}</text>
            </view>
            <view class="task-progress" wx:if="{{item.progress > 0}}">
              <text class="progress-text">{{item.progress}}%</text>
            </view>
          </view>
          
          <!-- 任务标签 -->
          <view class="task-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text 
              class="task-tag"
              wx:for="{{item.tags}}" 
              wx:key="*this"
              wx:for-item="tag"
            >
              {{tag}}
            </text>
          </view>
        </view>
        
        <!-- 任务操作 -->
        <view class="task-actions">
          <button 
            class="action-btn reminder-btn {{item.reminder && item.reminder.enabled ? 'active' : ''}}"
            bindtap="toggleReminder"
            data-id="{{item._id}}"
            catchtap="true"
          >
            <image src="/images/icons/bell.png" class="action-icon" />
          </button>
          <button 
            class="action-btn more-btn"
            bindtap="showTaskActions"
            data-id="{{item._id}}"
            catchtap="true"
          >
            <image src="/images/icons/more.png" class="action-icon" />
          </button>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{taskList.length === 0}}">
        <image src="/images/empty/tasks.png" class="empty-icon" />
        <text class="empty-title">{{getEmptyTitle()}}</text>
        <text class="empty-desc">{{getEmptyDesc()}}</text>
        <button class="btn btn-primary" bindtap="createTask" wx:if="{{currentFilter === 'all'}}">
          创建第一个任务
        </button>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && !isLoading}}">
      <button class="btn btn-secondary btn-small" bindtap="loadMore" loading="{{isLoadingMore}}">
        {{isLoadingMore ? '加载中...' : '加载更多'}}
      </button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal" wx:if="{{showFilter}}">
  <view class="modal-overlay" bindtap="hideFilterModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">筛选任务</text>
      <button class="modal-close" bindtap="hideFilterModal">×</button>
    </view>
    
    <view class="modal-body">
      <view class="filter-group">
        <text class="filter-group-title">优先级</text>
        <view class="filter-options">
          <text 
            class="filter-option {{filterSettings.priority === item.value ? 'active' : ''}}"
            wx:for="{{priorityOptions}}" 
            wx:key="value"
            bindtap="selectFilterOption"
            data-type="priority"
            data-value="{{item.value}}"
          >
            {{item.label}}
          </text>
        </view>
      </view>
      
      <view class="filter-group">
        <text class="filter-group-title">分类</text>
        <view class="filter-options">
          <text 
            class="filter-option {{filterSettings.category === item.value ? 'active' : ''}}"
            wx:for="{{categoryOptions}}" 
            wx:key="value"
            bindtap="selectFilterOption"
            data-type="category"
            data-value="{{item.value}}"
          >
            {{item.label}}
          </text>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn btn-secondary" bindtap="resetFilter">重置</button>
      <button class="btn btn-primary" bindtap="applyFilter">应用</button>
    </view>
  </view>
</view>

<!-- 排序弹窗 -->
<view class="sort-modal" wx:if="{{showSort}}">
  <view class="modal-overlay" bindtap="hideSortModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">排序方式</text>
      <button class="modal-close" bindtap="hideSortModal">×</button>
    </view>
    
    <view class="modal-body">
      <view 
        class="sort-option {{sortSettings.field === item.field && sortSettings.order === item.order ? 'active' : ''}}"
        wx:for="{{sortOptions}}" 
        wx:key="field"
        bindtap="selectSortOption"
        data-field="{{item.field}}"
        data-order="{{item.order}}"
      >
        <text class="sort-text">{{item.label}}</text>
        <image 
          src="/images/icons/check.png" 
          class="sort-check"
          wx:if="{{sortSettings.field === item.field && sortSettings.order === item.order}}"
        />
      </view>
    </view>
  </view>
</view>

<!-- 任务操作弹窗 -->
<view class="action-modal" wx:if="{{showActions}}">
  <view class="modal-overlay" bindtap="hideActionModal"></view>
  <view class="modal-content">
    <view class="action-list">
      <button class="action-item" bindtap="editTask">
        <image src="/images/icons/edit.png" class="action-icon" />
        <text class="action-text">编辑任务</text>
      </button>
      <button class="action-item" bindtap="duplicateTask">
        <image src="/images/icons/copy.png" class="action-icon" />
        <text class="action-text">复制任务</text>
      </button>
      <button class="action-item" bindtap="shareTask">
        <image src="/images/icons/share.png" class="action-icon" />
        <text class="action-text">分享任务</text>
      </button>
      <button class="action-item danger" bindtap="deleteTask">
        <image src="/images/icons/delete.png" class="action-icon" />
        <text class="action-text">删除任务</text>
      </button>
    </view>
  </view>
</view>

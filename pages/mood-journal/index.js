// pages/mood-journal/index.js
const app = getApp();

Page({
  data: {
    // 今日心情
    selectedMood: '',
    moodIntensity: 5,
    todayDate: '',
    todayJournal: null,
    
    // 心情选项
    moodOptions: [
      { value: 'happy', emoji: '😊', name: '开心' },
      { value: 'calm', emoji: '😌', name: '平静' },
      { value: 'anxious', emoji: '😰', name: '焦虑' },
      { value: 'sad', emoji: '😢', name: '难过' },
      { value: 'angry', emoji: '😡', name: '愤怒' }
    ],
    
    // 统计数据
    moodStats: {},
    moodTrend: [],
    
    // 历史记录
    journalList: [],
    isLoading: true,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 10,
    
    // 日期筛选
    showDatePicker: false,
    dateRange: {
      start: '',
      end: ''
    },
    quickDateOptions: [
      { value: 7, label: '最近7天' },
      { value: 30, label: '最近30天' },
      { value: 90, label: '最近3个月' },
      { value: 365, label: '最近一年' }
    ]
  },

  onLoad() {
    console.log('情绪日记页面加载');
    this.initPage();
  },

  onShow() {
    console.log('情绪日记页面显示');
    this.refreshData();
  },

  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    console.log('触底加载更多');
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadMore();
    }
  },

  // 初始化页面
  async initPage() {
    // 设置今日日期
    const today = new Date();
    const todayStr = app.formatDate(today, 'YYYY-MM-DD');
    const todayDisplay = app.formatDate(today, 'MM月DD日');
    
    this.setData({
      todayDate: todayDisplay
    });
    
    // 加载数据
    await this.loadInitialData(todayStr);
  },

  // 加载初始数据
  async loadInitialData(todayStr) {
    try {
      await Promise.all([
        this.loadTodayMood(todayStr),
        this.loadMoodStats(),
        this.loadMoodTrend(),
        this.loadJournalList(true)
      ]);
    } catch (error) {
      console.error('加载初始数据失败:', error);
    }
  },

  // 刷新数据
  async refreshData() {
    const today = app.formatDate(new Date(), 'YYYY-MM-DD');
    await this.loadInitialData(today);
  },

  // 加载今日心情
  async loadTodayMood(date) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'mood',
        data: {
          action: 'getTodayMood',
          date: date
        }
      });

      if (res.result.code === 0 && res.result.data) {
        const journal = res.result.data;
        this.setData({
          todayJournal: journal,
          selectedMood: journal.mood.primary,
          moodIntensity: journal.mood.intensity || 5
        });
      } else {
        this.setData({
          todayJournal: null,
          selectedMood: '',
          moodIntensity: 5
        });
      }
    } catch (error) {
      console.error('加载今日心情失败:', error);
    }
  },

  // 加载心情统计
  async loadMoodStats() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'mood',
        data: {
          action: 'getMoodStats',
          days: 7
        }
      });

      if (res.result.code === 0) {
        this.setData({
          moodStats: res.result.data
        });
      }
    } catch (error) {
      console.error('加载心情统计失败:', error);
    }
  },

  // 加载心情趋势
  async loadMoodTrend() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'mood',
        data: {
          action: 'getMoodTrend',
          days: 7
        }
      });

      if (res.result.code === 0) {
        this.setData({
          moodTrend: res.result.data.trend
        });
      }
    } catch (error) {
      console.error('加载心情趋势失败:', error);
    }
  },

  // 加载日记列表
  async loadJournalList(reset = true) {
    try {
      if (reset) {
        this.setData({ 
          isLoading: true,
          currentPage: 1
        });
      } else {
        this.setData({ isLoadingMore: true });
      }

      const { dateRange, currentPage, pageSize } = this.data;
      
      const res = await wx.cloud.callFunction({
        name: 'mood',
        data: {
          action: 'getJournalList',
          startDate: dateRange.start || undefined,
          endDate: dateRange.end || undefined,
          page: currentPage,
          limit: pageSize
        }
      });

      if (res.result.code === 0) {
        const { journals, pagination } = res.result.data;
        
        let journalList;
        if (reset) {
          journalList = journals;
        } else {
          journalList = [...this.data.journalList, ...journals];
        }
        
        this.setData({
          journalList: journalList,
          hasMore: currentPage < pagination.totalPages,
          currentPage: currentPage
        });
      } else {
        app.showError(res.result.message);
      }
    } catch (error) {
      console.error('加载日记列表失败:', error);
      app.showError('加载失败，请重试');
    } finally {
      this.setData({ 
        isLoading: false,
        isLoadingMore: false
      });
    }
  },

  // 加载更多
  async loadMore() {
    const nextPage = this.data.currentPage + 1;
    this.setData({ currentPage: nextPage });
    await this.loadJournalList(false);
  },

  // 选择心情
  selectMood(e) {
    const mood = e.currentTarget.dataset.mood;
    this.setData({ selectedMood: mood });
  },

  // 心情强度变化
  onIntensityChange(e) {
    this.setData({
      moodIntensity: e.detail.value
    });
  },

  // 快速保存心情
  async quickSaveMood() {
    const { selectedMood, moodIntensity } = this.data;
    
    if (!selectedMood) {
      app.showError('请先选择心情');
      return;
    }

    try {
      app.showLoading('保存中...');
      
      const today = app.formatDate(new Date(), 'YYYY-MM-DD');
      
      const res = await wx.cloud.callFunction({
        name: 'mood',
        data: {
          action: 'quickRecord',
          mood: selectedMood,
          date: today
        }
      });

      if (res.result.code === 0) {
        app.hideLoading();
        app.showSuccess('心情记录成功');
        
        // 重新加载今日心情
        await this.loadTodayMood(today);
        
        // 刷新统计数据
        await this.loadMoodStats();
        await this.loadMoodTrend();
      } else {
        app.hideLoading();
        app.showError(res.result.message);
      }
    } catch (error) {
      console.error('快速保存心情失败:', error);
      app.hideLoading();
      app.showError('保存失败，请重试');
    }
  },

  // 编辑今日日记
  editTodayJournal() {
    if (this.data.todayJournal) {
      wx.navigateTo({
        url: `/pages/mood-edit/index?id=${this.data.todayJournal._id}`
      });
    }
  },

  // 创建日记
  createJournal() {
    wx.navigateTo({
      url: '/pages/mood-create/index'
    });
  },

  // 查看日记详情
  viewJournal(e) {
    const journalId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/mood-detail/index?id=${journalId}`
    });
  },

  // 编辑日记
  editJournal(e) {
    const journalId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/mood-edit/index?id=${journalId}`
    });
  },

  // 显示日期选择器
  showDatePicker() {
    this.setData({ showDatePicker: true });
  },

  hideDatePicker() {
    this.setData({ showDatePicker: false });
  },

  // 日期选择
  onStartDateChange(e) {
    this.setData({
      'dateRange.start': e.detail.value
    });
  },

  onEndDateChange(e) {
    this.setData({
      'dateRange.end': e.detail.value
    });
  },

  // 快速日期选择
  selectQuickDate(e) {
    const days = e.currentTarget.dataset.value;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days + 1);
    
    this.setData({
      dateRange: {
        start: app.formatDate(startDate, 'YYYY-MM-DD'),
        end: app.formatDate(endDate, 'YYYY-MM-DD')
      }
    });
  },

  // 重置日期范围
  resetDateRange() {
    this.setData({
      dateRange: {
        start: '',
        end: ''
      }
    });
  },

  // 应用日期筛选
  applyDateRange() {
    this.setData({ showDatePicker: false });
    this.loadJournalList(true);
  },

  // 工具方法
  getMoodEmoji(mood) {
    const option = this.data.moodOptions.find(opt => opt.value === mood);
    return option ? option.emoji : '😐';
  },

  getStatsSummary() {
    const { moodStats } = this.data;
    if (!moodStats.totalRecords) {
      return '还没有记录';
    }
    
    const mostCommon = moodStats.mostCommonMood;
    const moodName = this.data.moodOptions.find(opt => opt.value === mostCommon)?.name || mostCommon;
    return `主要情绪: ${moodName}`;
  },

  getAverageIntensity() {
    const { moodStats } = this.data;
    if (!moodStats.moodAverages || Object.keys(moodStats.moodAverages).length === 0) {
      return '-';
    }
    
    const averages = Object.values(moodStats.moodAverages);
    const total = averages.reduce((sum, avg) => sum + avg, 0);
    const average = total / averages.length;
    return Math.round(average * 10) / 10;
  },

  formatTrendDate(dateStr) {
    const date = new Date(dateStr);
    const today = new Date();
    const diffDays = Math.floor((today - date) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今';
    } else if (diffDays === 1) {
      return '昨';
    } else {
      return app.formatDate(date, 'DD');
    }
  },

  formatJournalDate(dateStr, type) {
    const date = new Date(dateStr);
    
    if (type === 'day') {
      return app.formatDate(date, 'DD');
    } else if (type === 'month') {
      return app.formatDate(date, 'MM月');
    }
    
    return dateStr;
  },

  getActivitiesText(activities) {
    if (!activities || activities.length === 0) {
      return '';
    }
    
    return activities.slice(0, 3).map(activity => activity.name).join(', ') + 
           (activities.length > 3 ? '...' : '');
  }
});

/* pages/analysis-result/index.wxss */

.analysis-result-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: var(--page-padding);
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.loading-animation {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.loading-circle {
  width: 20rpx;
  height: 20rpx;
  background: var(--primary-color);
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-circle:nth-child(1) { animation-delay: -0.32s; }
.loading-circle:nth-child(2) { animation-delay: -0.16s; }

.loading-text {
  display: block;
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.loading-tip {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

/* 情绪分析结果 */
.emotion-analysis {
  margin-bottom: var(--component-margin);
}

.confidence-score {
  font-size: var(--font-size-caption);
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
}

.emotion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.emotion-tag {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-xl);
  font-size: var(--font-size-body-sm);
  font-weight: var(--font-weight-medium);
}

.emotion-tag.anxious {
  background: rgba(253, 121, 168, 0.2);
  color: var(--mood-anxious);
}

.emotion-tag.stress {
  background: rgba(243, 156, 18, 0.2);
  color: var(--warning-color);
}

.emotion-tag.fear {
  background: rgba(231, 76, 60, 0.2);
  color: var(--error-color);
}

.analysis-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1rpx solid var(--bg-tertiary);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

.summary-value {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.summary-value.urgency-high {
  color: var(--error-color);
}

.summary-value.urgency-medium {
  color: var(--warning-color);
}

.summary-value.urgency-low {
  color: var(--success-color);
}

.keywords-section {
  border-top: 1rpx solid var(--bg-tertiary);
  padding-top: var(--spacing-lg);
}

.keywords-title {
  display: block;
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.keyword-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
}

/* 任务建议区域 */
.tasks-section {
  margin-bottom: var(--component-margin);
}

.section-header {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: block;
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.section-desc {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.task-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border-left: 4rpx solid var(--text-tertiary);
  transition: all 0.3s ease;
}

.task-card.high {
  border-left-color: var(--error-color);
}

.task-card.medium {
  border-left-color: var(--warning-color);
}

.task-card.low {
  border-left-color: var(--success-color);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.task-priority {
  display: flex;
  align-items: center;
}

.priority-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: var(--text-tertiary);
  margin-right: var(--spacing-xs);
}

.task-card.high .priority-dot {
  background: var(--error-color);
}

.task-card.medium .priority-dot {
  background: var(--warning-color);
}

.task-card.low .priority-dot {
  background: var(--success-color);
}

.priority-text {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.task-time {
  font-size: var(--font-size-caption);
  color: var(--text-tertiary);
}

.task-content {
  margin-bottom: var(--spacing-lg);
}

.task-title {
  display: block;
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.task-description {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.6;
}

.task-suggestion {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-lg);
}

.suggestion-label {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.suggestion-time {
  font-size: var(--font-size-caption);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.task-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.task-actions .btn {
  flex: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--component-margin);
}

.action-buttons .btn {
  flex: 1;
}

/* 反馈区域 */
.feedback-section {
  margin-bottom: var(--component-margin);
}

.rating-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.rating-stars {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.star {
  font-size: 48rpx;
  color: var(--bg-tertiary);
  transition: color 0.3s ease;
}

.star.active {
  color: #FFD700;
}

.rating-text {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

.feedback-input {
  width: 100%;
  min-height: 120rpx;
  padding: var(--spacing-md);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-body-sm);
  color: var(--text-primary);
  background: var(--bg-secondary);
  margin-bottom: var(--spacing-md);
}

/* 错误状态 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.error-title {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.error-message {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

/* 编辑弹窗 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  margin: var(--spacing-xl);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
}

.modal-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-body);
  color: var(--text-primary);
  background: var(--bg-primary);
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}

.priority-options {
  display: flex;
  gap: var(--spacing-sm);
}

.priority-option {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: center;
  border: 2rpx solid var(--bg-tertiary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.priority-option.active {
  border-color: var(--primary-color);
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
}

.modal-footer {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  border-top: 1rpx solid var(--bg-tertiary);
  display: flex;
  gap: var(--spacing-md);
}

.modal-footer .btn {
  flex: 1;
}

/* 动画 */
@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

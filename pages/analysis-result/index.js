// pages/analysis-result/index.js
const app = getApp();

Page({
  data: {
    recordId: '',
    isLoading: true,
    analysisResult: null,
    generatedTasks: [],
    acceptedTasks: [],
    isRegenerating: false,
    
    // 反馈相关
    rating: 0,
    feedbackText: '',
    
    // 编辑弹窗
    showEditModal: false,
    editingTask: {},
    editingIndex: -1,
    
    // 选项数据
    priorityOptions: [
      { value: 'high', text: '紧急' },
      { value: 'medium', text: '重要' },
      { value: 'low', text: '普通' }
    ],
    
    // 错误信息
    errorMessage: ''
  },

  onLoad(options) {
    console.log('分析结果页面加载');
    const { recordId } = options;
    
    if (!recordId) {
      this.setData({
        isLoading: false,
        errorMessage: '缺少记录ID'
      });
      return;
    }
    
    this.setData({ recordId });
    this.loadAnalysisResult();
  },

  onShow() {
    console.log('分析结果页面显示');
  },

  // 加载分析结果
  async loadAnalysisResult() {
    try {
      this.setData({ isLoading: true });
      
      // 轮询获取分析结果
      await this.pollAnalysisResult();
      
    } catch (error) {
      console.error('加载分析结果失败:', error);
      this.setData({
        isLoading: false,
        errorMessage: error.message || '加载失败，请重试'
      });
    }
  },

  // 轮询分析结果
  async pollAnalysisResult() {
    const maxAttempts = 30; // 最多轮询30次（30秒）
    let attempts = 0;
    
    const poll = async () => {
      attempts++;
      
      try {
        const res = await wx.cloud.callFunction({
          name: 'anxiety',
          data: {
            action: 'getAnalysis',
            recordId: this.data.recordId
          }
        });
        
        if (res.result.code === 0) {
          const data = res.result.data;
          
          if (data.status === 'completed') {
            // 分析完成
            this.setData({
              isLoading: false,
              analysisResult: data.aiAnalysis,
              generatedTasks: data.generatedTasks || []
            });
            return;
          } else if (data.status === 'failed') {
            // 分析失败
            throw new Error('AI分析失败，请重试');
          } else if (data.status === 'processing') {
            // 继续轮询
            if (attempts < maxAttempts) {
              setTimeout(poll, 1000); // 1秒后重试
            } else {
              throw new Error('分析超时，请重试');
            }
          }
        } else {
          throw new Error(res.result.message);
        }
      } catch (error) {
        if (attempts < maxAttempts) {
          setTimeout(poll, 2000); // 2秒后重试
        } else {
          throw error;
        }
      }
    };
    
    await poll();
  },

  // 重试分析
  retryAnalysis() {
    this.loadAnalysisResult();
  },

  // 重新生成任务
  async regenerateTasks() {
    try {
      this.setData({ isRegenerating: true });
      
      // 这里可以调用重新生成任务的接口
      // 目前使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 模拟重新生成的任务
      const newTasks = this.generateMockTasks();
      
      this.setData({
        generatedTasks: newTasks,
        acceptedTasks: [],
        isRegenerating: false
      });
      
      app.showSuccess('任务已重新生成');
      
    } catch (error) {
      console.error('重新生成任务失败:', error);
      this.setData({ isRegenerating: false });
      app.showError('重新生成失败，请重试');
    }
  },

  // 编辑任务
  editTask(e) {
    const index = e.currentTarget.dataset.index;
    const task = this.data.generatedTasks[index];
    
    this.setData({
      showEditModal: true,
      editingTask: { ...task },
      editingIndex: index
    });
  },

  // 接受单个任务
  acceptTask(e) {
    const index = e.currentTarget.dataset.index;
    const acceptedTasks = [...this.data.acceptedTasks];
    
    if (!acceptedTasks.includes(index)) {
      acceptedTasks.push(index);
      this.setData({ acceptedTasks });
      app.showSuccess('任务已接受');
    }
  },

  // 接受所有任务
  async acceptAllTasks() {
    try {
      app.showLoading('创建任务中...');
      
      // 调用云函数创建任务
      const res = await wx.cloud.callFunction({
        name: 'tasks',
        data: {
          action: 'createFromAnalysis',
          recordId: this.data.recordId,
          tasks: this.data.generatedTasks
        }
      });
      
      if (res.result.code === 0) {
        app.hideLoading();
        app.showSuccess('所有任务已创建');
        
        // 跳转到任务列表
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/task-list/index'
          });
        }, 1500);
      } else {
        throw new Error(res.result.message);
      }
      
    } catch (error) {
      console.error('创建任务失败:', error);
      app.hideLoading();
      app.showError('创建任务失败，请重试');
    }
  },

  // 编辑弹窗相关
  hideEditModal() {
    this.setData({ showEditModal: false });
  },

  onEditTitle(e) {
    this.setData({
      'editingTask.title': e.detail.value
    });
  },

  onEditDescription(e) {
    this.setData({
      'editingTask.description': e.detail.value
    });
  },

  onEditTime(e) {
    this.setData({
      'editingTask.estimatedTime': parseInt(e.detail.value) || 0
    });
  },

  selectPriority(e) {
    const priority = e.currentTarget.dataset.priority;
    this.setData({
      'editingTask.priority': priority
    });
  },

  saveEditedTask() {
    const { editingTask, editingIndex, generatedTasks } = this.data;
    
    // 验证输入
    if (!editingTask.title || !editingTask.description) {
      app.showError('请填写完整的任务信息');
      return;
    }
    
    // 更新任务
    const updatedTasks = [...generatedTasks];
    updatedTasks[editingIndex] = { ...editingTask };
    
    this.setData({
      generatedTasks: updatedTasks,
      showEditModal: false
    });
    
    app.showSuccess('任务已更新');
  },

  // 反馈相关
  setRating(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({ rating });
  },

  onFeedbackInput(e) {
    this.setData({
      feedbackText: e.detail.value
    });
  },

  async submitFeedback() {
    const { rating, feedbackText, recordId } = this.data;
    
    try {
      await wx.cloud.callFunction({
        name: 'anxiety',
        data: {
          action: 'submitFeedback',
          recordId: recordId,
          rating: rating,
          comment: feedbackText
        }
      });
      
      app.showSuccess('反馈已提交，谢谢！');
      
      // 清空反馈
      this.setData({
        rating: 0,
        feedbackText: ''
      });
      
    } catch (error) {
      console.error('提交反馈失败:', error);
      app.showError('提交失败，请重试');
    }
  },

  // 工具方法
  getEmotionText(emotion) {
    const emotionMap = {
      '焦虑': '😰 焦虑',
      '压力': '😤 压力',
      '恐惧': '😨 恐惧',
      '担心': '😟 担心',
      '紧张': '😬 紧张'
    };
    return emotionMap[emotion] || emotion;
  },

  getUrgencyText(urgency) {
    const urgencyMap = {
      high: '高',
      medium: '中',
      low: '低'
    };
    return urgencyMap[urgency] || urgency;
  },

  getComplexityText(complexity) {
    const complexityMap = {
      high: '复杂',
      medium: '中等',
      low: '简单'
    };
    return complexityMap[complexity] || complexity;
  },

  getPriorityText(priority) {
    const priorityMap = {
      high: '紧急',
      medium: '重要',
      low: '普通'
    };
    return priorityMap[priority] || priority;
  },

  formatSuggestedTime(timeStr) {
    if (!timeStr) return '';
    
    const time = new Date(timeStr);
    const now = new Date();
    const diffMs = time.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays}天后 ${app.formatDate(time, 'HH:mm')}`;
    } else if (diffHours > 0) {
      return `${diffHours}小时后`;
    } else if (diffMs > 0) {
      return app.formatDate(time, 'HH:mm');
    } else {
      return '现在';
    }
  },

  getRatingText(rating) {
    const ratingTexts = [
      '',
      '很不满意',
      '不满意',
      '一般',
      '满意',
      '非常满意'
    ];
    return ratingTexts[rating] || '';
  },

  // 生成模拟任务（用于重新生成功能）
  generateMockTasks() {
    const taskTemplates = [
      {
        title: '制定详细计划',
        description: '将大目标分解为具体的小步骤',
        priority: 'high',
        estimatedTime: 25
      },
      {
        title: '寻求专业建议',
        description: '咨询相关领域的专家或有经验的人',
        priority: 'medium',
        estimatedTime: 30
      },
      {
        title: '放松身心',
        description: '进行深呼吸或冥想练习',
        priority: 'low',
        estimatedTime: 10
      },
      {
        title: '整理思路',
        description: '写下所有的想法和担忧',
        priority: 'medium',
        estimatedTime: 20
      }
    ];
    
    // 随机选择2-3个任务
    const count = 2 + Math.floor(Math.random() * 2);
    const selectedTasks = [];
    const usedIndexes = [];
    
    for (let i = 0; i < count; i++) {
      let index;
      do {
        index = Math.floor(Math.random() * taskTemplates.length);
      } while (usedIndexes.includes(index));
      
      usedIndexes.push(index);
      
      const task = { ...taskTemplates[index] };
      const now = new Date();
      task.suggestedTime = new Date(now.getTime() + (i + 1) * 2 * 60 * 60 * 1000).toISOString();
      
      selectedTasks.push(task);
    }
    
    return selectedTasks;
  }
});

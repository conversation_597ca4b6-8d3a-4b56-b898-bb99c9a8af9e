/* pages/index/index.wxss */

.index-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 头部欢迎区域 */
.header-section {
  padding: var(--spacing-lg) var(--page-padding) var(--spacing-xl);
}

.welcome-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: var(--spacing-lg);
  border: 4rpx solid var(--primary-light);
}

.greeting {
  flex: 1;
}

.greeting-text {
  display: block;
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.user-name {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.mood-quick-check {
  border-top: 1rpx solid var(--bg-tertiary);
  padding-top: var(--spacing-lg);
}

.mood-label {
  display: block;
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.mood-options {
  display: flex;
  justify-content: space-between;
}

.mood-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  min-width: 100rpx;
}

.mood-item.active {
  background: rgba(74, 144, 226, 0.1);
  transform: scale(1.05);
}

.mood-emoji {
  font-size: 48rpx;
  margin-bottom: var(--spacing-xs);
}

.mood-name {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

/* 快速操作区域 */
.quick-actions {
  padding: 0 var(--page-padding) var(--spacing-xl);
}

.section-title {
  margin-bottom: var(--spacing-lg);
}

.title-text {
  display: block;
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.title-desc {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

.action-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.action-item {
  display: flex;
  align-items: center;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: var(--primary-color);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-item.primary::before {
  opacity: 1;
  background: var(--primary-color);
}

.action-item:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-lg);
}

.action-icon .icon {
  width: 48rpx;
  height: 48rpx;
}

.action-content {
  flex: 1;
}

.action-title {
  display: block;
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.action-desc {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
}

.action-arrow {
  width: 32rpx;
  height: 32rpx;
}

.arrow-icon {
  width: 100%;
  height: 100%;
  opacity: 0.5;
}

/* 数据统计区域 */
.stats-section {
  padding: 0 var(--page-padding) var(--spacing-xl);
}

.stats-grid {
  display: flex;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-md);
  border-right: 1rpx solid var(--bg-tertiary);
}

.stat-item:last-child {
  border-right: none;
}

.stat-number {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

/* 最近任务区域 */
.recent-tasks {
  padding: 0 var(--page-padding) var(--spacing-xl);
}

.more-link {
  font-size: var(--font-size-body-sm);
  color: var(--primary-color);
  float: right;
  margin-top: var(--spacing-xs);
}

.task-list {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.task-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1rpx solid var(--bg-tertiary);
  transition: background-color 0.3s ease;
  position: relative;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item:active {
  background: var(--bg-secondary);
}

.task-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: var(--text-tertiary);
}

.task-item.high::before {
  background: var(--error-color);
}

.task-item.medium::before {
  background: var(--warning-color);
}

.task-item.low::before {
  background: var(--success-color);
}

.task-checkbox {
  width: 48rpx;
  height: 48rpx;
  margin-right: var(--spacing-md);
}

.checkbox-icon {
  width: 100%;
  height: 100%;
}

.task-content {
  flex: 1;
}

.task-title {
  display: block;
  font-size: var(--font-size-body);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.task-time {
  font-size: var(--font-size-caption);
  color: var(--text-secondary);
}

.task-priority {
  margin-left: var(--spacing-md);
}

.priority-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-caption);
  font-weight: var(--font-weight-medium);
}

.priority-tag.high {
  background: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
}

.priority-tag.medium {
  background: rgba(243, 156, 18, 0.1);
  color: var(--warning-color);
}

.priority-tag.low {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

/* 励志语句区域 */
.inspiration-section {
  padding: 0 var(--page-padding) var(--spacing-xl);
}

.inspiration-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-md);
}

.inspiration-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: var(--spacing-lg);
}

.heart-icon {
  width: 100%;
  height: 100%;
  filter: brightness(0) invert(1);
}

.inspiration-text {
  flex: 1;
  font-size: var(--font-size-body);
  color: #FFFFFF;
  line-height: 1.8;
  font-weight: var(--font-weight-medium);
}

/* 授权弹窗 */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  margin: var(--spacing-xl);
  max-width: 600rpx;
  width: 100%;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

.modal-header {
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  text-align: center;
}

.modal-title {
  display: block;
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.modal-desc {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.6;
}

.modal-body {
  padding: 0 var(--spacing-xl) var(--spacing-lg);
  text-align: center;
}

.modal-logo {
  width: 120rpx;
  height: 120rpx;
}

.modal-footer {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  display: flex;
  gap: var(--spacing-md);
}

.modal-footer .btn {
  flex: 1;
}

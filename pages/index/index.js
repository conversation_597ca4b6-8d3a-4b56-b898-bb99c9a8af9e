// pages/index/index.js
const app = getApp();

Page({
  data: {
    userInfo: {},
    greetingText: '',
    selectedMood: '',
    showAuthModal: false,
    moodOptions: [
      { value: 'happy', emoji: '😊', name: '开心' },
      { value: 'calm', emoji: '😌', name: '平静' },
      { value: 'anxious', emoji: '😰', name: '焦虑' },
      { value: 'sad', emoji: '😢', name: '难过' }
    ],
    stats: {
      totalAnxietyRecords: 0,
      totalTasksCompleted: 0,
      streakDays: 0,
      totalMoodRecords: 0
    },
    recentTasks: [],
    inspirationText: '每一次面对焦虑的勇气，都是成长的开始。'
  },

  onLoad() {
    console.log('首页加载');
    this.initPage();
  },

  onShow() {
    console.log('首页显示');
    this.refreshData();
  },

  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 初始化页面
  async initPage() {
    try {
      // 设置问候语
      this.setGreeting();
      
      // 检查登录状态
      if (!app.globalData.isLoggedIn) {
        await this.checkAndLogin();
      }
      
      // 加载用户数据
      await this.loadUserData();
      
      // 设置励志语句
      this.setInspiration();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  // 刷新数据
  async refreshData() {
    try {
      if (app.globalData.isLoggedIn) {
        await Promise.all([
          this.loadUserStats(),
          this.loadRecentTasks(),
          this.loadTodayMood()
        ]);
      }
    } catch (error) {
      console.error('刷新数据失败:', error);
    }
  },

  // 检查并登录
  async checkAndLogin() {
    try {
      if (!app.globalData.isLoggedIn) {
        // 静默登录
        await app.login();
        console.log('静默登录成功');
      }
    } catch (error) {
      console.error('登录失败:', error);
      // 显示授权弹窗
      this.setData({ showAuthModal: true });
    }
  },

  // 加载用户数据
  async loadUserData() {
    try {
      const userInfo = app.globalData.userInfo || {};
      this.setData({ userInfo });
      
      if (app.globalData.isLoggedIn) {
        await this.loadUserStats();
        await this.loadRecentTasks();
        await this.loadTodayMood();
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'user',
        data: { action: 'getStats' }
      });

      if (res.result.code === 0) {
        this.setData({
          stats: res.result.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  // 加载最近任务
  async loadRecentTasks() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'tasks',
        data: {
          action: 'getList',
          status: 'pending',
          limit: 3
        }
      });

      if (res.result.code === 0) {
        const tasks = res.result.data.tasks.map(task => ({
          ...task,
          dueTime: this.formatDueTime(task.dueDate),
          priorityText: this.getPriorityText(task.priority)
        }));
        
        this.setData({ recentTasks: tasks });
      }
    } catch (error) {
      console.error('加载最近任务失败:', error);
    }
  },

  // 加载今日心情
  async loadTodayMood() {
    try {
      const today = app.formatDate(new Date(), 'YYYY-MM-DD');
      const res = await wx.cloud.callFunction({
        name: 'mood',
        data: {
          action: 'getTodayMood',
          date: today
        }
      });

      if (res.result.code === 0 && res.result.data) {
        this.setData({
          selectedMood: res.result.data.mood.primary
        });
      }
    } catch (error) {
      console.error('加载今日心情失败:', error);
    }
  },

  // 设置问候语
  setGreeting() {
    const hour = new Date().getHours();
    let greeting = '';
    
    if (hour < 6) {
      greeting = '夜深了，注意休息';
    } else if (hour < 12) {
      greeting = '早上好';
    } else if (hour < 18) {
      greeting = '下午好';
    } else {
      greeting = '晚上好';
    }
    
    this.setData({ greetingText: greeting });
  },

  // 设置励志语句
  setInspiration() {
    const inspirations = [
      '每一次面对焦虑的勇气，都是成长的开始。',
      '焦虑是成长路上的伙伴，不是敌人。',
      '一步一步来，你比想象中更强大。',
      '今天的困难，是明天的经验。',
      '相信自己，你已经走了很远的路。',
      '每个小小的进步，都值得庆祝。',
      '焦虑提醒我们在乎，行动帮我们前进。'
    ];
    
    const randomIndex = Math.floor(Math.random() * inspirations.length);
    this.setData({
      inspirationText: inspirations[randomIndex]
    });
  },

  // 选择心情
  async selectMood(e) {
    const mood = e.currentTarget.dataset.mood;
    this.setData({ selectedMood: mood });
    
    try {
      // 保存今日心情
      const today = app.formatDate(new Date(), 'YYYY-MM-DD');
      await wx.cloud.callFunction({
        name: 'mood',
        data: {
          action: 'quickRecord',
          mood: mood,
          date: today
        }
      });
      
      app.showSuccess('心情记录成功');
    } catch (error) {
      console.error('记录心情失败:', error);
      app.showError('记录失败，请重试');
    }
  },

  // 切换任务状态
  async toggleTask(e) {
    const taskId = e.currentTarget.dataset.id;
    const task = this.data.recentTasks.find(t => t._id === taskId);
    
    if (!task) return;
    
    try {
      const newStatus = task.status === 'completed' ? 'pending' : 'completed';
      
      await wx.cloud.callFunction({
        name: 'tasks',
        data: {
          action: 'updateStatus',
          taskId: taskId,
          status: newStatus
        }
      });
      
      // 更新本地数据
      const updatedTasks = this.data.recentTasks.map(t => {
        if (t._id === taskId) {
          return { ...t, status: newStatus };
        }
        return t;
      });
      
      this.setData({ recentTasks: updatedTasks });
      
      if (newStatus === 'completed') {
        app.showSuccess('任务完成！');
        // 刷新统计数据
        this.loadUserStats();
      }
      
    } catch (error) {
      console.error('更新任务状态失败:', error);
      app.showError('操作失败，请重试');
    }
  },

  // 页面跳转方法
  goToAnxietyDump() {
    wx.navigateTo({
      url: '/pages/anxiety-dump/index'
    });
  },

  goToTaskList() {
    wx.switchTab({
      url: '/pages/task-list/index'
    });
  },

  goToMoodJournal() {
    wx.switchTab({
      url: '/pages/mood-journal/index'
    });
  },

  goToTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/task-detail/index?id=${taskId}`
    });
  },

  // 授权相关方法
  async handleAuth() {
    try {
      app.showLoading('登录中...');
      
      // 获取用户信息
      await app.getUserProfile();
      
      // 更新页面数据
      this.setData({
        userInfo: app.globalData.userInfo,
        showAuthModal: false
      });
      
      // 加载用户数据
      await this.loadUserData();
      
      app.hideLoading();
      app.showSuccess('登录成功');
      
    } catch (error) {
      console.error('授权失败:', error);
      app.hideLoading();
      app.showError('授权失败，请重试');
    }
  },

  hideAuthModal() {
    this.setData({ showAuthModal: false });
  },

  // 工具方法
  formatDueTime(dueDate) {
    if (!dueDate) return '';
    
    const now = new Date();
    const due = new Date(dueDate);
    const diffMs = due.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays}天后`;
    } else if (diffHours > 0) {
      return `${diffHours}小时后`;
    } else if (diffMs > 0) {
      return '即将到期';
    } else {
      return '已过期';
    }
  },

  getPriorityText(priority) {
    const priorityMap = {
      high: '紧急',
      medium: '重要',
      low: '普通'
    };
    return priorityMap[priority] || '普通';
  }
});

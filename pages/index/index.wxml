<!--pages/index/index.wxml-->
<view class="index-page">
  <!-- 头部欢迎区域 -->
  <view class="header-section">
    <view class="welcome-card">
      <view class="avatar-section">
        <image 
          class="user-avatar" 
          src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
          mode="aspectFill"
        />
        <view class="greeting">
          <text class="greeting-text">{{greetingText}}</text>
          <text class="user-name">{{userInfo.nickName || '朋友'}}</text>
        </view>
      </view>
      
      <view class="mood-quick-check">
        <text class="mood-label">今天感觉怎么样？</text>
        <view class="mood-options">
          <view 
            class="mood-item {{selectedMood === item.value ? 'active' : ''}}"
            wx:for="{{moodOptions}}" 
            wx:key="value"
            bindtap="selectMood"
            data-mood="{{item.value}}"
          >
            <text class="mood-emoji">{{item.emoji}}</text>
            <text class="mood-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速操作区域 -->
  <view class="quick-actions">
    <view class="section-title">
      <text class="title-text">快速开始</text>
      <text class="title-desc">选择你需要的帮助</text>
    </view>
    
    <view class="action-grid">
      <view class="action-item primary" bindtap="goToAnxietyDump">
        <view class="action-icon">
          <image src="/images/icons/anxiety.png" class="icon" />
        </view>
        <view class="action-content">
          <text class="action-title">倾诉焦虑</text>
          <text class="action-desc">说出你的困扰，让AI帮你分析</text>
        </view>
        <view class="action-arrow">
          <image src="/images/icons/arrow-right.png" class="arrow-icon" />
        </view>
      </view>
      
      <view class="action-item" bindtap="goToTaskList">
        <view class="action-icon">
          <image src="/images/icons/task.png" class="icon" />
        </view>
        <view class="action-content">
          <text class="action-title">查看任务</text>
          <text class="action-desc">管理你的待办事项</text>
        </view>
        <view class="action-arrow">
          <image src="/images/icons/arrow-right.png" class="arrow-icon" />
        </view>
      </view>
      
      <view class="action-item" bindtap="goToMoodJournal">
        <view class="action-icon">
          <image src="/images/icons/journal.png" class="icon" />
        </view>
        <view class="action-content">
          <text class="action-title">心情日记</text>
          <text class="action-desc">记录每天的情绪变化</text>
        </view>
        <view class="action-arrow">
          <image src="/images/icons/arrow-right.png" class="arrow-icon" />
        </view>
      </view>
    </view>
  </view>

  <!-- 数据统计区域 -->
  <view class="stats-section">
    <view class="section-title">
      <text class="title-text">我的进展</text>
      <text class="title-desc">看看你的成长轨迹</text>
    </view>
    
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{stats.totalAnxietyRecords}}</text>
        <text class="stat-label">倾诉次数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalTasksCompleted}}</text>
        <text class="stat-label">完成任务</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.streakDays}}</text>
        <text class="stat-label">连续天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalMoodRecords}}</text>
        <text class="stat-label">心情记录</text>
      </view>
    </view>
  </view>

  <!-- 最近任务区域 -->
  <view class="recent-tasks" wx:if="{{recentTasks.length > 0}}">
    <view class="section-title">
      <text class="title-text">待办任务</text>
      <text class="more-link" bindtap="goToTaskList">查看全部</text>
    </view>
    
    <view class="task-list">
      <view 
        class="task-item {{item.priority}}"
        wx:for="{{recentTasks}}" 
        wx:key="_id"
        bindtap="goToTaskDetail"
        data-id="{{item._id}}"
      >
        <view class="task-checkbox" bindtap="toggleTask" data-id="{{item._id}}" catchtap="true">
          <image 
            src="/images/icons/{{item.status === 'completed' ? 'check-filled' : 'check-empty'}}.png" 
            class="checkbox-icon"
          />
        </view>
        <view class="task-content">
          <text class="task-title">{{item.title}}</text>
          <text class="task-time">{{item.dueTime}}</text>
        </view>
        <view class="task-priority">
          <text class="priority-tag {{item.priority}}">{{item.priorityText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 励志语句区域 -->
  <view class="inspiration-section">
    <view class="inspiration-card">
      <view class="inspiration-icon">
        <image src="/images/icons/heart.png" class="heart-icon" />
      </view>
      <text class="inspiration-text">{{inspirationText}}</text>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

<!-- 登录授权弹窗 -->
<view class="auth-modal" wx:if="{{showAuthModal}}">
  <view class="modal-overlay" bindtap="hideAuthModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">欢迎使用心安AI</text>
      <text class="modal-desc">为了给你提供更好的服务，需要获取你的基本信息</text>
    </view>
    <view class="modal-body">
      <image src="/images/logo.png" class="modal-logo" />
    </view>
    <view class="modal-footer">
      <button class="btn btn-secondary" bindtap="hideAuthModal">稍后再说</button>
      <button class="btn btn-primary" bindtap="handleAuth">立即授权</button>
    </view>
  </view>
</view>

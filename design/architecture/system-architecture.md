# 系统架构设计

## 整体架构概览

"心安AI"小程序采用微信云开发的Serverless架构，结合第三方AI服务，构建轻量级、高可用的心理支持应用。

```mermaid
graph TB
    subgraph "用户端"
        A[微信小程序前端]
    end
    
    subgraph "微信云开发平台"
        B[云函数]
        C[云数据库]
        D[云存储]
        E[订阅消息]
    end
    
    subgraph "第三方服务"
        F[AI服务API]
        G[语音转文字API]
    end
    
    A --> B
    B --> C
    B --> D
    B --> F
    B --> G
    A --> E
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
    style G fill:#f1f8e9
```

## 架构层次

### 1. 表现层 (Presentation Layer)
- **微信小程序前端**
  - 使用微信小程序原生框架开发
  - 响应式设计，适配不同屏幕尺寸
  - 组件化开发，提高代码复用性
  - 状态管理使用小程序原生方案

### 2. 业务逻辑层 (Business Logic Layer)
- **云函数服务**
  - 用户认证与授权
  - AI任务拆解逻辑
  - 任务管理业务逻辑
  - 情绪数据分析
  - 消息推送逻辑

### 3. 数据访问层 (Data Access Layer)
- **云数据库**
  - 用户信息存储
  - 任务数据管理
  - 情绪日记记录
  - 系统配置数据

### 4. 外部服务层 (External Services Layer)
- **AI服务集成**
  - 自然语言处理
  - 任务智能拆解
  - 情绪分析
- **微信生态服务**
  - 用户身份认证
  - 订阅消息推送
  - 分享功能

## 核心组件设计

### 前端架构

```
src/
├── pages/              # 页面组件
│   ├── index/         # 首页
│   ├── anxiety-dump/  # 焦虑倾诉页
│   ├── task-list/     # 任务清单页
│   ├── mood-journal/  # 情绪日记页
│   └── profile/       # 个人中心页
├── components/        # 公共组件
│   ├── task-item/     # 任务项组件
│   ├── mood-selector/ # 情绪选择器
│   ├── loading/       # 加载组件
│   └── achievement-card/ # 成就卡片
├── utils/             # 工具函数
│   ├── api.js         # API调用封装
│   ├── storage.js     # 本地存储管理
│   └── auth.js        # 认证相关
├── styles/            # 样式文件
│   ├── common.wxss    # 公共样式
│   └── theme.wxss     # 主题样式
└── config/            # 配置文件
    └── index.js       # 应用配置
```

### 后端架构 (云函数)

```
cloudfunctions/
├── auth/              # 用户认证
├── ai-breakdown/      # AI任务拆解
├── task-management/   # 任务管理
├── mood-tracking/     # 情绪追踪
├── notification/      # 消息推送
└── common/            # 公共模块
    ├── db.js          # 数据库操作
    ├── ai-client.js   # AI服务客户端
    └── utils.js       # 工具函数
```

## 数据流设计

### 核心业务流程

1. **焦虑倾诉流程**
```
用户输入 → 前端验证 → 云函数处理 → AI分析 → 结果返回 → 界面更新
```

2. **任务管理流程**
```
任务操作 → 前端更新 → 云函数同步 → 数据库更新 → 状态同步
```

3. **消息推送流程**
```
定时触发 → 云函数检查 → 筛选用户 → 发送消息 → 记录日志
```

## 安全设计

### 1. 数据安全
- 所有敏感数据加密存储
- 使用HTTPS协议传输
- 定期数据备份

### 2. 访问控制
- 基于微信用户身份认证
- 云函数权限控制
- 数据库访问权限管理

### 3. 隐私保护
- 用户数据仅本人可见
- 提供数据删除功能
- 遵循数据最小化原则

## 性能优化

### 1. 前端优化
- 代码分包加载
- 图片懒加载
- 缓存策略优化

### 2. 后端优化
- 云函数冷启动优化
- 数据库查询优化
- AI接口调用优化

### 3. 网络优化
- CDN加速
- 请求合并
- 数据压缩

## 可扩展性设计

### 1. 模块化架构
- 功能模块独立开发
- 接口标准化
- 组件可复用

### 2. 配置化管理
- 业务规则配置化
- 界面元素配置化
- AI模型参数配置化

### 3. 多端扩展
- 代码结构支持多端
- 业务逻辑复用
- 数据模型统一

## 监控与运维

### 1. 性能监控
- 小程序性能监控
- 云函数执行监控
- 数据库性能监控

### 2. 错误监控
- 前端错误收集
- 后端异常监控
- AI服务调用监控

### 3. 业务监控
- 用户行为分析
- 功能使用统计
- 转化率监控

# 数据模型设计

## 数据库选型

采用微信云开发的云数据库（基于MongoDB的NoSQL数据库），具有以下优势：
- 与微信小程序深度集成
- 自动扩容，无需运维
- 内置权限控制
- 支持实时数据同步

## 数据模型概览

```mermaid
erDiagram
    Users ||--o{ AnxietyRecords : creates
    Users ||--o{ Tasks : owns
    Users ||--o{ MoodJournals : writes
    Users ||--o{ Achievements : earns
    AnxietyRecords ||--o{ Tasks : generates
    Tasks ||--o{ TaskLogs : has
    
    Users {
        string _id PK
        string openid UK
        string nickname
        string avatarUrl
        datetime createdAt
        datetime updatedAt
        object preferences
        object statistics
    }
    
    AnxietyRecords {
        string _id PK
        string userId FK
        string content
        string voiceUrl
        object aiAnalysis
        array generatedTasks
        string status
        datetime createdAt
    }
    
    Tasks {
        string _id PK
        string userId FK
        string anxietyRecordId FK
        string title
        string description
        string priority
        datetime dueDate
        string status
        datetime completedAt
        datetime createdAt
        datetime updatedAt
    }
    
    MoodJournals {
        string _id PK
        string userId FK
        string mood
        string note
        number rating
        datetime recordDate
        datetime createdAt
    }
    
    Achievements {
        string _id PK
        string userId FK
        string type
        string title
        string description
        object metadata
        datetime earnedAt
    }
    
    TaskLogs {
        string _id PK
        string taskId FK
        string action
        object oldValue
        object newValue
        datetime createdAt
    }
```

## 详细数据结构

### 1. 用户表 (Users)

```javascript
{
  _id: "user_unique_id",
  openid: "wx_openid_from_wechat",
  unionid: "wx_unionid_optional",
  profile: {
    nickname: "用户昵称",
    avatarUrl: "头像URL",
    gender: 1, // 0-未知 1-男 2-女
    city: "城市",
    province: "省份",
    country: "国家"
  },
  preferences: {
    notificationEnabled: true,
    reminderTime: "09:00", // 默认提醒时间
    theme: "light", // light/dark
    language: "zh-CN"
  },
  statistics: {
    totalAnxietyRecords: 0,
    totalTasksCompleted: 0,
    totalMoodRecords: 0,
    streakDays: 0, // 连续使用天数
    lastActiveDate: "2025-07-06"
  },
  privacy: {
    dataRetentionDays: 365, // 数据保留天数
    allowAnalytics: true
  },
  createdAt: "2025-07-06T10:00:00.000Z",
  updatedAt: "2025-07-06T10:00:00.000Z"
}
```

### 2. 焦虑记录表 (AnxietyRecords)

```javascript
{
  _id: "anxiety_record_id",
  userId: "user_id",
  input: {
    type: "text", // text/voice
    content: "用户输入的焦虑内容",
    voiceUrl: "语音文件URL（如果是语音输入）",
    duration: 30 // 语音时长（秒）
  },
  aiAnalysis: {
    emotions: ["焦虑", "担心", "压力"], // 识别的情绪
    keywords: ["考试", "复习", "时间不够"], // 关键词
    urgency: "high", // low/medium/high
    complexity: "medium", // low/medium/high
    category: "学习", // 焦虑类别
    confidence: 0.85 // AI分析置信度
  },
  generatedTasks: [
    {
      title: "制定复习计划",
      description: "列出所有需要复习的科目和章节",
      priority: "high",
      estimatedTime: 30,
      suggestedTime: "今晚8点"
    }
  ],
  status: "processed", // pending/processing/processed/failed
  processingTime: 2.5, // AI处理耗时（秒）
  feedback: {
    helpful: true,
    rating: 5,
    comment: "AI分析很准确"
  },
  createdAt: "2025-07-06T10:00:00.000Z",
  updatedAt: "2025-07-06T10:00:00.000Z"
}
```

### 3. 任务表 (Tasks)

```javascript
{
  _id: "task_id",
  userId: "user_id",
  anxietyRecordId: "anxiety_record_id", // 来源焦虑记录
  title: "任务标题",
  description: "任务详细描述",
  priority: "high", // low/medium/high
  category: "学习", // 任务分类
  estimatedTime: 30, // 预估耗时（分钟）
  dueDate: "2025-07-07T20:00:00.000Z",
  status: "pending", // pending/in_progress/completed/cancelled
  progress: 0, // 进度百分比 0-100
  tags: ["紧急", "重要"],
  reminder: {
    enabled: true,
    time: "2025-07-07T19:30:00.000Z",
    sent: false
  },
  completion: {
    completedAt: "2025-07-07T20:30:00.000Z",
    actualTime: 45, // 实际耗时（分钟）
    satisfaction: 4, // 完成满意度 1-5
    note: "完成得比预期好"
  },
  createdAt: "2025-07-06T10:00:00.000Z",
  updatedAt: "2025-07-07T20:30:00.000Z"
}
```

### 4. 情绪日记表 (MoodJournals)

```javascript
{
  _id: "mood_journal_id",
  userId: "user_id",
  recordDate: "2025-07-06", // 记录日期（YYYY-MM-DD）
  mood: {
    primary: "焦虑", // 主要情绪
    secondary: ["担心", "紧张"], // 次要情绪
    intensity: 7, // 情绪强度 1-10
    triggers: ["考试压力", "时间不够"] // 触发因素
  },
  activities: [
    {
      name: "完成数学作业",
      impact: "positive", // positive/negative/neutral
      duration: 60
    }
  ],
  note: "今天虽然有点焦虑，但完成了计划的任务，感觉好一些了",
  weather: "晴天", // 可选：天气情况
  location: "家里", // 可选：地点
  tags: ["学习", "进步"],
  createdAt: "2025-07-06T22:00:00.000Z",
  updatedAt: "2025-07-06T22:00:00.000Z"
}
```

### 5. 成就表 (Achievements)

```javascript
{
  _id: "achievement_id",
  userId: "user_id",
  type: "task_completion", // 成就类型
  title: "任务达人",
  description: "连续完成10个任务",
  icon: "🏆",
  rarity: "common", // common/rare/epic/legendary
  metadata: {
    tasksCompleted: 10,
    anxietyRecordId: "related_record_id",
    category: "学习"
  },
  rewards: {
    points: 100,
    badge: "task_master_bronze"
  },
  shareCard: {
    imageUrl: "成就卡片图片URL",
    text: "我在心安AI中获得了'任务达人'成就！"
  },
  earnedAt: "2025-07-06T20:00:00.000Z"
}
```

### 6. 任务日志表 (TaskLogs)

```javascript
{
  _id: "task_log_id",
  taskId: "task_id",
  userId: "user_id",
  action: "status_change", // create/update/delete/status_change
  details: {
    field: "status",
    oldValue: "pending",
    newValue: "completed",
    reason: "用户手动完成"
  },
  metadata: {
    source: "user_action", // user_action/system/ai
    deviceInfo: "小程序版本信息"
  },
  createdAt: "2025-07-06T20:00:00.000Z"
}
```

## 数据索引设计

### 主要索引

```javascript
// Users集合
db.users.createIndex({ "openid": 1 }, { unique: true })
db.users.createIndex({ "createdAt": -1 })

// AnxietyRecords集合
db.anxietyRecords.createIndex({ "userId": 1, "createdAt": -1 })
db.anxietyRecords.createIndex({ "status": 1 })

// Tasks集合
db.tasks.createIndex({ "userId": 1, "status": 1 })
db.tasks.createIndex({ "userId": 1, "dueDate": 1 })
db.tasks.createIndex({ "anxietyRecordId": 1 })

// MoodJournals集合
db.moodJournals.createIndex({ "userId": 1, "recordDate": -1 })

// Achievements集合
db.achievements.createIndex({ "userId": 1, "earnedAt": -1 })

// TaskLogs集合
db.taskLogs.createIndex({ "taskId": 1, "createdAt": -1 })
db.taskLogs.createIndex({ "userId": 1, "createdAt": -1 })
```

## 数据生命周期管理

### 数据保留策略
- **用户数据**: 用户主动删除或账号注销后删除
- **焦虑记录**: 默认保留1年，用户可设置
- **任务数据**: 完成后保留6个月
- **情绪日记**: 默认永久保留，用户可删除
- **日志数据**: 保留3个月

### 数据清理规则
```javascript
// 定期清理过期数据的云函数
const cleanupExpiredData = async () => {
  const sixMonthsAgo = new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000);
  const threeMonthsAgo = new Date(Date.now() - 3 * 30 * 24 * 60 * 60 * 1000);
  
  // 清理已完成的过期任务
  await db.collection('tasks').where({
    status: 'completed',
    completedAt: db.command.lt(sixMonthsAgo)
  }).remove();
  
  // 清理过期日志
  await db.collection('taskLogs').where({
    createdAt: db.command.lt(threeMonthsAgo)
  }).remove();
};
```

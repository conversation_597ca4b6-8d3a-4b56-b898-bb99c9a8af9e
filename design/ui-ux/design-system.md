# UI/UX设计系统规范

## 设计理念

"心安AI"的设计理念围绕**温暖、安全、简洁、治愈**四个核心词展开，旨在为用户创造一个舒适、无压力的心理支持环境。

### 设计原则

1. **情感化设计**: 通过色彩、字体、动画传达温暖和关怀
2. **简约至上**: 减少认知负担，让用户专注于核心功能
3. **一致性**: 保持界面元素和交互的一致性
4. **可访问性**: 考虑不同用户群体的使用需求
5. **微信生态**: 遵循微信小程序设计规范

## 色彩系统

### 主色调 (Primary Colors)
```css
/* 主品牌色 - 宁静蓝 */
--primary-color: #4A90E2;
--primary-light: #7BB3F0;
--primary-dark: #2E5C8A;

/* 辅助色 - 温暖绿 */
--secondary-color: #7ED321;
--secondary-light: #A8E65C;
--secondary-dark: #5BA617;
```

### 中性色 (Neutral Colors)
```css
/* 文字颜色 */
--text-primary: #2C3E50;    /* 主要文字 */
--text-secondary: #7F8C8D;  /* 次要文字 */
--text-tertiary: #BDC3C7;   /* 辅助文字 */
--text-disabled: #ECF0F1;   /* 禁用文字 */

/* 背景颜色 */
--bg-primary: #FFFFFF;      /* 主背景 */
--bg-secondary: #F8F9FA;    /* 次背景 */
--bg-tertiary: #E9ECEF;     /* 三级背景 */
--bg-overlay: rgba(0,0,0,0.5); /* 遮罩 */
```

### 功能色 (Functional Colors)
```css
/* 状态颜色 */
--success-color: #27AE60;   /* 成功 */
--warning-color: #F39C12;   /* 警告 */
--error-color: #E74C3C;     /* 错误 */
--info-color: #3498DB;      /* 信息 */

/* 情绪色彩 */
--mood-happy: #FFD93D;      /* 开心 */
--mood-calm: #6C5CE7;       /* 平静 */
--mood-anxious: #FD79A8;    /* 焦虑 */
--mood-sad: #74B9FF;        /* 难过 */
```

## 字体系统

### 字体族
```css
/* 主字体 */
font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
```

### 字体大小
```css
/* 标题字体 */
--font-size-h1: 32rpx;  /* 主标题 */
--font-size-h2: 28rpx;  /* 二级标题 */
--font-size-h3: 24rpx;  /* 三级标题 */

/* 正文字体 */
--font-size-body: 28rpx;    /* 正文 */
--font-size-body-sm: 24rpx; /* 小正文 */
--font-size-caption: 20rpx; /* 说明文字 */

/* 特殊字体 */
--font-size-button: 28rpx;  /* 按钮文字 */
--font-size-input: 28rpx;   /* 输入框文字 */
```

### 字重
```css
--font-weight-light: 300;   /* 细体 */
--font-weight-normal: 400;  /* 常规 */
--font-weight-medium: 500;  /* 中等 */
--font-weight-bold: 600;    /* 粗体 */
```

## 间距系统

### 基础间距单位
```css
/* 基础单位 8rpx */
--spacing-xs: 8rpx;    /* 极小间距 */
--spacing-sm: 16rpx;   /* 小间距 */
--spacing-md: 24rpx;   /* 中等间距 */
--spacing-lg: 32rpx;   /* 大间距 */
--spacing-xl: 48rpx;   /* 极大间距 */
--spacing-xxl: 64rpx;  /* 超大间距 */
```

### 页面布局间距
```css
/* 页面边距 */
--page-padding: 32rpx;

/* 组件间距 */
--component-margin: 24rpx;

/* 内容间距 */
--content-padding: 16rpx;
```

## 组件设计规范

### 按钮 (Button)

#### 主要按钮
```css
.btn-primary {
  background: var(--primary-color);
  color: #FFFFFF;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: var(--font-size-button);
  font-weight: var(--font-weight-medium);
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.btn-primary:active {
  background: var(--primary-dark);
  transform: translateY(2rpx);
}
```

#### 次要按钮
```css
.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  border-radius: 12rpx;
  padding: 22rpx 46rpx;
}
```

### 输入框 (Input)

```css
.input-field {
  background: var(--bg-primary);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: 12rpx;
  padding: 24rpx 32rpx;
  font-size: var(--font-size-input);
  color: var(--text-primary);
  transition: border-color 0.3s ease;
}

.input-field:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}
```

### 卡片 (Card)

```css
.card {
  background: var(--bg-primary);
  border-radius: 16rpx;
  padding: var(--spacing-lg);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: var(--component-margin);
}

.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1rpx solid var(--bg-tertiary);
}
```

### 任务项 (Task Item)

```css
.task-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: 12rpx;
  margin-bottom: var(--spacing-sm);
  border-left: 6rpx solid var(--primary-color);
}

.task-item.completed {
  opacity: 0.6;
  border-left-color: var(--success-color);
}

.task-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 3rpx solid var(--primary-color);
  margin-right: var(--spacing-md);
}
```

## 动画与交互

### 过渡动画
```css
/* 基础过渡 */
.transition-base {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 弹性动画 */
.transition-bounce {
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
```

### 加载动画
```css
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}
```

### 成功反馈动画
```css
@keyframes checkmark {
  0% { transform: scale(0) rotate(45deg); }
  50% { transform: scale(1.2) rotate(45deg); }
  100% { transform: scale(1) rotate(45deg); }
}

.success-checkmark {
  animation: checkmark 0.6s ease-in-out;
}
```

## 页面布局规范

### 页面结构
```html
<view class="page">
  <view class="page-header">
    <!-- 页面头部 -->
  </view>
  <view class="page-content">
    <!-- 页面内容 -->
  </view>
  <view class="page-footer">
    <!-- 页面底部 -->
  </view>
</view>
```

### 响应式设计
```css
/* 小屏幕适配 */
@media (max-width: 375px) {
  .page {
    padding: var(--spacing-md);
  }
}

/* 大屏幕适配 */
@media (min-width: 414px) {
  .page {
    padding: var(--spacing-lg);
  }
}
```

## 图标系统

### 图标规范
- **尺寸**: 32rpx, 48rpx, 64rpx
- **风格**: 线性图标，2rpx线宽
- **颜色**: 使用主题色彩系统

### 常用图标
```css
.icon {
  width: 48rpx;
  height: 48rpx;
  color: var(--text-secondary);
}

.icon-primary {
  color: var(--primary-color);
}

.icon-success {
  color: var(--success-color);
}
```

## 情绪表达设计

### 情绪图标
- 😊 开心 - #FFD93D
- 😌 平静 - #6C5CE7  
- 😰 焦虑 - #FD79A8
- 😢 难过 - #74B9FF
- 😡 愤怒 - #E17055

### 情绪强度指示器
```css
.mood-intensity {
  display: flex;
  gap: var(--spacing-xs);
}

.mood-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: var(--bg-tertiary);
}

.mood-dot.active {
  background: var(--primary-color);
}
```

## 无障碍设计

### 对比度要求
- 正文文字对比度 ≥ 4.5:1
- 大文字对比度 ≥ 3:1
- 非文字元素对比度 ≥ 3:1

### 触摸目标
- 最小触摸区域: 88rpx × 88rpx
- 相邻触摸目标间距: ≥ 16rpx

### 语义化标记
```html
<!-- 使用语义化的aria标签 -->
<button aria-label="完成任务">
  <text>✓</text>
</button>

<view role="alert" aria-live="polite">
  任务已完成
</view>
```

## 品牌元素

### Logo使用规范
- 最小使用尺寸: 64rpx × 64rpx
- 安全区域: Logo周围至少保留1倍Logo高度的空白
- 颜色变体: 彩色版、单色版、反白版

### 吉祥物设计
- 设计一个温暖可爱的AI助手形象
- 用于引导用户、提供情感支持
- 在关键节点出现，增加亲和力

## 设计交付规范

### 设计文件命名
```
页面名称_状态_版本号.sketch
例: anxiety-dump_final_v1.0.sketch
```

### 切图规范
- 格式: PNG (透明背景) / JPG (不透明背景)
- 分辨率: @1x, @2x, @3x
- 命名: <EMAIL>

### 标注规范
- 使用rpx作为单位
- 标注间距、字号、颜色值
- 提供组件状态说明

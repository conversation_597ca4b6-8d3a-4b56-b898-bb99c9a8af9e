# 焦虑倾诉模块设计

## 模块概述

焦虑倾诉模块是"心安AI"的核心入口功能，为用户提供一个安全、私密的环境来表达和记录焦虑情绪。通过文字或语音输入，用户可以无压力地倾诉内心的焦虑和困扰。

## 功能需求

### 核心功能
1. **文字输入**: 支持多行文本输入，无字数限制
2. **语音输入**: 集成微信语音识别，实时转换为文字
3. **输入历史**: 保存用户的倾诉记录，支持查看历史
4. **隐私保护**: 确保用户数据安全，仅本人可见
5. **情感引导**: 通过界面设计和文案引导用户表达

### 辅助功能
1. **输入提示**: 提供倾诉引导语和示例
2. **字数统计**: 实时显示输入字数
3. **草稿保存**: 自动保存输入内容，防止丢失
4. **清空重置**: 支持清空当前输入内容

## 用户界面设计

### 页面布局
```
┌─────────────────────────────────┐
│           页面标题               │
│        "倾诉你的焦虑"            │
├─────────────────────────────────┤
│                                 │
│         引导文案区域             │
│    "在这里，你可以安全地..."      │
│                                 │
├─────────────────────────────────┤
│                                 │
│                                 │
│         文本输入区域             │
│      (多行文本输入框)            │
│                                 │
│                                 │
├─────────────────────────────────┤
│  [语音按钮]    [字数: 0/∞]      │
├─────────────────────────────────┤
│                                 │
│    [清空]        [提交分析]      │
│                                 │
└─────────────────────────────────┘
```

### 界面元素设计

#### 1. 页面头部
```css
.anxiety-dump-header {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-title {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.6;
}
```

#### 2. 引导文案区域
```css
.guide-section {
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  margin: var(--spacing-md);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
}

.guide-text {
  font-size: var(--font-size-body-sm);
  color: var(--text-secondary);
  line-height: 1.8;
  text-align: center;
}
```

#### 3. 输入区域
```css
.input-section {
  padding: var(--spacing-lg);
}

.text-input {
  width: 100%;
  min-height: 400rpx;
  padding: var(--spacing-lg);
  border: 2rpx solid var(--bg-tertiary);
  border-radius: 16rpx;
  font-size: var(--font-size-body);
  line-height: 1.6;
  background: var(--bg-primary);
  resize: none;
}

.text-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
}

.text-input::placeholder {
  color: var(--text-tertiary);
  font-style: italic;
}
```

#### 4. 工具栏
```css
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
}

.voice-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
}

.voice-button.recording {
  background: var(--error-color);
  animation: pulse 1s infinite;
}

.word-count {
  font-size: var(--font-size-caption);
  color: var(--text-tertiary);
}
```

#### 5. 操作按钮
```css
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.btn-clear {
  flex: 1;
  background: transparent;
  color: var(--text-secondary);
  border: 2rpx solid var(--bg-tertiary);
}

.btn-submit {
  flex: 2;
  background: var(--primary-color);
  color: #FFFFFF;
}
```

## 交互设计

### 输入交互流程

```mermaid
graph TD
    A[进入页面] --> B[显示引导文案]
    B --> C[用户选择输入方式]
    C --> D[文字输入]
    C --> E[语音输入]
    
    D --> F[实时字数统计]
    F --> G[自动保存草稿]
    
    E --> H[录音中状态]
    H --> I[语音转文字]
    I --> J[确认转换结果]
    J --> G
    
    G --> K[用户点击提交]
    K --> L[验证输入内容]
    L --> M[显示加载状态]
    M --> N[跳转到分析结果页]
```

### 语音输入交互

1. **开始录音**
   - 长按语音按钮开始录音
   - 按钮变红并显示录音动画
   - 显示录音时长

2. **录音中**
   - 实时显示录音波形
   - 支持取消录音（滑动取消）
   - 最长录音时间60秒

3. **结束录音**
   - 松开按钮结束录音
   - 显示"正在识别..."状态
   - 识别完成后显示文字结果

4. **确认结果**
   - 用户可编辑识别结果
   - 支持重新录音
   - 确认后添加到输入框

### 输入提示与引导

#### 占位符文本
```javascript
const placeholderTexts = [
  "在这里，你可以安全地表达内心的焦虑和困扰...",
  "告诉我什么让你感到焦虑，我会帮你分析和解决",
  "无论是学习、工作还是生活上的压力，都可以在这里倾诉",
  "描述一下你现在的感受，让我们一起面对这些挑战"
];
```

#### 引导提示
```javascript
const guidePrompts = [
  {
    trigger: "empty_input_30s",
    message: "不知道从何说起？试试描述一下你最近遇到的困难"
  },
  {
    trigger: "short_input",
    message: "可以更详细地描述一下具体的情况吗？"
  },
  {
    trigger: "long_pause",
    message: "慢慢来，没有时间限制，想到什么就说什么"
  }
];
```

## 技术实现

### 前端实现

#### 页面结构 (anxiety-dump.wxml)
```html
<view class="anxiety-dump-page">
  <!-- 页面头部 -->
  <view class="anxiety-dump-header">
    <text class="page-title">倾诉你的焦虑</text>
    <text class="page-subtitle">在这里，你可以安全地表达内心的困扰</text>
  </view>

  <!-- 引导文案 -->
  <view class="guide-section">
    <text class="guide-text">{{guideText}}</text>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <textarea 
      class="text-input"
      placeholder="{{placeholder}}"
      value="{{inputText}}"
      bindinput="onTextInput"
      bindblur="onInputBlur"
      auto-height
      maxlength="-1"
    />
  </view>

  <!-- 工具栏 -->
  <view class="toolbar">
    <button 
      class="voice-button {{isRecording ? 'recording' : ''}}"
      bindtouchstart="startRecording"
      bindtouchend="stopRecording"
      bindtouchcancel="cancelRecording"
    >
      <image src="/images/icons/microphone.png" class="voice-icon" />
    </button>
    
    <text class="word-count">字数: {{wordCount}}</text>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn-clear" bindtap="clearInput">清空</button>
    <button 
      class="btn-submit" 
      bindtap="submitAnxiety"
      disabled="{{!canSubmit}}"
      loading="{{isSubmitting}}"
    >
      {{isSubmitting ? '分析中...' : '开始分析'}}
    </button>
  </view>
</view>
```

#### 页面逻辑 (anxiety-dump.js)
```javascript
Page({
  data: {
    inputText: '',
    wordCount: 0,
    isRecording: false,
    isSubmitting: false,
    canSubmit: false,
    placeholder: '在这里，你可以安全地表达内心的焦虑和困扰...',
    guideText: '无论是学习压力、工作困扰还是生活烦恼，都可以在这里自由表达。我会帮你分析并提供具体的解决方案。'
  },

  onLoad() {
    this.loadDraft();
    this.setRandomPlaceholder();
  },

  // 文字输入处理
  onTextInput(e) {
    const text = e.detail.value;
    this.setData({
      inputText: text,
      wordCount: text.length,
      canSubmit: text.trim().length > 10
    });
    
    // 自动保存草稿
    this.saveDraft(text);
  },

  // 开始录音
  startRecording() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.record']) {
          wx.authorize({
            scope: 'scope.record',
            success: () => this.doStartRecording(),
            fail: () => this.showRecordPermissionTip()
          });
        } else {
          this.doStartRecording();
        }
      }
    });
  },

  doStartRecording() {
    this.setData({ isRecording: true });
    
    wx.startRecord({
      success: (res) => {
        this.convertVoiceToText(res.tempFilePath);
      },
      fail: (err) => {
        console.error('录音失败:', err);
        this.showToast('录音失败，请重试');
      }
    });
  },

  // 停止录音
  stopRecording() {
    if (this.data.isRecording) {
      wx.stopRecord();
      this.setData({ isRecording: false });
    }
  },

  // 语音转文字
  convertVoiceToText(filePath) {
    wx.showLoading({ title: '识别中...' });
    
    // 调用云函数进行语音识别
    wx.cloud.callFunction({
      name: 'speechToText',
      data: { filePath },
      success: (res) => {
        const text = res.result.text;
        this.appendText(text);
      },
      fail: (err) => {
        console.error('语音识别失败:', err);
        this.showToast('语音识别失败，请重试');
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 添加文字到输入框
  appendText(text) {
    const newText = this.data.inputText + (this.data.inputText ? '\n' : '') + text;
    this.setData({
      inputText: newText,
      wordCount: newText.length,
      canSubmit: newText.trim().length > 10
    });
    this.saveDraft(newText);
  },

  // 提交焦虑内容
  submitAnxiety() {
    if (!this.data.canSubmit) return;

    this.setData({ isSubmitting: true });

    wx.cloud.callFunction({
      name: 'anxiety',
      data: {
        action: 'submit',
        content: this.data.inputText.trim()
      },
      success: (res) => {
        if (res.result.code === 0) {
          // 清除草稿
          this.clearDraft();
          
          // 跳转到分析结果页
          wx.navigateTo({
            url: `/pages/analysis-result/index?recordId=${res.result.data.recordId}`
          });
        } else {
          this.showToast(res.result.message);
        }
      },
      fail: (err) => {
        console.error('提交失败:', err);
        this.showToast('提交失败，请重试');
      },
      complete: () => {
        this.setData({ isSubmitting: false });
      }
    });
  },

  // 清空输入
  clearInput() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空当前输入的内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            inputText: '',
            wordCount: 0,
            canSubmit: false
          });
          this.clearDraft();
        }
      }
    });
  },

  // 草稿管理
  saveDraft(text) {
    wx.setStorageSync('anxiety_draft', {
      text,
      timestamp: Date.now()
    });
  },

  loadDraft() {
    try {
      const draft = wx.getStorageSync('anxiety_draft');
      if (draft && draft.text) {
        // 如果草稿不超过24小时，则恢复
        if (Date.now() - draft.timestamp < 24 * 60 * 60 * 1000) {
          this.setData({
            inputText: draft.text,
            wordCount: draft.text.length,
            canSubmit: draft.text.trim().length > 10
          });
        }
      }
    } catch (err) {
      console.error('加载草稿失败:', err);
    }
  },

  clearDraft() {
    wx.removeStorageSync('anxiety_draft');
  },

  // 工具方法
  setRandomPlaceholder() {
    const placeholders = [
      '在这里，你可以安全地表达内心的焦虑和困扰...',
      '告诉我什么让你感到焦虑，我会帮你分析和解决',
      '无论是学习、工作还是生活上的压力，都可以在这里倾诉',
      '描述一下你现在的感受，让我们一起面对这些挑战'
    ];
    
    const randomIndex = Math.floor(Math.random() * placeholders.length);
    this.setData({
      placeholder: placeholders[randomIndex]
    });
  },

  showToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
});
```

## 数据流设计

### 输入数据处理
1. **文字输入**: 实时保存到本地草稿
2. **语音输入**: 上传到云存储 → 调用语音识别API → 返回文字结果
3. **数据验证**: 检查内容长度和格式
4. **提交处理**: 保存到数据库 → 触发AI分析

### 隐私保护措施
1. **本地加密**: 草稿数据本地加密存储
2. **传输加密**: HTTPS传输，数据加密
3. **访问控制**: 仅用户本人可访问
4. **数据清理**: 定期清理临时数据

## 性能优化

### 前端优化
1. **懒加载**: 非关键资源延迟加载
2. **防抖处理**: 输入事件防抖，减少频繁调用
3. **缓存策略**: 合理使用本地缓存
4. **代码分包**: 按需加载页面代码

### 后端优化
1. **异步处理**: AI分析异步执行
2. **缓存机制**: 常用数据缓存
3. **限流控制**: 防止恶意请求
4. **资源优化**: 语音文件压缩和清理

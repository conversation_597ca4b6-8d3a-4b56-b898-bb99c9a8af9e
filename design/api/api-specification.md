# API接口规范文档

## 接口设计原则

1. **RESTful风格**: 遵循REST API设计规范
2. **统一响应格式**: 所有接口返回统一的JSON格式
3. **错误处理**: 标准化的错误码和错误信息
4. **安全认证**: 基于微信小程序的用户身份认证
5. **版本控制**: 支持API版本管理

## 通用规范

### 请求格式
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **请求方法**: GET, POST, PUT, DELETE

### 响应格式
```javascript
{
  "code": 0,           // 状态码，0表示成功
  "message": "success", // 状态信息
  "data": {},          // 响应数据
  "timestamp": 1625587200000, // 时间戳
  "requestId": "req_123456"   // 请求ID
}
```

### 错误码定义
```javascript
{
  0: "成功",
  1001: "参数错误",
  1002: "用户未登录",
  1003: "权限不足",
  2001: "AI服务异常",
  2002: "语音识别失败",
  3001: "数据库操作失败",
  3002: "数据不存在",
  5001: "系统内部错误"
}
```

## 核心API接口

### 1. 用户认证模块

#### 1.1 用户登录
```
POST /api/auth/login
```

**请求参数:**
```javascript
{
  "code": "wx_login_code",  // 微信登录凭证
  "userInfo": {             // 用户信息（可选）
    "nickname": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 1,
    "city": "城市",
    "province": "省份"
  }
}
```

**响应数据:**
```javascript
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "userId": "user_123",
    "token": "jwt_token",
    "isNewUser": false,
    "profile": {
      "nickname": "用户昵称",
      "avatarUrl": "头像URL"
    }
  }
}
```

#### 1.2 获取用户信息
```
GET /api/auth/profile
```

**响应数据:**
```javascript
{
  "code": 0,
  "data": {
    "userId": "user_123",
    "profile": {
      "nickname": "用户昵称",
      "avatarUrl": "头像URL"
    },
    "preferences": {
      "notificationEnabled": true,
      "reminderTime": "09:00"
    },
    "statistics": {
      "totalAnxietyRecords": 15,
      "totalTasksCompleted": 42,
      "streakDays": 7
    }
  }
}
```

### 2. 焦虑倾诉模块

#### 2.1 提交焦虑内容
```
POST /api/anxiety/submit
```

**请求参数:**
```javascript
{
  "type": "text",           // text/voice
  "content": "我很焦虑...", // 文本内容
  "voiceUrl": "语音文件URL", // 语音文件（type为voice时）
  "duration": 30            // 语音时长（秒）
}
```

**响应数据:**
```javascript
{
  "code": 0,
  "message": "提交成功",
  "data": {
    "recordId": "record_123",
    "status": "processing"    // processing/completed
  }
}
```

#### 2.2 获取AI分析结果
```
GET /api/anxiety/analysis/{recordId}
```

**响应数据:**
```javascript
{
  "code": 0,
  "data": {
    "recordId": "record_123",
    "status": "completed",
    "aiAnalysis": {
      "emotions": ["焦虑", "担心"],
      "keywords": ["考试", "复习"],
      "urgency": "high",
      "category": "学习"
    },
    "generatedTasks": [
      {
        "title": "制定复习计划",
        "description": "列出需要复习的科目",
        "priority": "high",
        "estimatedTime": 30
      }
    ]
  }
}
```

### 3. 任务管理模块

#### 3.1 创建任务
```
POST /api/tasks
```

**请求参数:**
```javascript
{
  "title": "任务标题",
  "description": "任务描述",
  "priority": "high",
  "dueDate": "2025-07-07T20:00:00.000Z",
  "anxietyRecordId": "record_123"
}
```

#### 3.2 获取任务列表
```
GET /api/tasks?status=pending&page=1&limit=20
```

**查询参数:**
- `status`: 任务状态 (pending/completed/all)
- `page`: 页码
- `limit`: 每页数量
- `category`: 任务分类
- `priority`: 优先级

**响应数据:**
```javascript
{
  "code": 0,
  "data": {
    "tasks": [
      {
        "taskId": "task_123",
        "title": "任务标题",
        "description": "任务描述",
        "priority": "high",
        "status": "pending",
        "dueDate": "2025-07-07T20:00:00.000Z",
        "createdAt": "2025-07-06T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

#### 3.3 更新任务状态
```
PUT /api/tasks/{taskId}/status
```

**请求参数:**
```javascript
{
  "status": "completed",
  "note": "完成备注",
  "actualTime": 45
}
```

#### 3.4 删除任务
```
DELETE /api/tasks/{taskId}
```

### 4. 情绪日记模块

#### 4.1 创建情绪记录
```
POST /api/mood/journal
```

**请求参数:**
```javascript
{
  "recordDate": "2025-07-06",
  "mood": {
    "primary": "焦虑",
    "intensity": 7,
    "triggers": ["考试压力"]
  },
  "note": "今天的心情记录",
  "activities": [
    {
      "name": "完成作业",
      "impact": "positive",
      "duration": 60
    }
  ]
}
```

#### 4.2 获取情绪记录
```
GET /api/mood/journal?startDate=2025-07-01&endDate=2025-07-07
```

### 5. 消息推送模块

#### 5.1 订阅消息授权
```
POST /api/notification/subscribe
```

**请求参数:**
```javascript
{
  "templateId": "template_123",
  "scene": "task_reminder"
}
```

#### 5.2 发送提醒消息
```
POST /api/notification/send
```

**请求参数:**
```javascript
{
  "userId": "user_123",
  "templateId": "template_123",
  "data": {
    "task": "复习数学",
    "time": "20:00"
  }
}
```

### 6. 成就系统模块

#### 6.1 获取用户成就
```
GET /api/achievements?type=all&page=1&limit=10
```

#### 6.2 生成成就卡片
```
POST /api/achievements/card
```

**请求参数:**
```javascript
{
  "achievementId": "achievement_123",
  "shareText": "自定义分享文案"
}
```

## 云函数接口实现

### 函数结构
```
cloudfunctions/
├── auth/              # 用户认证相关
│   └── index.js
├── anxiety/           # 焦虑倾诉相关
│   └── index.js
├── tasks/             # 任务管理相关
│   └── index.js
├── mood/              # 情绪日记相关
│   └── index.js
├── notification/      # 消息推送相关
│   └── index.js
└── achievements/      # 成就系统相关
    └── index.js
```

### 云函数示例 (anxiety/index.js)
```javascript
const cloud = require('wx-server-sdk');
const { callAIService } = require('../common/ai-client');

cloud.init();
const db = cloud.database();

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'submit':
        return await submitAnxiety(data, context);
      case 'getAnalysis':
        return await getAnalysis(data, context);
      default:
        return { code: 1001, message: '不支持的操作' };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return { code: 5001, message: '系统内部错误' };
  }
};

async function submitAnxiety(data, context) {
  const { OPENID } = cloud.getWXContext();
  
  // 保存焦虑记录
  const record = await db.collection('anxietyRecords').add({
    data: {
      userId: OPENID,
      input: {
        type: data.type,
        content: data.content,
        voiceUrl: data.voiceUrl
      },
      status: 'processing',
      createdAt: new Date()
    }
  });
  
  // 异步调用AI分析
  processAIAnalysis(record._id, data.content);
  
  return {
    code: 0,
    message: '提交成功',
    data: {
      recordId: record._id,
      status: 'processing'
    }
  };
}
```

## 接口安全设计

### 1. 身份认证
- 基于微信小程序的openid进行用户识别
- 使用JWT token进行会话管理
- 云函数自动获取用户上下文

### 2. 权限控制
- 用户只能访问自己的数据
- 管理员接口需要特殊权限验证
- 敏感操作需要二次确认

### 3. 数据验证
- 输入参数严格验证
- SQL注入防护
- XSS攻击防护

### 4. 限流控制
- API调用频率限制
- 防止恶意请求
- 资源使用监控
